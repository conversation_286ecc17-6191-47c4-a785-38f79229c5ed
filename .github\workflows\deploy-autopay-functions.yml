name: AutoPay Functions Deploy

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: Select the environment
        required: true

env:
  NUGET_PACKAGES_DIRECTORY: ".nuget"
  BRANCH: "${{ github.ref }}"
  GET_DUE_FUNCTION_APP_NAME: "func-get-due-${{ vars.STAGE }}"
  PROCESS_DUE_FUNCTION_APP_NAME: "func-process-due-${{ vars.STAGE }}"
  PACKAGE_REGISTRY_USERNAME: ${{ secrets.PACKAGE_REGISTRY_USERNAME }}
  PACKAGE_REGISTRY_PASSWORD: ${{ secrets.PACKAGE_REGISTRY_PASSWORD }}

jobs:
  deploy-get-due:
    environment: ${{ github.event.inputs.environment }}
    name: "Deploying GetDue Function to ${{ github.event.inputs.environment }}"
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 0

      - name: Log in to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - run: sudo apt-get update
      - run: sudo apt-get -y install zip
      - run: dotnet tool restore
      
      - run: dotnet publish --configuration "Release" BlueTape.Services.LMS/Functions/AutoPay/BlueTape.Functions.LMS.GetDue/BlueTape.Functions.LMS.GetDue.csproj --runtime "linux-x64" --no-self-contained -o get-due-functions
      - run: (cd get-due-functions && zip -r ../get-due-functions-${{ github.run_id }}.zip .)

      - name: 'Deploy GetDue Function'
        uses: Azure/functions-action@v1.4.7
        with:
         app-name: "${{ env.GET_DUE_FUNCTION_APP_NAME }}"
         package: "get-due-functions-${{ github.run_id }}.zip"
         publish-profile: ${{ secrets.AZURE_GETDUE_FUNCTIONAPP_PUBLISH_PROFILE }}

  deploy-process-due:
    environment: ${{ github.event.inputs.environment }}
    name: "Deploying ProcessDue Function to ${{ github.event.inputs.environment }}"
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 0

      - name: Log in to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - run: sudo apt-get update
      - run: sudo apt-get -y install zip
      - run: dotnet tool restore
      
      - run: dotnet publish --configuration "Release" BlueTape.Services.LMS/Functions/AutoPay/BlueTape.Functions.LMS.ProcessDue/BlueTape.Functions.LMS.ProcessDue.csproj --runtime "linux-x64" --no-self-contained -o process-due-functions
      - run: (cd process-due-functions && zip -r ../process-due-functions-${{ github.run_id }}.zip .)

      - name: 'Deploy ProcessDue Function'
        uses: Azure/functions-action@v1.4.7
        with:
         app-name: "${{ env.PROCESS_DUE_FUNCTION_APP_NAME }}"
         package: "process-due-functions-${{ github.run_id }}.zip"
         publish-profile: ${{ secrets.AZURE_PROCESSDUE_FUNCTIONAPP_PUBLISH_PROFILE }} 