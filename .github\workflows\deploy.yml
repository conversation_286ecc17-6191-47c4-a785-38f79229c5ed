name: Deploy

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: Select the environment
        required: true

env:
  NUGET_PACKAGES_DIRECTORY: ".nuget"
  PATH_TO_SLN: "./BlueTape.Services.LMS"
  BRANCH: "${{ github.ref }}"
  PACKA<PERSON>_REGISTRY_USERNAME: ${{ secrets.PACKAGE_REGISTRY_USERNAME }}
  PACKAGE_REGISTRY_PASSWORD: ${{ secrets.PACKAGE_REGISTRY_PASSWORD }}
  OVERDUE_DETECTOR_FUNCTION_NAME: "lms-overdue-detector-${{ vars.STAGE }}"
  
jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 0

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: 8.0.x

      - name: Restore dependencies
        run: dotnet restore $PATH_TO_SLN --packages $NUGET_PACKAGES_DIRECTORY -f
      - name: Build
        run: dotnet build --no-restore --configuration Release $PATH_TO_SLN
      - name: Test
        run: dotnet test $PATH_TO_SLN --no-build --no-restore --configuration Release --verbosity normal

  deploy:
    needs: build
    environment: ${{ github.event.inputs.environment }}
    name: "Deploying ${{ github.event.inputs.environment }} environment"
    runs-on: ubuntu-latest

    env:
      ENV_NAME: ${{ vars.STAGE }}
      APP_SOURCE_PATH: BlueTape.Services.LMS
      LOGIN_SERVER: containerregistrybt${{ vars.STAGE }}.azurecr.io
      IMAGE_NAME: loan-management-service:latest
    
    steps:
      - uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 0

      - name: Log in to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: 'Build and push image'
        uses: azure/docker-login@v1
        with:
          login-server: containerregistrybt${{ vars.STAGE }}.azurecr.io
          username: ${{ secrets.AZURE_CLIENT_ID }}
          password: ${{ secrets.AZURE_CLIENT_SECRET }}
      - run: |
          docker build \
          --build-arg ASPNETCORE_ENVIRONMENT=${ENV_NAME} \
          --build-arg PACKAGE_REGISTRY_USERNAME=${PACKAGE_REGISTRY_USERNAME} \
          --build-arg PACKAGE_REGISTRY_PASSWORD=${PACKAGE_REGISTRY_PASSWORD} \
          -t $LOGIN_SERVER/$IMAGE_NAME \
          $APP_SOURCE_PATH 
          docker push $LOGIN_SERVER/$IMAGE_NAME

      - name: Build and deploy Container App
        uses: azure/container-apps-deploy-action@v2
        with:
          acrName: containerregistrybt${{ vars.STAGE }}
          buildArguments: ASPNETCORE_ENVIRONMENT=${{env.ENV_NAME}} PACKAGE_REGISTRY_USERNAME=${{env.PACKAGE_REGISTRY_USERNAME}} PACKAGE_REGISTRY_PASSWORD=${{env.PACKAGE_REGISTRY_PASSWORD}}
          appSourcePath: ${{env.APP_SOURCE_PATH}}
          acrUsername: ${{ secrets.AZURE_ACR_USERNAME }}
          acrPassword: ${{ secrets.AZURE_ACR_PASSWORD }}
          containerAppName: ca-loan-management-service-${{ vars.STAGE }}
          containerAppEnvironment: container-app-environment-${{ vars.STAGE }}
          resourceGroup: ${{ vars.STAGE }}
