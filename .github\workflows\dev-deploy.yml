name: Dev Deploy

on: workflow_dispatch
  
env:
 <PERSON><PERSON><PERSON><PERSON>_REGISTRY_USERNAME: ${{ secrets.PACKAGE_REGISTRY_USERNAME }}
 PACKAGE_REGISTRY_PASSWORD: ${{ secrets.PACKAGE_REGISTRY_PASSWORD }}
 OBJECTS_DIRECTORY: obj
 NUGET_PACKAGES_DIRECTORY: ".nuget"
 SOURCE_CODE_PATH: BlueTape.Services.LMS/**/*
 PATH_TO_SLN: "./BlueTape.Services.LMS"
 GIT_DEPTH: '0'
 LP_AWS_ACCOUNT: "${{secrets.AWS_ACCOUNT_ID}}"
 AWS_REGION: us-west-1
 BRANCH: "${{ github.ref }}"
 LOGZIO_TOKEN: "${{secrets.LOGZIO_TOKEN}}"
 AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }} 
 AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
  
jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4.1.1
      with:
          fetch-depth: 0
          
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 6.0.x  

    - name: Restore dependencies
      run: dotnet restore $PATH_TO_SLN --packages $NUGET_PACKAGES_DIRECTORY -f
    - name: Build
      run: dotnet build --no-restore --configuration Release $PATH_TO_SLN
    - name: Test
      run: dotnet test $PATH_TO_SLN --no-build --no-restore --configuration Release --verbosity normal

  deploy:
   needs: build
   environment: dev
   runs-on: ubuntu-latest
   container:
     image: mcr.microsoft.com/dotnet/sdk:6.0

   env:
    STAGE: ${{ vars.STAGE }}
    ASPNETCORE_ENVIRONMENT: ${{ vars.STAGE }}
    LP_AWS_ACCOUNT: "${{secrets.AWS_ACCOUNT_ID}}"
    CONNECTION_STRING: "${{secrets.CONNECTION_STRING}}"
    NET-SOFR-API-URL: ${{ vars.NET-SOFR-API-URL }}
    API_KEY: ${{ secrets.API_KEY }}
    LAMBDA_PACKAGE_LOCATION: ../scripts/deploy/${{ github.run_id }}.zip
    OVERDUE_DETECTOR_LAMBDA_PACKAGE_LOCATION: ../scripts/deploy/overdue-${{ github.run_id }}.zip
    LOAN_PARAMETERS_LAMBDA_PACKAGE_LOCATION: ../scripts/deploy/loan-parameters-${{ github.run_id }}.zip
    PENALTY_DETECTOR_LAMBDA_PACKAGE_LOCATION: ../scripts/deploy/penalty-${{ github.run_id }}.zip
   steps:
    - uses: actions/checkout@v4.1.1
      with:
          fetch-depth: 0
          
    - run: apt-get update
    - run: apt-get -y install zip
    - run: dotnet tool restore
    - run: dotnet lambda package -pl BlueTape.Services.LMS/BlueTape.Services.LMS.API -o ./scripts/deploy/${{ github.run_id }}.zip
    - run: dotnet lambda package -pl BlueTape.Services.LMS/BlueTape.Services.LMS.Lambda.OverDueDetector -o ./scripts/deploy/overdue-${{ github.run_id }}.zip
    - run: dotnet lambda package -pl BlueTape.Services.LMS/BlueTape.Services.LMS.Lambda.PenaltyDetector -o ./scripts/deploy/penalty-${{ github.run_id }}.zip
    - run: dotnet lambda package -pl BlueTape.Services.LMS/BlueTape.Services.LMS.Lambda.LoanParametersCreation -o ./scripts/deploy/loan-parameters-${{ github.run_id }}.zip
    - name: Use latest Node.js
      uses: actions/setup-node@v3.8.1
      with:
        node-version: latest

    - run: npm config set prefix /usr/local
    - run: npm i -g serverless

    - name: Setup serverless-dotenv-plugin
      run: serverless plugin install -n serverless-dotenv-plugin
      working-directory: ./BlueTape.Services.LMS

    - name: Setup serverless-offline
      run: serverless plugin install -n serverless-offline
      working-directory: ./BlueTape.Services.LMS

    - name: Setup serverless-plugin-warmup
      run: serverless plugin install -n serverless-plugin-warmup
      working-directory: ./BlueTape.Services.LMS

    - name: Setup serverless-prune-plugin
      run: serverless plugin install -n serverless-prune-plugin
      working-directory: ./BlueTape.Services.LMS
    
    - name: Setup serverless-domain-manager
      run: serverless plugin install -n serverless-domain-manager
      working-directory: ./BlueTape.Services.LMS

    - name: Deploy
      run: serverless deploy --stage $STAGE --verbose
      working-directory: ./BlueTape.Services.LMS
