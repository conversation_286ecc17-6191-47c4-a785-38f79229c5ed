﻿using BlueTape.DataAccess.Mongo.Documents;

namespace BlueTape.DataAccess.Mongo.Abstractions;
public interface ILoanApplicationRepository : IGenericRepository<LoanApplicationDocument>
{
    Task<List<LoanApplicationDocument>> GetApprovedLoanApplicationsByCompanyId(string companyId, CancellationToken ct);

    Task<List<LoanApplicationDocument>> GetRejectedLoanApplications(CancellationToken ct);

    Task<List<LoanApplicationDocument>> GetApprovedLoanApplications(CancellationToken ct);

    Task<LoanApplicationDocument?> GetByLmsId(string lmsId, CancellationToken ct);

    Task<IReadOnlyCollection<LoanApplicationDocument>> GetByLmsIds(IReadOnlyCollection<string> lmsId, CancellationToken ct);

    Task<IReadOnlyCollection<LightLoanApplicationDocument>> GetLightByLmsIds(IReadOnlyCollection<string> lmsId, CancellationToken ct);
}
