﻿using BlueTape.DataAccess.Mongo.Documents;

namespace BlueTape.DataAccess.Mongo.Abstractions
{
    public interface ITransactionRepository : IGenericRepository<TransactionDocument>
    {
        Task<List<TransactionDocument>> GetCardTransactionsWithProcessingOperations(DateTime thresholdDate, CancellationToken cancellationToken);
        Task<TransactionDocument> GetCardTransactionWithProcessingOperationByTransactionId(string transactionId, DateTime thresholdDate, CancellationToken cancellationToken);
    }
}
