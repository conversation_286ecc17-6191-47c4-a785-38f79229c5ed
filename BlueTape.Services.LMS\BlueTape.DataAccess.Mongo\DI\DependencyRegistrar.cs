﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.DataAccess.Mongo.DI
{
    public static class DependencyRegistrar
    {
        public static void AddMongoDataAccessDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddSingleton<ILmsMongoDBContext, LmsMongoDBContext>();
            services.AddTransient<ILoanApplicationRepository, LoanApplicationRepository>();
            services.AddTransient<IUserRepository, UserRepository>();
            services.AddTransient<IUserRoleRepository, UserRoleRepository>();
            services.AddTransient<ILoanPricingPackageRepository, LoanPricingPackageRepository>();
            services.AddTransient<ICardPricingPackageRepository, CardPricingPackageRepository>();
            services.AddTransient<IOperationRepository, OperationRepository>();
            services.AddTransient<ICustomerAccountRepository, CustomerAccountRepository>();
            services.AddTransient<ITransactionRepository, TransactionRepository>();
        }
    }
}
