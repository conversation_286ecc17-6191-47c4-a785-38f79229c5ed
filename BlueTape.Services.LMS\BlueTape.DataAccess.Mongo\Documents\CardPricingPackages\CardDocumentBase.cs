﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents.CardPricingPackages;

[BsonIgnoreExtraElements]
public class CardDocumentBase
{
    [BsonElement("merchant")]
    public MerchantDocument Merchant { get; set; } = new();

    [BsonElement("customer")]
    public CustomerDocument Customer { get; set; } = new();

    [BsonElement("tabapayBin")]
    public string TabapayBin { get; set; } = string.Empty;
}
