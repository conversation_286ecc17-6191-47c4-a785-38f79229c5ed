﻿using BlueTape.MongoDB.Attributes;
using BlueTape.MongoDB.DTO;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents.CardPricingPackages;

[BsonIgnoreExtraElements]
[MongoCollection("cardpricingpackages")]
public class CardPricingPackageDocument : Document
{
    [BsonElement("name")]
    public string Name { get; set; } = string.Empty;

    [BsonElement("title")]
    public string Title { get; set; } = string.Empty;

    [BsonElement("description")]
    public string Description { get; set; } = string.Empty;

    [BsonElement("tabapayMID")]
    public string TabapayMID { get; set; } = string.Empty;

    [BsonElement("metadata")]
    public MetadataDocument Metadata { get; set; } = new();

    [BsonElement("deactivated")]
    public bool Deactivated { get; set; }

    [BsonElement("createdBy")]
    public string? CreatedBy { get; set; }

    [BsonElement("updatedBy")]
    public string? UpdatedBy { get; set; }

    [BsonElement("isHidden")]
    public bool? IsHidden { get; set; }
}

