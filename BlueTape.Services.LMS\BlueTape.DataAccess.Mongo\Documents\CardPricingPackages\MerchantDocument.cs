﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents.CardPricingPackages;

[BsonIgnoreExtraElements]
public class MerchantDocument
{
    [BsonElement("percentage")]
    public double Percentage { get; set; }

    [BsonElement("min")]
    public double? Min { get; set; }

    [BsonElement("max")]
    public double? Max { get; set; }

    [BsonElement("amount")]
    public double Amount { get; set; }
}
