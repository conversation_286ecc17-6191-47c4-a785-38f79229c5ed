﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents.CardPricingPackages;

[BsonIgnoreExtraElements]
public class MetadataDocument
{
    [BsonElement("ach")]
    public AchDocument? Ach { get; set; }

    [BsonElement("debitCardRegulated")]
    public DebitCardRegulatedDocument? DebitCardRegulated { get; set; }

    [BsonElement("debitCardUnregulated")]
    public DebitCardUnregulatedDocument? DebitCardUnregulated { get; set; }

    [BsonElement("creditCardVisa")]
    public CreditCardVisaDocument? CreditCardVisa { get; set; }

    [BsonElement("creditCardMasterCard")]
    public CreditCardMasterCardDocument? CreditCardMasterCard { get; set; }

    [BsonElement("creditCardDiscover")]
    public CreditCardDiscoverDocument? CreditCardDiscover { get; set; }

    [BsonElement("creditCardVisa2")]
    public CreditCardVisa2Document? CreditCardVisa2 { get; set; }

    [BsonElement("creditCardMasterCard2")]
    public CreditCardMasterCard2Document? CreditCardMasterCard2 { get; set; }

    [BsonElement("creditCardDiscover2")]
    public CreditCardDiscover2Document? CreditCardDiscover2 { get; set; }

    [BsonElement("creditCardTravel")]
    public CreditCardTravelDocument? CreditCardTravel { get; set; }

    [BsonElement("creditCardBusiness")]
    public CreditCardBusinessDocument? CreditCardBusiness { get; set; }

    [BsonElement("amex")]
    public AmexDocument? Amex { get; set; }
}