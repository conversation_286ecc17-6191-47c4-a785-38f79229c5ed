﻿using BlueTape.MongoDB.Attributes;
using BlueTape.MongoDB.DTO;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents.CustomerAccounts;

[BsonIgnoreExtraElements]
[MongoCollection("customeraccounts")]
public class CustomerAccountDocument : Document
{
    [BsonElement("company_id")]
    public string CompanyId { get; set; } = null!;

    [BsonElement("settings")]
    public CustomerAccountSettings? Settings { get; set; }
}
