﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson;
using BlueTape.MongoDB.DTO;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
[MongoCollection("loanapplications")]
public class LightLoanApplicationDocument : Document
{
    [BsonElement("company_id")]
    public string? CompanyId { get; set; }

    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("lms_id")]
    public string? LmsId { get; set; }

    [BsonElement("metadata")]
    public LoanApplicationMetadataDocument? Metadata { get; set; }
}