﻿using BlueTape.MongoDB.Attributes;
using BlueTape.MongoDB.DTO;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
[MongoCollection("loanapplications")]
public class LoanApplicationDocument : Document
{
    [BsonElement("company_id")]
    public string? CompanyId { get; set; }

    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("status1")]
    public string? Status1 { get; set; }

    [BsonElement("amountDue")]
    public decimal? AmountDue { get; set; }

    [BsonElement("draft")]
    public Dictionary<string, object>? Draft { get; set; }

    [BsonElement("progress")]
    public BsonDocument? Progress { get; set; }

    [BsonElement("executionArn")]
    public string? ExecutionArn { get; set; }

    [BsonElement("creditApplicationId")]
    public string? CreditApplicationId { get; set; }

    [BsonElement("drawApprovalId")]
    public string? DrawApprovalId { get; set; }

    [BsonElement("submitDate")]
    public DateTime? SubmitDate { get; set; }

    [BsonElement("decisionDate")]
    public DateTime? DecisionDate { get; set; }

    [BsonElement("approvedBy")]
    public string? ApprovedBy { get; set; }

    [BsonElement("approvedAmount")]
    public decimal? ApprovedAmount { get; set; }

    [BsonElement("usedAmount")]
    public decimal? UsedAmount { get; set; }

    [BsonElement("lms_id")]
    public string? LmsId { get; set; }

    [BsonElement("isSentBack")]
    public bool? IsSentBack { get; set; }

    [BsonElement("notes")]
    public IEnumerable<LoanApplicationNotesDocument>? Notes { get; set; }

    [BsonElement("invoiceDetails")]
    public InvoiceDetails? InvoiceDetails { get; set; }

    [BsonElement("metadata")]
    public LoanApplicationMetadataDocument? Metadata { get; set; }
}

