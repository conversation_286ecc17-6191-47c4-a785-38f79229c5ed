﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
public class LoanApplicationNotesDocument
{
    [BsonElement("message")]
    public string? Message { get; set; }

    [BsonElement("name")]
    public string? Name { get; set; }

    [BsonElement("login")]
    public string? Login { get; set; }

    [BsonElement("createdAt")]
    public DateTime? CreatedAt { get; set; }
}
