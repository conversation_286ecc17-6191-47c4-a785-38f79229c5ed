using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
public class LoanPaymentPlanDocument
{
    [BsonElement("name")] public string? Name { get; set; }

    [BsonElement("lmsTemplateId")] public string? LmsTemplateId { get; set; }

    [BsonElement("frequency")] public string? Frequency { get; set; }

    [BsonElement("type")] public string? Type { get; set; }

    [BsonElement("days")] public int Days { get; set; }

    [BsonElement("fee")] public double Fee { get; set; }

    [BsonElement("term")] public int Term { get; set; }

    [BsonElement("firstPaymentDelayDays")] public int FirstPaymentDelayDays { get; set; }
}