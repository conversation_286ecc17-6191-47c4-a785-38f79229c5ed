﻿using BlueTape.MongoDB.Attributes;
using BlueTape.MongoDB.DTO;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;


[BsonIgnoreExtraElements]
[MongoCollection("loanpricingpackages")]
public class LoanPricingPackageDocument : Document
{
    [BsonElement("name")]
    public string? Name { get; set; }

    [BsonElement("title")]
    public string? Title { get; set; }

    [BsonElement("description")]
    public string? Description { get; set; }

    [BsonElement("code")]
    public string? Code { get; set; }

    [BsonElement("product")]
    public string Product { get; set; } = string.Empty;

    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("order")]
    public int? Order { get; set; }

    [BsonElement("individual")]
    public bool Individual { get; set; }

    [BsonElement("createdBy")]
    public string? CreatedBy { get; set; }

    [BsonElement("updatedBy")]
    public string? UpdatedBy { get; set; }

    [BsonElement("metadata")]
    public LoanPricingPackageMetadataDocument? Metadata { get; set; }
}
