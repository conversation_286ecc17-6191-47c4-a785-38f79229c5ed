﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
public class LoanPricingPackageMetadataDocument
{
    [BsonElement("advanceRate")]
    public int AdvanceRate { get; set; }

    [BsonElement("merchant")]
    public double Merchant { get; set; }

    [BsonElement("maxAmountReceived")]
    public double MaxAmountReceived { get; set; }

    [BsonElement("finalPayment")]
    public int FinalPayment { get; set; }

    [BsonElement("merchantRebate")]
    public double MerchantRebate { get; set; }

    [BsonElement("merchantFeeAfterRebate")]
    public double MerchantFeeAfterRebate { get; set; }

    [BsonElement("maxAmountReceivedAfterRebate")]
    public double MaxAmountReceivedAfterRebate { get; set; }

    [BsonElement("CustomerFees30")]
    public int CustomerFees30 { get; set; }

    [BsonElement("CustomerFees6090")]
    public string CustomerFees6090 { get; set; } = string.Empty;

    [BsonElement("advancedPaymentDay")]
    public string AdvancedPaymentDay { get; set; } = string.Empty;

    [BsonElement("finalPaymentDay")]
    public string? FinalPaymentDay { get; set; }
}
