﻿using BlueTape.MongoDB.Attributes;
using BlueTape.MongoDB.DTO;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
[MongoCollection("operations")]
public class OperationDocument : Document
{
    [BsonElement("owner_id")]
    public string OwnerId { get; set; } = string.Empty;

    [BsonElement("firstTransactionDate")]
    public DateTime? FirstTransactionDate { get; set; }

    [BsonElement("date")]
    public DateTime? Date { get; set; }

    [BsonElement("status")]
    public string Status { get; set; } = string.Empty;

    [BsonElement("type")]
    public string Type { get; set; } = string.Empty;

    [BsonElement("amount")]
    [BsonRepresentation(BsonType.Double)]
    public decimal Amount { get; set; }

    [BsonElement("metadata")]
    public OperationMetadataDocument Metadata { get; set; } = new();
}