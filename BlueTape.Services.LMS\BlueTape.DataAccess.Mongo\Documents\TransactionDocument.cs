﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using BlueTape.MongoDB.DTO;
using BlueTape.MongoDB.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents
{
    [BsonIgnoreExtraElements]
    [MongoCollection("transactions")]
    public class TransactionDocument : Document
    {
        [BsonElement("operation_id")]
        public string OperationId { get; set; } = null!;

        public OperationDocument? Operation { get; set; }

        [BsonElement("type")]
        [BsonIgnoreIfDefault]
        public string Type { get; set; } = null!;

        [BsonElement("payer_id")]
        [BsonIgnoreIfDefault]
        public string? PayerId { get; set; } = null!;

        [BsonElement("payee_id")]
        [BsonIgnoreIfDefault]
        public string? PayeeId { get; set; } = null!;

        [BsonRepresentation(BsonType.Double)]
        [BsonElement("amount")]
        public decimal Amount { get; set; }

        [BsonElement("currency")]
        public string Currency { get; set; } = string.Empty;

        [BsonElement("status")]
        public string Status { get; set; } = null!;

        [BsonRepresentation(BsonType.Double)]
        [BsonElement("fee")]
        public decimal Fee { get; set; }

        [BsonElement("payment_method")]
        public string PaymentMethod { get; set; } = string.Empty;

        [BsonElement("reason")]
        [BsonIgnoreIfDefault]
        public string? Reason { get; set; }

        [BsonElement("date")]
        [BsonIgnoreIfDefault]
        public DateTime? Date { get; set; }

        [BsonElement("createdBy")]
        [BsonIgnoreIfDefault]
        [BsonIgnoreIfNull]
        public string? CreatedBy { get; set; }

        [BsonElement("updatedBy")]
        [BsonIgnoreIfDefault]
        public string? UpdatedBy { get; set; }

        [BsonElement("payment_provider")]
        [BsonIgnoreIfDefault]
        public string? Provider { get; set; }

        [BsonElement("paymentRequestId")]
        [BsonIgnoreIfDefault]
        [BsonIgnoreIfNull]
        public string? PaymentRequestId { get; set; }

        [BsonElement("paymentTransactionId")]
        public string? PaymentTransactionId { get; set; }
    }
}
