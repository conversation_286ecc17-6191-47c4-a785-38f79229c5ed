﻿using BlueTape.MongoDB.Attributes;
using BlueTape.MongoDB.DTO;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
[MongoCollection("users")]
public class UserDto : Document
{
    [BsonElement("sub")]
    public string Sub { get; set; } = null!;

    [BsonElement("name")]
    public string? Name { get; set; }

    [BsonElement("login")]
    public string? Login { get; set; }

    [BsonElement("phone")]
    public string? Phone { get; set; }

    [BsonElement("email")]
    public string? Email { get; set; }

    [BsonElement("lastName")]
    public string? LastName { get; set; }

    [BsonElement("firstName")]
    public string? FirstName { get; set; }

    [BsonElement("hubspotId")]
    public long? HubSpotId { get; set; }

    [BsonElement("hubspotLastSyncDate")]
    public DateTime? HubSpotLastSyncDate { get; set; }
}