using BlueTape.MongoDB.Attributes;
using BlueTape.MongoDB.DTO;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
[MongoCollection("userroles")]
public class UserRoleDocument : Document
{
    [BsonElement("sub")]
    public string Sub { get; set; } = string.Empty;

    [BsonElement("role")]
    public string Role { get; set; } = string.Empty;

    [BsonElement("status")]
    public string Status { get; set; } = null!;

    [BsonElement("company_id")]
    public string? CompanyId { get; set; }

    [BsonElement("settings")]
    public UserRoleSettingsDocument? Settings { get; set; }
}

[BsonIgnoreExtraElements]
public class UserRoleSettingsDocument
{
    [BsonElement("paymentEmailNotificationsEnabled")]
    public bool PaymentEmailNotificationsEnabled { get; set; }
}