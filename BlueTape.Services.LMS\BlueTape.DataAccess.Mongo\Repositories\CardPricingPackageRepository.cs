﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Documents.CardPricingPackages;
using BlueTape.DataAccess.Mongo.Documents.Filters;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;

namespace BlueTape.DataAccess.Mongo.Repositories;

public class CardPricingPackageRepository(ILmsMongoDBContext context, ILogger<GenericRepository<CardPricingPackageDocument>> logger)
    : GenericRepository<CardPricingPackageDocument>(context, logger), ICardPricingPackageRepository
{
    public async Task<List<CardPricingPackageDocument>> GetByFilter(CardPricingPackagesFilter query,
        CancellationToken ct)
    {
        var filter = BuildFilterDefinition(query);

        return await Collection.Find(filter).ToListAsync(ct);
    }

    private static FilterDefinition<CardPricingPackageDocument> BuildFilterDefinition(CardPricingPackagesFilter query)
    {
        var expression = Builders<CardPricingPackageDocument>.Filter;
        var filter = expression.Empty;
        filter &= expression.Eq(x => x.Deactivated, false);

        if (!string.IsNullOrEmpty(query.Id)) filter &= expression.Eq(x => x.Id, query.Id);
        if (!string.IsNullOrEmpty(query.Name)) filter &= expression.Regex(x => x.Name, new BsonRegularExpression(".*" + query.Name, "i"));
        if (query.IsHidden.HasValue && (bool)query.IsHidden) filter &= expression.Eq(x => x.IsHidden, true);
        if (query.IsHidden.HasValue && !(bool)query.IsHidden) filter &= expression.Ne(x => x.IsHidden, true);

        return filter;
    }

}
