﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Documents.CustomerAccounts;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.DataAccess.Mongo.Repositories;

public class CustomerAccountRepository : GenericRepository<CustomerAccountDocument>, ICustomerAccountRepository
{
    public CustomerAccountRepository(ILmsMongoDBContext context, ILogger<CustomerAccountRepository> logger) : base(context, logger)
    {
    }

    public async Task<IEnumerable<CustomerAccountDocument>> GetByIds(string[] ids, CancellationToken cancellationToken)
    {
        var expression = Builders<CustomerAccountDocument>.Filter.Where(x => ids.Contains(x.Id));

        return await Collection.Find(expression).ToListAsync(cancellationToken);
    }
}
