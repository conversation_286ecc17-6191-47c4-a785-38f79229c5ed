﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.MongoDB.DTO;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System.Linq.Expressions;

namespace BlueTape.DataAccess.Mongo.Repositories
{
    public class GenericRepository<TDocument> : IGenericRepository<TDocument> where TDocument : Document
    {
        protected readonly ILogger Logger;
        protected readonly IMongoCollection<TDocument> Collection;

        protected GenericRepository(
            ILmsMongoDBContext context,
            ILogger<GenericRepository<TDocument>> logger)
        {
            Logger = logger;
            Collection = context.GetCollection<TDocument>();
        }

        public async Task<TDocument> Add(TDocument entity, CancellationToken ct)
        {
            Logger.LogInformation("Add new {type} document", typeof(TDocument).ToString());

            entity.CreatedAt = DateTime.UtcNow;

            await Collection.InsertOneAsync(entity, cancellationToken: ct);
            return entity;
        }

        public async Task<IEnumerable<TDocument>> GetAll(CancellationToken ct)
        {
            Logger.LogInformation("Get all {type} documents", typeof(TDocument).ToString());
            return await Collection.Find((x) => true).ToListAsync(ct);
        }

        public async Task<IEnumerable<TDocument>> GetAll(Expression<Func<TDocument, bool>> predicate,
            CancellationToken ct)
        {
            Logger.LogInformation("Get by expression {type} documents", typeof(TDocument).ToString());
            var documents = Collection.AsQueryable().Where(predicate);
            return await documents.ToListAsync(ct);
        }

        public async Task<TDocument> GetById(string id, CancellationToken ct)
        {
            Logger.LogInformation("Get by id {type} documents, id: {id}", typeof(TDocument).ToString(), id);

            var result = await Collection.Find(x => x.Id == id).FirstOrDefaultAsync(ct);

            if (result is not null) return result;

            Logger.LogError("Entity type of {type} with {id} not found", typeof(TDocument).ToString(), id);
            throw new VariableNullException($"Document with id {id} not found");
        }

        public virtual async Task<TDocument> Update(TDocument entity, CancellationToken ct)
        {
            Logger.LogInformation("Update {type} document, id: {id}", typeof(TDocument).ToString(), entity.Id);
            var entityFromMongo = await GetById(entity.Id, ct);
            entity.CreatedAt = entityFromMongo.CreatedAt;
            entity.UpdatedAt = DateTime.Now;
            await Collection.ReplaceOneAsync(x => x.Id == entity.Id, entity, cancellationToken: ct);
            return entity;
        }
    }
}