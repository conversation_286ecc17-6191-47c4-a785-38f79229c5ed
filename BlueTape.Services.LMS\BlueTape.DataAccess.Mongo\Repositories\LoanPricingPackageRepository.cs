﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.DataAccess.Mongo.Documents.Filters;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;

namespace BlueTape.DataAccess.Mongo.Repositories;

public class LoanPricingPackageRepository(
    ILmsMongoDBContext context,
    ILogger<GenericRepository<LoanPricingPackageDocument>> logger)
    : GenericRepository<LoanPricingPackageDocument>(context, logger), ILoanPricingPackageRepository
{
    public async Task<List<LoanPricingPackageDocument>> GetByFilter(LoanPricingPackagesFilter query,
        CancellationToken ct)
    {
        var filter = BuildFilterDefinition(query);

        return await Collection.Find(filter).ToListAsync(ct);
    }

    private static FilterDefinition<LoanPricingPackageDocument> BuildFilterDefinition(LoanPricingPackagesFilter query)
    {
        var expression = Builders<LoanPricingPackageDocument>.Filter;
        var filter = expression.Empty;
        filter &= expression.Where(x => x.Status == "active" || x.Status == null);

        if (!string.IsNullOrEmpty(query.Id)) filter &= expression.Eq(x => x.Id, query.Id);
        if (!string.IsNullOrEmpty(query.Code))
            filter &= expression.Regex(x => x.Code, new BsonRegularExpression(".*" + query.Code, "i"));
        if (!string.IsNullOrEmpty(query.Name))
            filter &= expression.Regex(x => x.Name, new BsonRegularExpression(".*" + query.Name, "i"));
        if (query.Product.HasValue)
            filter &= expression.Regex(x => x.Product, new BsonRegularExpression(".*" + query.Product, "i"));

        return filter;
    }
}