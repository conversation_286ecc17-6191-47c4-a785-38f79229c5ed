﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Documents;
using Microsoft.Extensions.Logging;

namespace BlueTape.DataAccess.Mongo.Repositories;

public class OperationRepository : GenericRepository<OperationDocument>, IOperationRepository
{
    public OperationRepository(ILmsMongoDBContext context, ILogger<OperationRepository> logger) : base(context, logger)
    {
    }
}
