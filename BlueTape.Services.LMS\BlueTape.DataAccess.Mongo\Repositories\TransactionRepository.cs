﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Documents;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;

namespace BlueTape.DataAccess.Mongo.Repositories
{
    public class TransactionRepository(ILmsMongoDBContext context, ILogger<TransactionRepository> logger)
    : GenericRepository<TransactionDocument>(context, logger), ITransactionRepository
    {
        public async Task<List<TransactionDocument>> GetCardTransactionsWithProcessingOperations(DateTime thresholdDate,
            CancellationToken ct)
        {
            var pipeline = new[]
            {
                new BsonDocument("$match", new BsonDocument
                {
                    { "status", "SUCCESS" },
                    { "payment_method", "card" }
                }),
                new BsonDocument("$lookup", new BsonDocument
                {
                    { "from", "operations" },
                    { "let", new BsonDocument
                    {
                        { "operation_id_str", "$operation_id" },
                        { "threshold", thresholdDate }
                    }},
                    { "pipeline", new BsonArray
                    {
                        new BsonDocument("$match", new BsonDocument("$expr",
                            new BsonDocument("$and", new BsonArray
                            {
                                new BsonDocument("$eq", new BsonArray { "$_id", new BsonDocument("$toObjectId", "$$operation_id_str") }),
                                new BsonDocument("$eq", new BsonArray { "$status", "PROCESSING" }),
                                new BsonDocument("$lt", new BsonArray { "$date", "$$threshold" })
                            })
                        ))
                    }},
                    { "as", "operation_data" }
                }),
                new BsonDocument("$match", new BsonDocument
                {
                    { "operation_data", new BsonDocument("$ne", new BsonArray()) }
                }),
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "Operation", new BsonDocument("$arrayElemAt", new BsonArray { "$operation_data", 0 }) }
                }),
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "amount", new BsonDocument("$convert", new BsonDocument
                    {
                        { "input", "$amount" },
                        { "to", "decimal" },
                        { "onError", "$amount" }
                    })},
                    { "Operation.amount", new BsonDocument("$convert", new BsonDocument
                    {
                        { "input", "$Operation.amount" },
                        { "to", "decimal" },
                        { "onError", "$Operation.amount" }
                    })}
                }),
                new BsonDocument("$sort", new BsonDocument("date", -1)),
                new BsonDocument("$project", new BsonDocument
                {
                    { "_id", 1 },
                    { "status", 1 },
                    { "payment_method", 1 },
                    { "operation_id", 1 },
                    { "amount", 1 },
                    { "Operation", 1 },
                    { "date", 1 },
                    { "updated_at", 1 },
                    { "payer_id", 1 }
                })
            };

            var results = await Collection
                .Aggregate<TransactionDocument>(pipeline)
                .ToListAsync(ct);

            return results;
        }

        public async Task<TransactionDocument> GetCardTransactionWithProcessingOperationByTransactionId(
            string transactionId,
            DateTime thresholdDate,
            CancellationToken cancellationToken)
        {
            var pipeline = new[]
            {
                new BsonDocument("$match", new BsonDocument
                {
                    { "_id", ObjectId.Parse(transactionId) },
                    { "status", "SUCCESS" },
                    { "payment_method", "card" }
                }),
                new BsonDocument("$lookup", new BsonDocument
                {
                    { "from", "operations" },
                    { "let", new BsonDocument
                    {
                        { "operation_id_str", "$operation_id" },
                        { "threshold", thresholdDate }
                    }},
                    { "pipeline", new BsonArray
                    {
                        new BsonDocument("$match", new BsonDocument("$expr",
                            new BsonDocument("$and", new BsonArray
                            {
                                new BsonDocument("$eq", new BsonArray { "$_id", new BsonDocument("$toObjectId", "$$operation_id_str") }),
                                new BsonDocument("$eq", new BsonArray { "$status", "PROCESSING" }),
                                new BsonDocument("$lt", new BsonArray { "$date", "$$threshold" })
                            })
                        ))
                    }},
                    { "as", "operation_data" }
                }),
                new BsonDocument("$match", new BsonDocument
                {
                    { "operation_data", new BsonDocument("$ne", new BsonArray()) }
                }),
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "Operation", new BsonDocument("$arrayElemAt", new BsonArray { "$operation_data", 0 }) }
                }),
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "amount", new BsonDocument("$convert", new BsonDocument
                    {
                        { "input", "$amount" },
                        { "to", "decimal" },
                        { "onError", "$amount" }
                    })},
                    { "Operation.amount", new BsonDocument("$convert", new BsonDocument
                    {
                        { "input", "$Operation.amount" },
                        { "to", "decimal" },
                        { "onError", "$Operation.amount" }
                    })}
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "_id", 1 },
                    { "status", 1 },
                    { "payment_method", 1 },
                    { "operation_id", 1 },
                    { "amount", 1 },
                    { "Operation", 1 },
                    { "date", 1 },
                    { "updated_at", 1 },
                    { "payer_id", 1 }
                })
            };

            var result = await Collection
                .Aggregate<TransactionDocument>(pipeline)
                .FirstOrDefaultAsync(cancellationToken);

            return result;
        }
    }
}
