﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Documents;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace BlueTape.DataAccess.Mongo.Repositories;
public class UserRepository(ILmsMongoDBContext context, ILogger<UserRepository> logger)
    : GenericRepository<UserDto>(context, logger), IUserRepository
{
    public async Task<IEnumerable<UserDto>> GetUsersByEmail(string? email, CancellationToken cancellationToken)
    {
        var expression = Builders<UserDto>.Filter.Where(x => x.Login == email);

        return await Collection.Find(expression).ToListAsync(cancellationToken);
    }

    public Task<List<UserDto>> GetBySub(string sub, CancellationToken cancellationToken)
    {
        var filterBuilder = new FilterDefinitionBuilder<UserDto>();
        var filter = filterBuilder.Where(x => x.Sub == sub);

        return Collection.Find(filter).ToListAsync(cancellationToken);
    }
}
