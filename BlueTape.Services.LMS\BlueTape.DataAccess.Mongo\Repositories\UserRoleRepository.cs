using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Documents;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using System.Linq.Expressions;

namespace BlueTape.DataAccess.Mongo.Repositories;

public class UserRoleRepository(
    ILmsMongoDBContext context,
    ILogger<UserRoleRepository> logger)
    : GenericRepository<UserRoleDocument>(context, logger), IUserRoleRepository
{
    public IEnumerable<UserRoleDocument> GetByPredicate(Expression<Func<UserRoleDocument, bool>> predicate, CancellationToken ct)
    {
        return Collection.AsQueryable().Where(predicate).ToList();
    }
}