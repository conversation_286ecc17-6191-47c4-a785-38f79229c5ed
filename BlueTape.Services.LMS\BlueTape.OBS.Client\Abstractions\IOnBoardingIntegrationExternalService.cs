﻿using BlueTape.OBS.DTOs;
using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplicationAuthorizationDetails;
using BlueTape.OBS.DTOs.CreditApplicationNotes;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.OBS.DTOs.Draft;
using BlueTape.OBS.DTOs.DrawApproval.Queries;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.OBS.DTOs.DrawApprovalNotes;
using BlueTape.OBS.DTOs.PaymentPlan;

namespace BlueTape.OBS.Client.Abstractions
{
    public interface IOnBoardingIntegrationExternalService
    {
        Task<IEnumerable<CreditApplicationDto>?> GetCreditApplications(string? id, string? companyId, string? einHash, string? status, CancellationToken ct);

        Task<CreditApplicationAuthorizationDetailsDto?> GetApplicationAuthorizationDetails(string applicationId,
            CancellationToken ct);

        Task<IEnumerable<DecisionEngineStepsDto>?> GetDecisionEngineStepsByExecutionId(string executionId, CancellationToken ct);

        Task<CreditApplicationDto?> PatchCreditApplication(string id, UpdateCreditApplicationDto updateDto,
            CancellationToken ct);

        Task<CreditApplicationNoteDto?> CreateCreditApplicationNotes(CreditApplicationNoteCreateDto createDto,
            string userid, string creditApplicationId, CancellationToken ct);

        Task<IEnumerable<DrawApprovalNoteDto>> GetDrawApprovalNotes(string drawApprovalId, CancellationToken ct);

        Task<IEnumerable<AccountAuthorizationDto>?> GetAccountAuthorizations(string? id, string? companyId,
            string? einHash, string? ssnHash, CancellationToken ct);
        Task<CreditApplicationDto?> CreateCreditApplication(CreateCreditApplicationDto createDto, CancellationToken ct);

        Task<IEnumerable<DraftDto>?> GetDrafts(string? id, string? companyId, CancellationToken ct);
        Task<GetQueryWithPaginationResultDto<DrawApprovalDto>> GetDrawApprovalList(GetDrawApprovalsQueryWithPagination query, CancellationToken ct);
        Task<IEnumerable<DrawApprovalDto>?> GetDrawApprovalsByInvoiceIds(string[] invoiceIds, CancellationToken ct);

        Task<IEnumerable<DraftDto>?> GetDraftsByCompanyIds(string[] companyIds, CancellationToken ct);
        Task<IEnumerable<LoanPaymentPlanDto>?> GetPaymentPlans(CancellationToken ct);
    }
}
