﻿namespace BlueTape.OBS.Client.Abstractions;

public interface IPathBuilder
{
    string GetCreditApplicationsIdPath(string id);
    string GetApplicationAuthorizationDetailsPath(string id);
    string GetCreditApplicationsPath(string? id, string? companyId, string? einHash, string? status);
    string GetAccountAuthorizationsPath(string? id, string? companyId, string? einHash, string? ssnHash);
    string GetDecisionEngineStepsPath(string executionId);
    string GetCreditApplicationsNotesPath(string id);
    string GetDraftsPath(string? id, string? companyId);
    string GetDrawApprovalsNotesPath(string id);
}
