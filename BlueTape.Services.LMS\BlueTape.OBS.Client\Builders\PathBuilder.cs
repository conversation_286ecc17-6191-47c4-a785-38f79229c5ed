﻿using BlueTape.OBS.Client.Abstractions;
using BlueTape.OBS.Client.Constants;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Primitives;

namespace BlueTape.OBS.Client.Builders;

public class PathBuilder : IPathBuilder
{
    public string GetAccountAuthorizationsPath(string? id, string? companyId, string? einHash, string? ssnHash)
    {
        var url = QueryHelpers.AddQueryString(
            OnBoardingServiceConstants.AccountAuthorizations,
            new Dictionary<string, StringValues>
            {
                { nameof(id), id },
                { nameof(companyId), companyId },
                { nameof(einHash), einHash },
                { nameof(ssnHash), ssnHash }
            });

        return url;
    }

    public string GetCreditApplicationsPath(string? id, string? companyId, string? einHash, string? status)
    {
        var url = QueryHelpers.AddQueryString(
            OnBoardingServiceConstants.CreditApplications,
            new Dictionary<string, StringValues>
            {
                { nameof(id), id },
                { nameof(companyId), companyId },
                { nameof(einHash), einHash },
                { nameof(status), status }
            });

        return url;
    }

    public string GetDecisionEngineStepsPath(string executionId) =>
        $"{OnBoardingServiceConstants.DecisionEngineSteps}/{OnBoardingServiceConstants.Execution}/{executionId}";

    public string GetDraftsPath(string? id, string? companyId)
    {
        var url = QueryHelpers.AddQueryString(
            OnBoardingServiceConstants.Drafts,
            new Dictionary<string, StringValues>
            {
                { nameof(id), id },
                { nameof(companyId), companyId }
            });

        return url;
    }

    public string GetCreditApplicationsIdPath(string id) =>
        $"{OnBoardingServiceConstants.CreditApplications}/{id}";

    public string GetApplicationAuthorizationDetailsPath(string id) =>
        $"{OnBoardingServiceConstants.CreditApplicationAuthorizationDetails}/{id}";

    public string GetCreditApplicationsNotesPath(string id) =>
        $"{OnBoardingServiceConstants.CreditApplications}/{id}/Notes";

    public string GetDrawApprovalsNotesPath(string id) =>
        $"{OnBoardingServiceConstants.DrawApprovals}/{id}/Notes";
}
