﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.OBS.Client.Abstractions;
using BlueTape.OBS.Client.Builders;
using BlueTape.OBS.Client.Constants;
using BlueTape.OBS.Client.Providers;
using BlueTape.OBS.Client.Services;
using BlueTape.Services.Utilities.AspNetCore.Tracing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.OBS.Client.Extensions
{
    public static class DependencyRegistrar
    {
        public static void AddDataAccessObsDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddSingleton<IPathBuilder, PathBuilder>();
            services.AddSingleton<ITraceIdProvider, TraceIdProvider>();
            services.AddSingleton<ITraceIdAccessor, TraceIdAccessor>();

            services.AddHttpClient<IOnBoardingServiceHttpClient, OnBoardingServiceHttpClient>(client =>
             {
                 client.BaseAddress = GetBaseAddressUri(config, OnBoardingServiceConstants.OnBoardingServiceUrl);
             }).SetHandlerLifetime(TimeSpan.FromMinutes(5))
            .AddPolicyHandler((serviceProvider, _) => HttpRetryPolicies.GetOnBoardingServiceRetryPolicy(serviceProvider));

            services.AddScoped<IOnBoardingIntegrationExternalService, OnBoardingExternalService>();
        }

        private static Uri GetBaseAddressUri(IConfiguration config, string configurationUrl)
        {
            return new Uri(config[configurationUrl]!);
        }
    }
}
