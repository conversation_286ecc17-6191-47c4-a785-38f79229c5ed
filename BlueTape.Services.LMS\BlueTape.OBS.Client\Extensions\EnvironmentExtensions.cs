﻿using BlueTape.OBS.Client.Constants;

namespace BlueTape.OBS.Client.Extensions
{
    public static class EnvironmentExtensions
    {
        public static bool IsDevelopment()
        {
            var currentEnv = Environment.GetEnvironmentVariable(ApplicationConstants.AspNetCoreEnvironmentVariable);
            return currentEnv is null or ApplicationConstants.DevelopmentEnvironment;
        }
    }
}
