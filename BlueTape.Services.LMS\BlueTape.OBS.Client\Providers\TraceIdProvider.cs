﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.OBS.Client.Abstractions;

namespace BlueTape.OBS.Client.Providers;

public class TraceIdProvider : ITraceIdProvider
{
    private readonly ITraceIdFactory _traceIdFactory;
    public TraceIdProvider(ITraceIdFactory traceIdFactory)
    {
        _traceIdFactory = traceIdFactory;
    }

    private string? _traceId;

    public string? TraceId
    {
        get => string.IsNullOrEmpty(_traceId) ? _traceIdFactory.CreateTraceId() : _traceId;
        set => _traceId = value;
    }
}
