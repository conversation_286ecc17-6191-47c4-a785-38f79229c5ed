﻿using BlueTape.OBS.Client.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;
using System.Net;

namespace BlueTape.OBS.Client.Services
{
    public static class HttpRetryPolicies
    {
        public static IAsyncPolicy<HttpResponseMessage> GetOnBoardingServiceRetryPolicy(IServiceProvider serviceProvider)
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .OrResult(msg => !msg.IsSuccessStatusCode &&
                                 msg.StatusCode is not HttpStatusCode.NotFound and HttpStatusCode.BadRequest)
                .WaitAndRetryAsync(new[]
                {
                    TimeSpan.FromMilliseconds(20),
                    TimeSpan.FromMilliseconds(50),
                    TimeSpan.FromMilliseconds(100),
                    TimeSpan.FromMilliseconds(500),
                    TimeSpan.FromMilliseconds(2000),
                }, (response, timespan, retryCount, context) =>
                {
                    var logger = serviceProvider.GetRequiredService<ILogger<IOnBoardingServiceHttpClient>>();
                    logger?.LogWarning("Polly retried after {ElapsedTime} milliseconds for the {RetryCount}-th time.", timespan.Milliseconds, retryCount);
                });
        }
    }
}