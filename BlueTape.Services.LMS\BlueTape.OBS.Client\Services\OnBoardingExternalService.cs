using BlueTape.OBS.Client.Abstractions;
using BlueTape.OBS.Client.Constants;
using BlueTape.OBS.DTOs;
using BlueTape.OBS.DTOs.AccountAuthorization;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.OBS.DTOs.CreditApplicationAuthorizationDetails;
using BlueTape.OBS.DTOs.CreditApplicationNotes;
using BlueTape.OBS.DTOs.DecisionEngineSteps;
using BlueTape.OBS.DTOs.Draft;
using BlueTape.OBS.DTOs.DrawApproval.Queries;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.OBS.DTOs.DrawApprovalNotes;
using BlueTape.OBS.DTOs.PaymentPlan;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using System.Text;

namespace BlueTape.OBS.Client.Services;

public class OnBoardingExternalService : IOnBoardingIntegrationExternalService
{
    private readonly IOnBoardingServiceHttpClient _httpClient;
    private readonly ILogger<OnBoardingExternalService> _logger;
    private readonly IPathBuilder _pathBuilder;
    private readonly ITraceIdProvider _traceIdProvider;

    public OnBoardingExternalService(IOnBoardingServiceHttpClient httpClient, ITraceIdProvider traceIdProvider,
        ILogger<OnBoardingExternalService> logger, IPathBuilder pathBuilder)
    {
        _httpClient = httpClient;
        _logger = logger;
        _pathBuilder = pathBuilder;
        _traceIdProvider = traceIdProvider;
    }

    public async Task<IEnumerable<AccountAuthorizationDto>?> GetAccountAuthorizations(string? id, string? companyId, string? einHash, string? ssnHash, CancellationToken ct)
    {
        SetTraceIdHeader();
        var path = _pathBuilder.GetAccountAuthorizationsPath(id, companyId, einHash, null);

        var response = await _httpClient.Client.GetAsync(path, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetAccountAuthorizations: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<IEnumerable<AccountAuthorizationDto>>(responseString);
        }

        throw new BadHttpRequestException($"GetAccountAuthorizations failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<IEnumerable<CreditApplicationDto>?> GetCreditApplications(string? id, string? companyId, string? einHash, string? status, CancellationToken ct)
    {
        SetTraceIdHeader();
        var path = _pathBuilder.GetCreditApplicationsPath(id, companyId, einHash, status);

        var response = await _httpClient.Client.GetAsync(path, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetCreditApplications: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<IEnumerable<CreditApplicationDto>>(responseString);
        }

        throw new BadHttpRequestException($"GetCreditApplications failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<CreditApplicationAuthorizationDetailsDto?> GetApplicationAuthorizationDetails(string applicationId, CancellationToken ct)
    {
        ArgumentException.ThrowIfNullOrEmpty(applicationId);

        SetTraceIdHeader();
        var path = _pathBuilder.GetApplicationAuthorizationDetailsPath(applicationId);

        var response = await _httpClient.Client.GetAsync(path, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetApplicationAuthorizationDetails: string response: {Response}", responseString);
            return JsonConvert.DeserializeObject<CreditApplicationAuthorizationDetailsDto>(responseString);
        }

        throw new BadHttpRequestException($"GetApplicationAuthorizationDetails failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<IEnumerable<DecisionEngineStepsDto>?> GetDecisionEngineStepsByExecutionId(string executionId,
        CancellationToken ct)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);
        SetTraceIdHeader();
        var path = _pathBuilder.GetDecisionEngineStepsPath(executionId);
        var response = await _httpClient.Client.GetAsync(path, ct);
        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetDecisionEngineStepsByExecutionId: string response: {Response}", responseString);
            return JsonConvert.DeserializeObject<IEnumerable<DecisionEngineStepsDto>>(responseString);
        }

        throw new BadHttpRequestException($"GetDecisionEngineStepsByExecutionId failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<CreditApplicationDto?> CreateCreditApplication(CreateCreditApplicationDto createDto, CancellationToken ct)
    {
        SetTraceIdHeader();
        var json = JsonConvert.SerializeObject(createDto);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);
        var response = await _httpClient.Client.PostAsync(OnBoardingServiceConstants.CreditApplications, data, ct);
        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("CreateCreditApplication: initial data : {data}, string response: {response}", data, responseString);
            return JsonConvert.DeserializeObject<CreditApplicationDto>(responseString);
        }

        throw new BadHttpRequestException($"CreateCreditApplication failed at path: {OnBoardingServiceConstants.CreditApplications}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<CreditApplicationNoteDto?> CreateCreditApplicationNotes(CreditApplicationNoteCreateDto createDto, string userid, string creditApplicationId, CancellationToken ct)
    {
        SetUserIdHeader(userid);
        var json = JsonConvert.SerializeObject(createDto);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);
        var path = _pathBuilder.GetCreditApplicationsNotesPath(creditApplicationId);
        var response = await _httpClient.Client.PostAsync(path, data, ct);
        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("CreateCreditApplicationNotes: initial data : {data}, string response: {response}", data, responseString);
            return JsonConvert.DeserializeObject<CreditApplicationNoteDto>(responseString);
        }

        throw new BadHttpRequestException($"CreateCreditApplicationNotes failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<IEnumerable<DraftDto>?> GetDrafts(string? id, string? companyId, CancellationToken ct)
    {
        SetTraceIdHeader();

        var path = _pathBuilder.GetDraftsPath(id, companyId);
        var response = await _httpClient.Client.GetAsync(path, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetDrafts: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<IEnumerable<DraftDto>>(responseString);
        }

        throw new BadHttpRequestException($"GetDrafts failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<IEnumerable<LoanPaymentPlanDto>?> GetPaymentPlans(CancellationToken ct)
    {
        SetTraceIdHeader();
        var path = OnBoardingServiceConstants.PaymentPlans;
        var response = await _httpClient.Client.GetAsync(path, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetPaymentPlans: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<IEnumerable<LoanPaymentPlanDto>>(responseString);
        }

        throw new BadHttpRequestException($"GetPaymentPlans failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<IEnumerable<DrawApprovalNoteDto>> GetDrawApprovalNotes(string drawApprovalId, CancellationToken ct)
    {
        SetTraceIdHeader();

        var path = _pathBuilder.GetDrawApprovalsNotesPath(drawApprovalId);
        var response = await _httpClient.Client.GetAsync(path, ct);

        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetDrawApprovalNotes: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<IEnumerable<DrawApprovalNoteDto>>(responseString) ?? Enumerable.Empty<DrawApprovalNoteDto>();
        }

        throw new BadHttpRequestException($"GetDrafts failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<CreditApplicationDto?> PatchCreditApplication(string id, UpdateCreditApplicationDto updateDto, CancellationToken ct)
    {
        SetTraceIdHeader();
        var path = _pathBuilder.GetCreditApplicationsIdPath(id);

        var json = JsonConvert.SerializeObject(updateDto);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);
        var response = await _httpClient.Client.PatchAsync(path, data, ct);
        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("PatchCreditApplication: initial data : {data}, string response: {response}", data, responseString);
            return JsonConvert.DeserializeObject<CreditApplicationDto>(responseString);
        }

        throw new BadHttpRequestException($"PatchCreditApplication failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    private void SetTraceIdHeader()
    {
        var traceId = _traceIdProvider.TraceId;
        if (!string.IsNullOrEmpty(traceId))
        {
            _httpClient.SetHeader(HttpConstants.TraceIdHeader, traceId);
        }
    }

    private void SetUserIdHeader(string userId) => _httpClient.SetHeader(HttpConstants.UserIdHeader, userId);

    public async Task<GetQueryWithPaginationResultDto<DrawApprovalDto>> GetDrawApprovalList(GetDrawApprovalsQueryWithPagination query, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(query);

        var url = QueryHelpers.AddQueryString(
           "drawApprovals",
            new Dictionary<string, StringValues>
            {
                { nameof(query.Id), query.Id },
                { nameof(query.CompanyId), query.CompanyId },
                { nameof(query.CompanyName), query.CompanyName },
                { nameof(query.Status), query.Status },
                { nameof(query.IhcStatus), query.IhcStatus },
                { nameof(query.Type), query.Type },
                { nameof(query.AutomatedDecisionResult), query.AutomatedDecisionResult },
                { nameof(query.AccountStatus), query.AccountStatus },
                { nameof(query.PageNumber), query.PageNumber.ToString() },
                { nameof(query.Search), query.Search},
                { nameof(query.MerchantId), query.MerchantId},
                { nameof(query.MerchantName), query.MerchantName},
                { nameof(query.CompanyDba), query.CompanyDba },
                { nameof(query.Product), query.Product},
                { nameof(query.PaymentPlanId), query.PaymentPlanId },
                { nameof(query.PageSize), query.PageSize.ToString() },
                { nameof(query.SortBy), query.SortBy },
                { nameof(query.SortOrder), query.SortOrder },
                { nameof(query.PayableTypes), query.PayableTypes },
                { nameof(query.InvoiceNumber), query.InvoiceNumber },
                { nameof(query.Term), query.Term },
                { nameof(query.InvoiceStatuses), query.InvoiceStatuses }
            });

        var response = await _httpClient.Client.GetAsync(url, ct);
        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetDrawApprovalList: string response: {response}", responseString);
            return JsonConvert.DeserializeObject<GetQueryWithPaginationResultDto<DrawApprovalDto>>(responseString) ?? new GetQueryWithPaginationResultDto<DrawApprovalDto>();
        }

        throw new BadHttpRequestException($"PatchCreditApplication failed at path: {url}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<IEnumerable<DraftDto>?> GetDraftsByCompanyIds(string[] companyIds, CancellationToken ct)
    {
        var json = JsonConvert.SerializeObject(companyIds);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);
        var path = OnBoardingServiceConstants.Drafts;
        var response = await _httpClient.Client.PostAsync(path, data, ct);
        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetDraftsByCompanyIds: initial data : {data}, string response: {response}", data, responseString);
            return JsonConvert.DeserializeObject<IEnumerable<DraftDto>?>(responseString);
        }

        throw new BadHttpRequestException($"GetDraftsByCompanyIds failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }

    public async Task<IEnumerable<DrawApprovalDto>?> GetDrawApprovalsByInvoiceIds(string[] invoiceIds, CancellationToken ct)
    {
        var json = JsonConvert.SerializeObject(invoiceIds);
        var data = new StringContent(json, Encoding.UTF8, HttpConstants.ApplicationJson);
        var path = "DrawApprovals/Invoices";
        var response = await _httpClient.Client.PostAsync(path, data, ct);
        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync(ct);
            _logger.LogInformation("GetDrawApprovalsByInvoiceIds: initial data : {data}, string response: {response}", data, responseString);
            return JsonConvert.DeserializeObject<IEnumerable<DrawApprovalDto>?>(responseString);
        }

        throw new BadHttpRequestException($"GetDrawApprovalsByInvoiceIds failed at path: {path}." +
                                          $"Status code: {response.StatusCode}");
    }
}