﻿using BlueTape.OBS.Client.Abstractions;
using BlueTape.OBS.Client.Constants;
using BlueTape.OBS.Client.Extensions;
using Microsoft.Extensions.Configuration;

namespace BlueTape.OBS.Client.Services
{
    public class OnBoardingServiceHttpClient : IOnBoardingServiceHttpClient
    {
        public HttpClient Client { get; private set; }

        public OnBoardingServiceHttpClient(HttpClient client, IConfiguration configurationService)
        {
            Client = client;
            if (EnvironmentExtensions.IsDevelopment()) return;
            var apiKey = configurationService[OnBoardingServiceConstants.OnBoardingServiceApiKey];
            Client.DefaultRequestHeaders.Add(HttpConstants.ApiKeyHeader, apiKey);
        }
    }
}
