﻿using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.ARS.DataAccess.Abstractions.Repositories
{
    public interface ILightLoanRepository
    {
        Task<IReadOnlyCollection<LightLoanEntity>> GetByCompanyId(string companyId, ProductType? product, string? merchantId, CancellationToken ct);
        Task<IReadOnlyCollection<LightLoanEntity>> GetAll(ProductType? product, string? merchantId, CancellationToken ct);
    }
}
