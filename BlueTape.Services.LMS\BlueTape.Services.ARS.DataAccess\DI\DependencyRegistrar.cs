﻿using BlueTape.Services.ARS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.ARS.DataAccess.Mappers;
using BlueTape.Services.ARS.DataAccess.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace BlueTape.Services.ARS.DataAccess.DI
{
    public static class DependencyRegistrar
    {
        public static void AddArsDataAccessDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddTransient<ILightLoanRepository, LightLoanRepository>();
            services.AddAutoMapper(typeof(LightEntitiesProfile).GetTypeInfo().Assembly);
        }
    }
}
