﻿using AutoMapper;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Extensions;

namespace BlueTape.Services.ARS.DataAccess.Mappers
{
    public class LightEntitiesProfile : Profile
    {
        public LightEntitiesProfile()
        {
            CreateProjection<LoanEntity, LightLoanEntity>();
            CreateProjection<PaymentEntity, LightPaymentEntity>();
            CreateProjection<LoanReceivableEntity, LightLoanReceivableEntity>()
                .ForMember(x => x.OutstandingAmount,
                    opt => opt
                        .MapFrom(x => x.GetOutstandingAmount()));
        }
    }
}
