﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using BlueTape.Services.ARS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.DataAccess.Contexts;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace BlueTape.Services.ARS.DataAccess.Repositories
{
    public class LightLoanRepository : ILightLoanRepository
    {
        private readonly DatabaseContext _db;
        private readonly IMapper _mapper;

        public LightLoanRepository(DatabaseContext db, IMapper mapper)
        {
            _db = db;
            _mapper = mapper;
        }

        public async Task<IReadOnlyCollection<LightLoanEntity>> GetByCompanyId(string companyId, ProductType? product, string? merchantId, CancellationToken ct)
        {
            ArgumentNullException.ThrowIfNull(_db.Loans);

            var query = _db.Loans
                .AsNoTracking()
                .Include(x => x.Payments)
                .Include(x => x.LoanReceivables)
                .Where(x => x.CompanyId == companyId && !x.IsDeleted && x.Status == LoanStatus.Started);

            if (product.HasValue)
            {
                query = query
                    .Include(x => x.Credit)
                    .Where(x => x.Credit != null && x.Credit.Product == product.Value);
            }

            if (!string.IsNullOrEmpty(merchantId) && !product.Equals(ProductType.LineOfCredit))
            {
                query = query.Where(x => x.MerchantId == merchantId);

            }

            var results = await query.ProjectTo<LightLoanEntity>(_mapper.ConfigurationProvider).ToListAsync(ct);

            return results;
        }

        public async Task<IReadOnlyCollection<LightLoanEntity>> GetAll(ProductType? product, string? merchantId, CancellationToken ct)
        {
            ArgumentNullException.ThrowIfNull(_db.Loans);

            var query = _db.Loans
                .AsNoTracking()
                .Include(x => x.Credit)
                .Include(x => x.Payments)
                .Include(x => x.LoanReceivables)
                .Where(x => !x.IsDeleted && x.Status == LoanStatus.Started);

            if (product.HasValue)
            {
                query = query
                    .Include(x => x.Credit)
                    .Where(x => x.Credit != null && x.Credit.Product == product.Value);
            }

            if (!string.IsNullOrEmpty(merchantId) && !product.Equals(ProductType.LineOfCredit))
            {
                query = query.Where(x => x.MerchantId == merchantId);
            }

            var results = await query.ProjectTo<LightLoanEntity>(_mapper.ConfigurationProvider).ToListAsync(ct);

            return results;
        }
    }
}