﻿namespace BlueTape.Services.ARS.IntegrationTests.Constants
{
    public static class AgingDetailsConstants
    {
        public const string PendingName = "pending";
        public const string DueName = "due";
        public const string ProcessingName = "processing";
        public const string PastDue30Name = "past due 1-30 days";
        public const string PastDue60Name = "past due 31-60 days";
        public const string PastDue90Name = "past due 61-90 days";
        public const string PastDue120Name = "past due 91-120 days";
        public const string PastDue150Name = "past due 121-150 days";
        public const string PastDue180Name = "past due 151-180 days";
        public const string PastDue180PlusName = "past due 180+ days";

        public const string PendingCode = "PENDING";
        public const string DueCode = "DUE";
        public const string ProcessingCode = "PROCESSING";
        public const string PastDue30Code = "PASTDUE30";
        public const string PastDue60Code = "PASTDUE60";
        public const string PastDue90Code = "PASTDUE90";
        public const string PastDue120Code = "PASTDUE120";
        public const string PastDue150Code = "PASTDUE150";
        public const string PastDue180Code = "PASTDUE180";
        public const string PastDue180PlusCode = "PASTDUE180PLUS";
    }
}
