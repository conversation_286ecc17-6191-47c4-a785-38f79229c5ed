﻿using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.ARS.IntegrationTests.Entities.LoanTemplates
{
    public static class LoanTemplateEntities
    {
        public static readonly IEnumerable<LoanTemplateEntity> LoanTemplateEntitiesList = new List<LoanTemplateEntity>
        {
            new() { Id = Guid.Parse("3476b83d-f7c7-492d-a986-a6e3b3b87b43"), InstallmentsNumber = 1, LoanFeePercentage = 0, PaymentDelayInDays = 30, Code="30", PaymentDelayCode = PaymentDelayCode.TD30, PaymentIntervalInDays = 7, MinimumLateFeeAmount = 35, EarlyPayPeriod = 0, TotalDurationInDays = 30, LateFeePercentage = 0.04m, GracePeriodInDays = 3, LateFeeCollectionDelayInDays = 1 },
            new() { Id = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"), InstallmentsNumber = 1, LoanFeePercentage = 0, PaymentDelayInDays = 30, Code="30", PaymentDelayCode = PaymentDelayCode.TD30, PaymentIntervalInDays = 7, MinimumLateFeeAmount = 35, EarlyPayPeriod = 0, TotalDurationInDays = 30, LateFeePercentage = 0.1m, GracePeriodInDays = 3, LateFeeCollectionDelayInDays = 1 },
            new() { Id = Guid.Parse("3ee4cbd6-935a-436e-b2d1-041ac9a5dc11"), InstallmentsNumber = 5, LoanFeePercentage = 0.02m, PaymentDelayInDays = 30, Code="60",PaymentDelayCode = PaymentDelayCode.TD30,  PaymentIntervalInDays = 7, MinimumLateFeeAmount = 35, EarlyPayPeriod = 0, TotalDurationInDays = 60, LateFeePercentage = 0.04m, GracePeriodInDays = 3, LateFeeCollectionDelayInDays = 1 },
            new() { Id = Guid.Parse("3ee4cbd6-935a-436e-b2d1-041ac9a5dc33"), InstallmentsNumber = 5, LoanFeePercentage = 0.02m, PaymentDelayInDays = 60, Code="60", PaymentDelayCode = PaymentDelayCode.TD60, PaymentIntervalInDays = 7, MinimumLateFeeAmount = 35, EarlyPayPeriod = 0, TotalDurationInDays = 60, LateFeePercentage = 0.04m, GracePeriodInDays = 3, LateFeeCollectionDelayInDays = 1 },
            new() { Id = Guid.Parse("7ce0034d-800e-4ce0-9585-e9dd17338897"), InstallmentsNumber = 9, LoanFeePercentage = 0.04m, PaymentDelayInDays = 60, Code="90",PaymentDelayCode = PaymentDelayCode.TD60,  PaymentIntervalInDays = 7, MinimumLateFeeAmount = 35, EarlyPayPeriod = 0, TotalDurationInDays = 90, LateFeePercentage = 0.04m, GracePeriodInDays = 3 , LateFeeCollectionDelayInDays = 1},
            new() { Id = Guid.Parse("3ee4cbd6-935a-436e-b2d1-041ac9a5dc12"), InstallmentsNumber = 9, LoanFeePercentage = 0.02m, PaymentDelayInDays = 30, Code="60",PaymentDelayCode = PaymentDelayCode.TD30,  PaymentIntervalInDays = 7, MinimumLateFeeAmount = 35, EarlyPayPeriod = 0, TotalDurationInDays = 90, LateFeePercentage = 0.04m, GracePeriodInDays = 3, LateFeeCollectionDelayInDays = 1 },
            new() { Id = Guid.Parse("7ce0034d-800e-4ce0-9585-e9dd17338879"), InstallmentsNumber = 9, LoanFeePercentage = 0.04m, PaymentDelayInDays = 30, Code="90",PaymentDelayCode = PaymentDelayCode.TD30,  PaymentIntervalInDays = 7, MinimumLateFeeAmount = 35, EarlyPayPeriod = 0, TotalDurationInDays = 90, LateFeePercentage = 0.04m, GracePeriodInDays = 3 , LateFeeCollectionDelayInDays = 1},
            new() { Id = Guid.Parse("7ce0034d-900e-4ce0-9585-e9dd17338878"), InstallmentsNumber = 13, LoanFeePercentage = 0.06m, PaymentDelayInDays = 30, Code="120",PaymentDelayCode = PaymentDelayCode.TD30,  PaymentIntervalInDays = 7, MinimumLateFeeAmount = 35, EarlyPayPeriod = 0, TotalDurationInDays = 120, LateFeePercentage = 0.04m, GracePeriodInDays = 3, LateFeeCollectionDelayInDays = 1},
            new() { Id = Guid.Parse("7ce0034d-900e-4ce0-9585-e9dd19438878"), InstallmentsNumber = 13, LoanFeePercentage = 0, PaymentDelayInDays = 30, PaymentDelayCode = PaymentDelayCode.TD30, Code="120", PaymentIntervalInDays = 7, MinimumLateFeeAmount = 35, EarlyPayPeriod = 0, TotalDurationInDays = 120, LateFeePercentage = 0.04m, GracePeriodInDays = 3, LateFeeCollectionDelayInDays = 1},
        };
    }
}
