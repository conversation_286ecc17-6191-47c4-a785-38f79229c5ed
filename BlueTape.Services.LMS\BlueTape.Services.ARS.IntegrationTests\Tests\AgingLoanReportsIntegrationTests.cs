﻿using BlueTape.LS.Domain.Enums;
using BlueTape.Services.ARS.IntegrationTests.Constants;
using BlueTape.Services.ARS.IntegrationTests.Dtos;
using BlueTape.Services.ARS.IntegrationTests.Tests.Base;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.IntegrationTests.Tests
{
    [Collection(TestsConfiguration.CollectionFixtureName)]
    public class AgingLoanReportsIntegrationTests : ArsIntegrationTestsBase
    {
        [Fact]
        public async Task GetAgingLoanReportByCompanyIdScenario()
        {
            var today = new DateOnly(2022, 05, 01);
            var createLoans = LoanDtos.CreateLoanDtosForAgingLoanReports;

            const decimal firstAmountWithFee = 7000;
            const decimal secondAmountWithFee = 13780;

            const int dueDelay = -30;
            const int processingDelay = -70;
            const int pastDue30Delay = -35;
            const int pastDue60Delay = -65;
            const int pastDue90Delay = -70;
            const int pastDue120Delay = -110;
            const int pastDue150Delay = -155;
            const int pastDue180Delay = -171;
            const int pastDuePlusDelay = -181;

            var firstLoan = await CreateAndStartLoan(createLoans[0]);
            await Pay(firstLoan.Id, 200);
            await ChangePaymentStatus(firstLoan.LoanReceivables[0].Id, PaymentStatus.Processing);

            await CreateAndStartLoan(createLoans[1]);

            await ChangeExpectedDate(today.AddDays(processingDelay), firstLoan.LoanReceivables[0].Id);
            await ChangeExpectedDate(today.AddDays(dueDelay), firstLoan.LoanReceivables[1].Id);
            await ChangeExpectedDate(today.AddDays(pastDue30Delay), firstLoan.LoanReceivables[2].Id);
            await ChangeExpectedDate(today.AddDays(pastDue60Delay), firstLoan.LoanReceivables[3].Id);
            await ChangeExpectedDate(today.AddDays(pastDue90Delay), firstLoan.LoanReceivables[4].Id);
            await ChangeExpectedDate(today.AddDays(pastDue120Delay), firstLoan.LoanReceivables[5].Id);
            await ChangeExpectedDate(today.AddDays(pastDue150Delay), firstLoan.LoanReceivables[6].Id);
            await ChangeExpectedDate(today.AddDays(pastDue180Delay), firstLoan.LoanReceivables[7].Id);
            await ChangeExpectedDate(today.AddDays(pastDuePlusDelay), firstLoan.LoanReceivables[8].Id);

            var agingLoanReports = await GetAgingLoanReportByCompanyId(TestConstants.CompanyId);

            agingLoanReports.ShouldAllBe(x => x.AgingDetails.Count == 10);
            agingLoanReports.Count.ShouldBe(2);
            var firstReport = agingLoanReports[0];
            var secondReport = agingLoanReports[1];
            firstReport.AgingDetails.Sum(x => x.Amount).ShouldBe(firstAmountWithFee);
            secondReport.AgingDetails.Sum(x => x.Amount).ShouldBe(secondAmountWithFee);
            agingLoanReports.Count.ShouldBe(2);
        }

        [Fact]
        public async Task GetAgingListByCompanyIdScenario_ProcessingLoansOnly()
        {
            var createLoans = LoanDtos.CreateLoanDtosForAgingLoanReports;

            const decimal firstAmountWithFee = 7000;
            const decimal secondAmountWithFee = 13780;
            const decimal thirdAmountWithFee = 10400;

            await CreateAndStartLoan(createLoans[0]);
            await CreateAndStartLoan(createLoans[1]);
            await CreateAndStartLoan(createLoans[2]);

            var agingLoanReports = await GetAgingLoanReportByCompanyId(TestConstants.CompanyId);

            agingLoanReports.Count.ShouldBe(3);
            var firstReport = agingLoanReports[0];
            var secondReport = agingLoanReports[1];
            var thirdReport = agingLoanReports[2];

            firstReport.AgingDetails!.Sum(x => x.Amount).ShouldBe(firstAmountWithFee);
            secondReport.AgingDetails!.Sum(x => x.Amount).ShouldBe(secondAmountWithFee);
            thirdReport.AgingDetails!.Sum(x => x.Amount).ShouldBe(thirdAmountWithFee);
        }

        [Fact]
        public async Task GetAgingLoanReportByCompanyIdScenario_CompanyIdNotFound()
        {
            var createLoans = LoanDtos.CreateLoanDtosWithSameCompanyId;

            await CreateAndStartLoan(createLoans[0]);
            await CreateAndStartLoan(createLoans[1]);
            await CreateAndStartLoan(createLoans[2]);

            var agingLoanReport = await GetAgingLoanReportByCompanyId("6346dee37aa413ca96c7832b");

            agingLoanReport.ShouldNotBeNull();
            agingLoanReport.Count.ShouldBe(0);
        }

        [Fact]
        public async Task GetAgingLoanReportByCompanyIdScenario_InvalidCompanyId()
        {
            var createLoans = LoanDtos.CreateLoanDtosForAgingLoanReports;

            await CreateAndStartLoan(createLoans[0]);
            await CreateAndStartLoan(createLoans[1]);

            var agingLoanReports = await GetAgingLoanReportByCompanyId(string.Empty);
            agingLoanReports.ShouldBeNull();
        }
    }
}
