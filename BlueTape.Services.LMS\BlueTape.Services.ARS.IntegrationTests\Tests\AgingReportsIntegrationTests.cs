﻿using BlueTape.Services.ARS.IntegrationTests.Constants;
using BlueTape.Services.ARS.IntegrationTests.Dtos;
using BlueTape.Services.ARS.IntegrationTests.Tests.Base;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.IntegrationTests.Tests
{
    [Collection(TestsConfiguration.CollectionFixtureName)]
    public class AgingReportsIntegrationTests : ArsIntegrationTestsBase
    {

        [Fact]
        public async Task GetAgingReportScenario()
        {
            var createLoans = LoanDtos.CreateLoanDtos;
            var today = new DateOnly(2022, 05, 01);

            const decimal payment = 1200;

            const int dueDelay = -30;
            const int pastDue30Delay = -35;
            const int pastDue60Delay = -65;
            const int pastDue90Delay = -95;
            const int pastDue120Delay = -125;
            const int pastDue150Delay = -155;
            const int pastDue180Delay = -185;
            const int pastDuePlusDelay = -211;

            ChangeCurrentDay(dueDelay, today);
            var dueLoan = await CreateAndStartLoan(createLoans[0]);


            ChangeCurrentDay(0, today);
            var pendingLoan = await CreateAndStartLoan(createLoans[2]);

            ChangeCurrentDay(pastDue30Delay, today);
            var pastDue30Loan = await CreateAndStartLoan(createLoans[3]);

            ChangeCurrentDay(pastDue60Delay, today);
            var pastDue60Loan = await CreateAndStartLoan(createLoans[4]);
            await Pay(pastDue60Loan.Id, payment);

            ChangeCurrentDay(pastDue90Delay, today);
            var pastDue90Loan = await CreateAndStartLoan(createLoans[5]);

            ChangeCurrentDay(pastDue120Delay, today);
            var pastDue120Loan = await CreateAndStartLoan(createLoans[6]);

            ChangeCurrentDay(pastDue150Delay, today);
            var pastDue150Loan = await CreateAndStartLoan(createLoans[7]);

            ChangeCurrentDay(pastDue180Delay, today);
            var pastDue180Loan = await CreateAndStartLoan(createLoans[8]);

            ChangeCurrentDay(pastDuePlusDelay, today);
            var pastDue180PlusLoan = await CreateAndStartLoan(createLoans[9]);

            ChangeCurrentDay(0, today);

            var loans = await GetLoans();

            var agingReport = await GetAgingReport();

            agingReport.CompanyCount.ShouldBe(loans.Select(x => x.CompanyId).Distinct().Count());
            agingReport.LoanCount.ShouldBe(loans.Count());

            var dueAgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.DueCode);
            var processingAgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.ProcessingCode);
            var pendingAgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PendingCode);
            var pastDue30AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue30Code);
            var pastDue60AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue60Code);
            var pastDue90AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue90Code);
            var pastDue120AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue120Code);
            var pastDue150AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue150Code);
            var pastDue180AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue180Code);
            var pastDue180PlusDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue180PlusCode);

            dueAgingDetails.Amount.ShouldBe(dueLoan.Amount);
            processingAgingDetails.Amount.ShouldBe(payment);
            pendingAgingDetails.Amount.ShouldBe(pendingLoan.Amount);
            pastDue30AgingDetails.Amount.ShouldBe(pastDue30Loan.Amount);
            pastDue60AgingDetails.Amount.ShouldBe(pastDue60Loan.Amount - payment);
            pastDue90AgingDetails.Amount.ShouldBe(pastDue90Loan.Amount);
            pastDue120AgingDetails.Amount.ShouldBe(pastDue120Loan.Amount);
            pastDue150AgingDetails.Amount.ShouldBe(pastDue150Loan.Amount);
            pastDue180AgingDetails.Amount.ShouldBe(pastDue180Loan.Amount);
            pastDue180PlusDetails.Amount.ShouldBe(pastDue180PlusLoan.Amount);

            agingReport.CompanyCount.ShouldBe(9);
            agingReport.LoanCount.ShouldBe(loans.Count());
        }

        [Fact]
        public async Task GetAgingReportByCompanyIdScenario_ValidCompanyId()
        {
            var today = new DateOnly(2022, 05, 01);
            var createLoans = LoanDtos.CreateLoanDtosWithSameCompanyId;
            const decimal payment = 1300;

            const int dueDelay = -30;
            const int pastDue30Delay = -35;
            const int pastDue60Delay = -65;
            const int pastDue90Delay = -95;
            const int pastDue120Delay = -125;
            const int pastDue150Delay = -155;
            const int pastDue180Delay = -185;
            const int pastDuePlusDelay = -211;

            ChangeCurrentDay(dueDelay, today);
            var dueLoan = await CreateAndStartLoan(createLoans[0]);

            ChangeCurrentDay(0, today);
            var pendingLoan = await CreateAndStartLoan(createLoans[2]);

            ChangeCurrentDay(pastDue30Delay, today);
            var pastDue30Loan = await CreateAndStartLoan(createLoans[3]);

            ChangeCurrentDay(pastDue60Delay, today);
            var pastDue60Loan = await CreateAndStartLoan(createLoans[4]);
            await Pay(pastDue60Loan.Id, payment);

            ChangeCurrentDay(pastDue90Delay, today);
            var pastDue90Loan = await CreateAndStartLoan(createLoans[5]);

            ChangeCurrentDay(pastDue120Delay, today);
            var pastDue120Loan = await CreateAndStartLoan(createLoans[6]);

            ChangeCurrentDay(pastDue150Delay, today);
            var pastDue150Loan = await CreateAndStartLoan(createLoans[7]);

            ChangeCurrentDay(pastDue180Delay, today);
            var pastDue180Loan = await CreateAndStartLoan(createLoans[8]);

            ChangeCurrentDay(pastDuePlusDelay, today);
            var pastDue180PlusLoan = await CreateAndStartLoan(createLoans[9]);

            ChangeCurrentDay(0, today);

            var loans = await GetLoans();

            var agingReport = await GetAgingReportByCompanyId(TestConstants.CompanyId);

            agingReport.CompanyCount.ShouldBe(loans.Select(x => x.CompanyId).Distinct().Count());
            agingReport.LoanCount.ShouldBe(loans.Count());

            var dueAgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.DueCode);
            var processingAgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.ProcessingCode);
            var pendingAgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PendingCode);
            var pastDue30AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue30Code);
            var pastDue60AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue60Code);
            var pastDue90AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue90Code);
            var pastDue120AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue120Code);
            var pastDue150AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue150Code);
            var pastDue180AgingDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue180Code);
            var pastDue180PlusDetails = agingReport.AgingDetails.First(x => x.Code == AgingDetailsConstants.PastDue180PlusCode);

            dueAgingDetails.Amount.ShouldBe(dueLoan.Amount);
            processingAgingDetails.Amount.ShouldBe(payment);
            pendingAgingDetails.Amount.ShouldBe(pendingLoan.Amount);
            pastDue30AgingDetails.Amount.ShouldBe(pastDue30Loan.Amount);
            pastDue60AgingDetails.Amount.ShouldBe(pastDue60Loan.Amount - payment);
            pastDue90AgingDetails.Amount.ShouldBe(pastDue90Loan.Amount);
            pastDue120AgingDetails.Amount.ShouldBe(pastDue120Loan.Amount);
            pastDue150AgingDetails.Amount.ShouldBe(pastDue150Loan.Amount);
            pastDue180AgingDetails.Amount.ShouldBe(pastDue180Loan.Amount);
            pastDue180PlusDetails.Amount.ShouldBe(pastDue180PlusLoan.Amount);

            agingReport.AgingDetails.ShouldAllBe(x => x.CompanyCount == 1);
            agingReport.CompanyCount.ShouldBe(1);
            agingReport.LoanCount.ShouldBe(loans.Count());
        }

        [Fact]
        public async Task GetAgingReportByCompanyIdScenario_InvalidCompanyId()
        {
            var agingReport = await GetAgingReportByCompanyId("12345678");

            agingReport.LoanCount.ShouldBe(0);
            agingReport.CompanyCount.ShouldBe(0);
            agingReport.AgingDetails.ShouldAllBe(x => x.LoanCount == 0);
            agingReport.AgingDetails.ShouldAllBe(x => x.CompanyCount == 0);
        }
    }
}
