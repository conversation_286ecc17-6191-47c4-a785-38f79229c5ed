﻿using BlueTape.LS.DTOs.Loan;
using BlueTape.Services.ARS.IntegrationTests.Constants;

namespace BlueTape.Services.ARS.IntegrationTests.Dtos
{
    public static class LoanDtos
    {
        public static readonly CreateLoanDto[] CreateLoanDtos = new CreateLoanDto[]
        {
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920451",
                EinHash = "EinHash",
                Amount = 5100
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920452",
                EinHash = "EinHash",
                Amount = 5200
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920453",
                EinHash = "EinHash",
                Amount = 5300
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920454",
                EinHash = "EinHash",
                Amount = 5400
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920455",
                EinHash = "EinHash",
                Amount = 5500
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920456",
                EinHash = "EinHash",
                Amount = 5600
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920457",
                EinHash = "EinHash",
                Amount = 5700
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920458",
                EinHash = "EinHash",
                Amount = 5800
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920459",
                EinHash = "EinHash",
                Amount = 5900
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = "6494628189540e3335920460",
                EinHash = "EinHash",
                Amount = 6000
            }
        };

        public static readonly CreateLoanDto[] CreateLoanDtosWithSameCompanyId = new CreateLoanDto[]
        {
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5100
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5200
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5300
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5400
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5500
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5600
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5700
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5800
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5900
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3476b10d-f7c7-492d-a986-a6e3b3b87b43"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 6000
            }
        };

        public static readonly CreateLoanDto[] CreateLoanDtosForAgingLoanReports = new CreateLoanDto[]
        {
            new()
            {
                LoanTemplateId = Guid.Parse("7ce0034d-900e-4ce0-9585-e9dd19438878"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 7000
            },
            new()
            {
                LoanTemplateId = Guid.Parse("7ce0034d-900e-4ce0-9585-e9dd17338878"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 13000
            },
            new()
            {
                LoanTemplateId = Guid.Parse("7ce0034d-800e-4ce0-9585-e9dd17338879"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 10000
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3ee4cbd6-935a-436e-b2d1-041ac9a5dc12"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5400
            },
            new()
            {
                LoanTemplateId = Guid.Parse("7ce0034d-800e-4ce0-9585-e9dd17338897"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5500
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3ee4cbd6-935a-436e-b2d1-041ac9a5dc33"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5600
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3ee4cbd6-935a-436e-b2d1-041ac9a5dc11"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5700
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3ee4cbd6-935a-436e-b2d1-041ac9a5dc12"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5800
            },
            new()
            {
                LoanTemplateId = Guid.Parse("7ce0034d-800e-4ce0-9585-e9dd17338879"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 5900
            },
            new()
            {
                LoanTemplateId = Guid.Parse("3ee4cbd6-935a-436e-b2d1-041ac9a5dc12"),
                CompanyId = TestConstants.CompanyId,
                EinHash = "EinHash",
                Amount = 6000
            }
        };
    }
}
