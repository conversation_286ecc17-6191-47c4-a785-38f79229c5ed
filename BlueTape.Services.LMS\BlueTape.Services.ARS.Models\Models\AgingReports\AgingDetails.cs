﻿namespace BlueTape.Services.ARS.Models.Models.AgingReports
{
    public class AgingDetails
    {
        public string? Name { get; set; }
        public string? Code { get; set; }
        public decimal Amount { get; set; }
        public int LoanCount { get; set; }
        public int CompanyCount { get; set; }
        public IEnumerable<Guid> LoansIds { get; set; } = Enumerable.Empty<Guid>();
    }
}
