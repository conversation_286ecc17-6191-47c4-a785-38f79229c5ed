using BlueTape.Services.ARS.Models.Models.AgingReports;
using BlueTape.Services.ARS.Tests.Constants;

namespace BlueTape.Services.ARS.Tests.Models.AgingReportDetails
{
    public static class ValidAgingDetails
    {
        public static readonly List<AgingDetails> AgingDetails = new List<AgingDetails>()
        {
            new()
            {
                Name = AgingDetailsConstants.PendingName,
                Code = AgingDetailsConstants.PendingCode,
                Amount = 3500,
                LoanCount = 9,
                CompanyCount = 1
},
            new()
            {
                Name = AgingDetailsConstants.DueName,
                Code = AgingDetailsConstants.DueCode,
                Amount = 120,
                LoanCount = 1,
                CompanyCount = 1
            },
            new()
            {
                Name = AgingDetailsConstants.ProcessingName,
                Code = AgingDetailsConstants.ProcessingCode,
                Amount = 1000,
                LoanCount = 2,
                CompanyCount = 1
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue30Name,
                Code = AgingDetailsConstants.PastDue30Code,
                Amount = 2000,
                LoanCount = 7,
                CompanyCount = 1
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue60Name,
                Code = AgingDetailsConstants.PastDue60Code,
                Amount = 1500,
                LoanCount = 5,
                CompanyCount = 1
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue90Name,
                Code = AgingDetailsConstants.PastDue90Code,
                Amount = 10450.6m,
                LoanCount =  5,
                CompanyCount = 1
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue120Name,
                Code = AgingDetailsConstants.PastDue120Code,
                Amount =  12003.6m,
                LoanCount = 7,
                CompanyCount = 1
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue150Name,
                Code = AgingDetailsConstants.PastDue150Code,
                Amount =  2599.6m,
                LoanCount = 3,
                CompanyCount = 1
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue180Name,
                Code = AgingDetailsConstants.PastDue180Code,
                Amount =  1046.6m,
                LoanCount = 1,
                CompanyCount = 1
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue180PlusName,
                Code = AgingDetailsConstants.PastDue180PlusCode,
                Amount =  2000,
                LoanCount = 4,
                CompanyCount = 1
            }
        };

        public static readonly List<AgingDetails> EmptyAgingDetails = new List<AgingDetails>
        {
            new()
            {
                Name = AgingDetailsConstants.PendingName,
                Code = AgingDetailsConstants.PendingCode,
                Amount = 0,
                LoanCount = 0,
                CompanyCount = 0
},
            new()
            {
                Name = AgingDetailsConstants.DueName,
                Code = AgingDetailsConstants.DueCode,
                Amount = 0,
                LoanCount = 0,
                CompanyCount = 0
            },
            new()
            {
                Name = AgingDetailsConstants.ProcessingName,
                Code = AgingDetailsConstants.ProcessingCode,
                Amount = 0,
                LoanCount = 0,
                CompanyCount = 0
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue30Name,
                Code = AgingDetailsConstants.PastDue30Code,
                Amount = 0,
                LoanCount = 0,
                CompanyCount = 0
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue60Name,
                Code = AgingDetailsConstants.PastDue60Code,
                Amount = 0,
                LoanCount = 0,
                CompanyCount = 0
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue90Name,
                Code = AgingDetailsConstants.PastDue90Code,
                Amount = 0,
                LoanCount = 0,
                CompanyCount = 0
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue120Name,
                Code = AgingDetailsConstants.PastDue120Code,
                Amount = 0,
                LoanCount = 0,
                CompanyCount = 0
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue150Name,
                Code = AgingDetailsConstants.PastDue150Code,
                Amount =  2599.6m,
                LoanCount = 0,
                CompanyCount = 0
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue180Name,
                Code = AgingDetailsConstants.PastDue180Code,
                Amount = 0,
                LoanCount = 0,
                CompanyCount = 0
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue180PlusName,
                Code = AgingDetailsConstants.PastDue180PlusCode,
                Amount = 0,
                LoanCount = 0,
                CompanyCount = 0
            }
        };
    }
}
