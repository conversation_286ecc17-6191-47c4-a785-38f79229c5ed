﻿using BlueTape.Services.ARS.Models.Models.AgingLoanReports;
using BlueTape.Services.ARS.Tests.Constants;

namespace BlueTape.Services.ARS.Tests.Models.AgingReportDetails
{
    public static class ValidAgingLoanDetails
    {
        public static readonly List<AgingLoanDetails> AgingLoanDetails = new List<AgingLoanDetails>()
        {
            new()
            {
                Name = AgingDetailsConstants.PendingName,
                Code = AgingDetailsConstants.PendingCode,
                Amount = 3500
            },
            new()
            {
                Name = AgingDetailsConstants.DueName,
                Code = AgingDetailsConstants.DueCode,
                Amount = 120
            },
            new()
            {
                Name = AgingDetailsConstants.ProcessingName,
                Code = AgingDetailsConstants.ProcessingCode,
                Amount = 1000
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue30Name,
                Code = AgingDetailsConstants.PastDue30Code,
                Amount = 2000
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue60Name,
                Code = AgingDetailsConstants.PastDue60Code,
                Amount = 1500
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue90Name,
                Code = AgingDetailsConstants.PastDue90Code,
                Amount = 10450.6m
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue120Name,
                Code = AgingDetailsConstants.PastDue120Code,
                Amount = 12003.6m
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue150Name,
                Code = AgingDetailsConstants.PastDue150Code,
                Amount = 2599.6m
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue180Name,
                Code = AgingDetailsConstants.PastDue180Code,
                Amount = 1046.6m
            },
            new()
            {
                Name = AgingDetailsConstants.PastDue180PlusName,
                Code = AgingDetailsConstants.PastDue180PlusCode,
                Amount = 2000
            }
        };
    }
}
