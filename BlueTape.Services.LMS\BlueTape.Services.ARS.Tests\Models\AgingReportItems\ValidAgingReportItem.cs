﻿using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;

namespace BlueTape.Services.ARS.Tests.Models.AgingReportItems
{
    public static class ValidAgingReportItem
    {
        public static readonly AgingReportItem DueAgingReportItem = new()
        {
            Type = AgingItemsConstants.Due,
        };

        public static readonly AgingReportItem PastDueAgingReportItem = new()
        {
            Type = AgingItemsConstants.PastDue,
            FromDate = 1,
            ToDate = 30
        };

        public static readonly AgingReportItem PastDuePlusAgingReportItem = new()
        {
            Type = AgingItemsConstants.PastDuePlus,
            FromDate = 180,
        };

        public static readonly AgingReportItem PendingAgingReportItem = new()
        {
            Type = AgingItemsConstants.Pending,
        };

        public static readonly AgingReportItem ProcessingAgingReportItem = new()
        {
            Type = AgingItemsConstants.Processing,
        };
    }
}
