using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;

namespace BlueTape.Services.ARS.Tests.Models.AgingReportItems
{
    public static class ValidAgingReportItems
    {
        public static readonly AgingReportItem[] AgingReportItems = new[]
        {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.Pending
                },

                new AgingReportItem()
                {
                    Type = AgingItemsConstants.Due
                },
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.Processing
                },
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 1,
                    ToDate = 30
                },
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 31,
                    ToDate = 60
                },
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 61,
                    ToDate = 90
                },
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 91,
                    ToDate = 120
                },
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 121,
                    ToDate = 150
                },
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 151,
                    ToDate = 180
                },
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDuePlus,
                    FromDate = 180
                }
        };
    }
}
