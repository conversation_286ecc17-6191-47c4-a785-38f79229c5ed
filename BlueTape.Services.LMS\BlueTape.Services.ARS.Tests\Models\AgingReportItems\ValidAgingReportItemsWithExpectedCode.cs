using BlueTape.Services.ARS.Application.Constants;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;
using System.Collections;

namespace BlueTape.Services.ARS.Tests.Models.AgingReportItems
{
    public class ValidAgingReportItemsWithExpectedCode : IEnumerable<object[]>
    {
        private readonly List<object[]> _data = new()
        {
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.Pending
                },
                AgingItemsConstants.Pending
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.Due
                },
                AgingItemsConstants.Due
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.Processing
                },
                AgingItemsConstants.Processing
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 1,
                    ToDate = 30
                },
                AgingDetailsConstants.PastDue30Code
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 31,
                    ToDate = 60
                },
                AgingDetailsConstants.PastDue60Code
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 61,
                    ToDate = 90
                },
                AgingDetailsConstants.PastDue90Code
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 91,
                    ToDate = 120
                },
                AgingDetailsConstants.PastDue120Code
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 121,
                    ToDate = 150
                },
                AgingDetailsConstants.PastDue150Code
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 151,
                    ToDate = 180
                },
                AgingDetailsConstants.PastDue180Code
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDuePlus,
                    FromDate = 180
                },
                AgingDetailsConstants.PastDue180PlusCode
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = string.Empty
            },
                string.Empty
            }
        };
        public IEnumerator<object[]> GetEnumerator() => _data.GetEnumerator();

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }
}
