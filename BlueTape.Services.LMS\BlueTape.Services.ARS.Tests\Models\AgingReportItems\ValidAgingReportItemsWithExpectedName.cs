using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;
using System.Collections;

namespace BlueTape.Services.ARS.Tests.Models.AgingReportItems
{
    public class ValidAgingReportItemsWithExpectedName : IEnumerable<object[]>
    {
        private readonly List<object[]> _data = new()
        {
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingDetailsConstants.PendingCode
                },
                AgingDetailsConstants.PendingName
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingDetailsConstants.DueCode
                },
                AgingDetailsConstants.DueName
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingDetailsConstants.ProcessingCode
                },
                AgingDetailsConstants.ProcessingName
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 1,
                    ToDate = 30
                },
                AgingDetailsConstants.PastDue30Name
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 31,
                    ToDate = 60
                },
                AgingDetailsConstants.PastDue60Name
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 61,
                    ToDate = 90
                },
                AgingDetailsConstants.PastDue90Name
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 91,
                    ToDate = 120
                },
                AgingDetailsConstants.PastDue120Name
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 121,
                    ToDate = 150
                },
                AgingDetailsConstants.PastDue150Name
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDue,
                    FromDate = 151,
                    ToDate = 180
                },
                AgingDetailsConstants.PastDue180Name
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = AgingItemsConstants.PastDuePlus,
                    FromDate = 180
                },
                AgingDetailsConstants.PastDue180PlusName
            },
            new object[]
            {
                new AgingReportItem()
                {
                    Type = string.Empty
            },
                string.Empty
            }
        };
        public IEnumerator<object[]> GetEnumerator() => _data.GetEnumerator();

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }
}
