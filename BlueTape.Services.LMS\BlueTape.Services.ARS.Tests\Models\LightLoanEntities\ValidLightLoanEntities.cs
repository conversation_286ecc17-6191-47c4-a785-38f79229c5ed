﻿using BlueTape.Services.ARS.Tests.Constants;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.ARS.Tests.Models.LightLoanEntities
{
    public static class ValidLightLoanEntities
    {
        public static readonly LightLoanEntity[] LightLoanEntities = new LightLoanEntity[]
        {
            new()
            {
                Id = Guid.NewGuid(),
                CompanyId = TestConstants.CompanyId,
                LoanReceivables = new List<LightLoanReceivableEntity>
                {
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 35m,
                        ExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 100m,
                        ExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow)
                    }
                }
            },
            new ()
            {
                Id = Guid.NewGuid(),
                CompanyId = TestConstants.CompanyId,
                LoanReceivables = new List<LightLoanReceivableEntity>
                {
                    new()
                    {
                        Status =LoanReceivableStatus.Paid,
                        OutstandingAmount = 100,
                        ExpectedDate = new DateOnly(2022, 10, 1)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Paid,
                        OutstandingAmount = 50,
                        ExpectedDate = new DateOnly(2022, 10, 15)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Paid,
                        OutstandingAmount = 25,
                        ExpectedDate = new DateOnly(2022, 11, 5)
                    }
                }
            },
            new ()
            {
                Id = Guid.NewGuid(),
                CompanyId = TestConstants.CompanyId,
                LoanReceivables = new List<LightLoanReceivableEntity>
                {
                    new()
                    {
                        Status =LoanReceivableStatus.Paid,
                        OutstandingAmount = 120,
                        ExpectedDate = new DateOnly(2022, 11, 20)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Paid,
                        OutstandingAmount = 70,
                        ExpectedDate = new DateOnly(2022, 11, 30)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Paid,
                        OutstandingAmount = 10,
                        ExpectedDate = new DateOnly(2022, 12, 7)
                    }
                }
            },
            new()
            {
                Id = Guid.NewGuid(),
                CompanyId = TestConstants.CompanyId,
                LoanReceivables = new List<LightLoanReceivableEntity>
                {
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 7,
                        ExpectedDate = new DateOnly(2022, 12, 18)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 10,
ExpectedDate = new DateOnly(2022, 11, 28)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 2.64m,
                        ExpectedDate = new DateOnly(2022, 11, 28)
                    }
                }
            },
            new()
            {
                Id = Guid.NewGuid(),
                CompanyId = TestConstants.CompanyId,
                LoanReceivables = new List<LightLoanReceivableEntity>
                {
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 100,
                        ExpectedDate = new DateOnly(2022, 10, 01)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 35,
                        ExpectedDate = new DateOnly(2022, 10, 12)
                    },
                    new()
                    {
                    Status =LoanReceivableStatus.Late,
                    OutstandingAmount = 35,
                    ExpectedDate = new DateOnly(2022, 10, 27)
                }
                }
            },
            new()
            {
                Id = Guid.NewGuid(),
                CompanyId = TestConstants.CompanyId,
                LoanReceivables = new List<LightLoanReceivableEntity>
                {
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 35,
                        ExpectedDate = new DateOnly(2022, 08, 01)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 35,
                        ExpectedDate = new DateOnly(2022, 08, 12)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 35,
                        ExpectedDate = new DateOnly(2022, 08, 27)
                    }
                }
            },
                new()
                {
                Id = Guid.NewGuid(),
                CompanyId = TestConstants.CompanyId,
                LoanReceivables = new List<LightLoanReceivableEntity>
                {
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 25,
                        ExpectedDate = new DateOnly(2022, 07, 01)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 25,
                        ExpectedDate = new DateOnly(2022, 07, 12)
                    },
                    new()
                    {
                        Status =LoanReceivableStatus.Late,
                        OutstandingAmount = 25,
                        ExpectedDate = new DateOnly(2022, 07, 27)
                    }
                }
            },
                new()
                {
                    Id = Guid.NewGuid(),
                    CompanyId = TestConstants.CompanyId,
                    LoanReceivables = new List<LightLoanReceivableEntity>
                    {
                        new()
                        {
                            Status =LoanReceivableStatus.Late,
                            OutstandingAmount = 45,
                            ExpectedDate = new DateOnly(2022, 06, 01)
                        },
                        new()
                        {
                            Status =LoanReceivableStatus.Late,
                            OutstandingAmount = 45,
                            ExpectedDate = new DateOnly(2022, 06, 12)
                        },
                        new()
                        {
                            Status =LoanReceivableStatus.Late,
                            OutstandingAmount = 45,
                            ExpectedDate = new DateOnly(2022, 06, 27)
                        }
                    }
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    CompanyId = TestConstants.CompanyId,
                    LoanReceivables = new List<LightLoanReceivableEntity>
                    {
                        new()
                        {
                            Status =LoanReceivableStatus.Paid,
                            OutstandingAmount = 45,
                            ExpectedDate = new DateOnly(2023, 06, 01)
                        },
                        new()
                        {
                            Status =LoanReceivableStatus.Paid,
                            OutstandingAmount = 45,
                            ExpectedDate = new DateOnly(2023, 06, 12)
                        },
                        new()
                        {
                            Status =LoanReceivableStatus.Paid,
                            OutstandingAmount = 45,
                            ExpectedDate = new DateOnly(2023, 06, 27)
                        }
                    }
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    CompanyId = TestConstants.CompanyId,
                    LoanReceivables = new List<LightLoanReceivableEntity>
                    {
                        new()
                        {
                            Status =LoanReceivableStatus.Late,
                            OutstandingAmount = 65,
                            ExpectedDate = new DateOnly(2023, 07, 01)
                        },
                        new()
                        {
                            Status =LoanReceivableStatus.Late,
                            OutstandingAmount = 65,
                            ExpectedDate = new DateOnly(2023, 07, 12)
                        },
                        new()
                        {
                            Status =LoanReceivableStatus.Late,
                            OutstandingAmount = 65,
                            ExpectedDate = new DateOnly(2023, 07, 27)
                        }
                    },
                    Payments = new List<LightPaymentEntity>
                    {
    new() { Status = PaymentStatus.Processing}
                    }
                },
        };
    }
}
