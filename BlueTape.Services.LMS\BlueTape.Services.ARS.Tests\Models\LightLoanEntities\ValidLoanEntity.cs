﻿using BlueTape.Services.ARS.Tests.Constants;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.ARS.Tests.Models.LightLoanEntities
{
    public static class ValidLoanEntity
    {
        public static readonly LightLoanEntity ValidSingleLoanEntity = new()
        {
            CompanyId = TestConstants.CompanyId,

            Id = Guid.NewGuid(),
            LoanReceivables = new()
            {
                new LightLoanReceivableEntity() //Pending 
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 10, 1),
                    OutstandingAmount = 100.232323m,
                    ScheduleStatus = ScheduleStatus.Current

                },
                new LightLoanReceivableEntity() //PastDuePLus 
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2022, 10, 1),
                    OutstandingAmount = 100.23232m,
                    ScheduleStatus = ScheduleStatus.Current

                },
                new LightLoanReceivableEntity() // Pending
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 10, 15),
                    OutstandingAmount = 200
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 5),
                    OutstandingAmount = 300
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 20),
                    OutstandingAmount = 550
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 30),
                    OutstandingAmount = 800
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 12, 7),
                    OutstandingAmount = 1300
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 12, 18),
                    OutstandingAmount = 2100
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Paid,
                    ExpectedDate = new DateOnly(2023, 12, 31),
                    OutstandingAmount = 10
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Current,
                    Status = LoanReceivableStatus.Canceled,
                    ExpectedDate = new DateOnly(2023, 12, 31),
                    OutstandingAmount = 10
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Replanned,
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 20),
                    OutstandingAmount = 55
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Postponed,
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 20),
                    OutstandingAmount = 55
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Rescheduled,
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 20),
                    OutstandingAmount = 55
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Replanned,
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 10, 1),
                    OutstandingAmount = 55
                }
            },
            Payments = new()
            {
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Processing,
                    Amount = 127,
                    Date = new DateOnly(2023, 11,3)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Success,
                    Amount = 100,
                    Date = new DateOnly(2023, 11,3)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Processing,
                    Amount = 10,
                    Date = new DateOnly(2023, 11,3)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Success,
                    Amount = 127,
                    Date = new DateOnly(2023, 11,3)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Rejected,
                    Amount = 127,
                    Date = new DateOnly(2023, 12,25)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Processing,
                    Amount = 127,
                    Date = new DateOnly(2023, 12,25)
                }
            }
        };

        public static readonly LightLoanEntity ValidSingleLoanEntityPastDuePlus = new()
        {
            CompanyId = TestConstants.CompanyId,

            Id = Guid.NewGuid(),
            LoanReceivables = new()
            {
                new LightLoanReceivableEntity() //Pending 
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 10, 1),
                    OutstandingAmount = 100.232323m,
                    ScheduleStatus = ScheduleStatus.Current

                },
                new LightLoanReceivableEntity() //PastDuePLus 
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2022, 10, 1),
                    OutstandingAmount = 300.23232m,
                    ScheduleStatus = ScheduleStatus.Current

                },
                new LightLoanReceivableEntity() // Pending
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 10, 15),
                    OutstandingAmount = 200
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 5),
                    OutstandingAmount = 300
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 20),
                    OutstandingAmount = 550
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 30),
                    OutstandingAmount = 800
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 12, 7),
                    OutstandingAmount = 1300
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 12, 18),
                    OutstandingAmount = 2100
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Paid,
                    ExpectedDate = new DateOnly(2023, 12, 31),
                    OutstandingAmount = 10
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Current,
                    Status = LoanReceivableStatus.Canceled,
                    ExpectedDate = new DateOnly(2023, 12, 31),
                    OutstandingAmount = 10
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Replanned,
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 20),
                    OutstandingAmount = 55
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Postponed,
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 20),
                    OutstandingAmount = 55
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Rescheduled,
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 11, 20),
                    OutstandingAmount = 55
                },
                new LightLoanReceivableEntity()
                {
                    ScheduleStatus = ScheduleStatus.Replanned,
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 10, 1),
                    OutstandingAmount = 55
                }
            },
            Payments = new()
            {
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Rejected,
                    Amount = 127,
                    Date = new DateOnly(2023, 11,3)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Processing,
                    Amount = 10,
                    Date = new DateOnly(2023, 11,3)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Processing,
                    Amount = 100,
                    Date = new DateOnly(2023, 11,3)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Rejected,
                    Amount = 127,
                    Date = new DateOnly(2023, 11,3)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Rejected,
                    Amount = 127,
                    Date = new DateOnly(2023, 12,25)
                },
                new LightPaymentEntity()
                {
                    Status = PaymentStatus.Rejected,
                    Amount = 127,
                    Date = new DateOnly(2023, 12,25)
                }
            }
        };

        public static readonly LightLoanEntity ValidSingleLoanEntityWithoutProcessingPayments = new()
        {
            CompanyId = TestConstants.CompanyId,
            Id = Guid.NewGuid(),
            LoanReceivables = new()
            {
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 10, 1),
                    OutstandingAmount = 100
                },
                new LightLoanReceivableEntity()
                {
                    Status = LoanReceivableStatus.Pending,
                    ExpectedDate = new DateOnly(2023, 10, 15),
                    OutstandingAmount = 200
                }
            },
        };
    }
}
