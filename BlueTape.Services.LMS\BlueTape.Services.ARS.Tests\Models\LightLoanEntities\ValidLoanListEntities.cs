﻿using BlueTape.Services.ARS.Tests.Constants;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.ARS.Tests.Models.LightLoanEntities
{
    public static class ValidLoanListEntities
    {
        public static readonly LightLoanEntity[] ValidListLoanEntity = new[]
        {
            new LightLoanEntity(){
                CompanyId = TestConstants.CompanyId,
                Id = Guid.NewGuid(),
                LoanReceivables = new()
                {
                    new LightLoanReceivableEntity() //Pending 
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2024, 01, 01),
                        OutstandingAmount = 100.232323m,
                        ScheduleStatus = ScheduleStatus.Current

                    },
                    new LightLoanReceivableEntity() // Pending
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2024, 01, 01),
                        OutstandingAmount = 200,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                    new LightLoanReceivableEntity() //PastDue
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2023, 11, 20),
                        OutstandingAmount = 3000,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                    new LightLoanReceivableEntity() //PastDue
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2023, 11, 20),
                        OutstandingAmount = 2500,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                     new LightLoanReceivableEntity() //PastDuePlus
                       {
                       Status = LoanReceivableStatus.Pending,
                       ExpectedDate = new DateOnly(2022, 12, 05),
                       OutstandingAmount = 5000,
                       ScheduleStatus = ScheduleStatus.Current
                       },
                       new LightLoanReceivableEntity() //PastDuePlus
                       {
                       Status = LoanReceivableStatus.Pending,
                       ExpectedDate = new DateOnly(2022, 12, 05),
                       OutstandingAmount = 2500.1234m,
                       ScheduleStatus = ScheduleStatus.Current
                       },
                     new LightLoanReceivableEntity() //Due
                     {
                         Status = LoanReceivableStatus.Pending,
                         ExpectedDate = new DateOnly(2023, 10, 01),
                         OutstandingAmount = 4700,
                         ScheduleStatus = ScheduleStatus.Current
                     },
                     new LightLoanReceivableEntity() //Due
                     {
                         Status = LoanReceivableStatus.Pending,
                         ExpectedDate = new DateOnly(2023, 10, 01),
                         OutstandingAmount = 10.375m,
                         ScheduleStatus = ScheduleStatus.Current
                     },
                },
                Payments = new()
                {
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 300
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 400
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 500
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 1000
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 2000
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 1000
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Rejected,
                        Amount = 500
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Success,
                        Amount = 500
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Canceled,
                        Amount = 500
                    },
                }
            },
            new LightLoanEntity{
                CompanyId = TestConstants.CompanyId,
                Id = Guid.NewGuid(),
                LoanReceivables = new()
                {
                    new LightLoanReceivableEntity() //Pending 
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2024, 01, 01),
                        OutstandingAmount = 100.232323m,
                        ScheduleStatus = ScheduleStatus.Current

                    },
                    new LightLoanReceivableEntity() // Pending
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2024, 01, 01),
                        OutstandingAmount = 200,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                    new LightLoanReceivableEntity() //PastDue
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2023, 11, 20),
                        OutstandingAmount = 3000,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                    new LightLoanReceivableEntity() //PastDue
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2023, 11, 20),
                        OutstandingAmount = 2500,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                    new LightLoanReceivableEntity() //PastDuePlus
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2022, 12, 05),
                        OutstandingAmount = 5000,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                    new LightLoanReceivableEntity() //PastDuePlus
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2022, 12, 05),
                        OutstandingAmount = 2500.1234m,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                    new LightLoanReceivableEntity() //Due
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2023, 10, 01),
                        OutstandingAmount = 4700,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                    new LightLoanReceivableEntity() //Due
                    {
                        Status = LoanReceivableStatus.Pending,
                        ExpectedDate = new DateOnly(2023, 10, 01),
                        OutstandingAmount = 15.8452m,
                        ScheduleStatus = ScheduleStatus.Current
                    },
                },
                Payments = new()
                {
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 300
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 400
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 500
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 1000
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 2000
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Processing,
                        Amount = 1000
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Rejected,
                        Amount = 500
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Success,
                        Amount = 500
                    },
                    new LightPaymentEntity()
                    {
                        Status = PaymentStatus.Canceled,
                        Amount = 500
                    },
                }
            }
        };
    }
}
