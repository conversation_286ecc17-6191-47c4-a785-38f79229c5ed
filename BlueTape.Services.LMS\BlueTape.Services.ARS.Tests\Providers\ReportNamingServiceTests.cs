using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Providers;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Models.AgingReportItems;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.Tests.Providers
{
    public class ReportNamingServiceTests
    {
        private readonly ReportNamingService _reportNamingService = new();

        [Theory]
        [ClassData(typeof(ValidAgingReportItemsWithExpectedName))]
        public void GetCodeReturnsCurrentName(AgingReportItem item, string result)
        {
            result.ShouldBe(_reportNamingService.CreateName(item));
        }

        [Theory]
        [ClassData(typeof(ValidAgingReportItemsWithExpectedCode))]
        public void GetNameReturnsCurrentCode(AgingReportItem item, string result)
        {
            result.ShouldBe(_reportNamingService.CreateCode(item));
        }
    }
}