﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Abstractions.Strategies.AgingLoanReports;
using BlueTape.Services.ARS.Application.Services;
using BlueTape.Services.ARS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Models.Models.AgingLoanReports;
using BlueTape.Services.ARS.Tests.Models.AgingReportItems;
using BlueTape.Services.ARS.Tests.Models.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;
using Microsoft.Extensions.Options;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.Tests.Services
{
    public class AgingLoanReportsServiceTests
    {
        private readonly AgingLoanReportsService _agingLoanReportsService;
        private readonly Mock<ILightLoanRepository> _loanRepositoryMock = new();
        private readonly Mock<IAgingStrategiesProvider> _strategyProviderMock = new();
        private readonly IOptions<AgingReportOptions> _agingReportOptions = Options.Create(new AgingReportOptions
        { AgingReportItems = ValidAgingReportItems.AgingReportItems });

        private readonly Mock<IAgingLoanDetailsStrategy> _agingLoanDetailsStrategyMock = new();

        public AgingLoanReportsServiceTests()
        {
            _agingLoanReportsService = new AgingLoanReportsService(_loanRepositoryMock.Object, _agingReportOptions,
                _strategyProviderMock.Object);
        }

        [Fact]
        public async Task GetReportsByCompanyId_HasData_ReturnsAgingLoanReports()
        {
            var lightLoanEntities = ValidLightLoanEntities.LightLoanEntities;
            var loanIds = lightLoanEntities.Select(x => x.Id);
            var product = ProductType.ARAdvance;

            _loanRepositoryMock
                .Setup(x => x.GetByCompanyId(It.IsAny<string>(), product, It.IsAny<string>(), default))
                .ReturnsAsync(lightLoanEntities);

            _strategyProviderMock
                .Setup(x => x.GetAgingLoanDetailsStrategy(It.IsAny<string>()))
                    .Returns(_agingLoanDetailsStrategyMock.Object);

            _agingLoanDetailsStrategyMock
                .Setup(x => x.Create(It.IsAny<AgingReportItem>(), It.IsAny<LightLoanEntity>()))
                    .Returns(It.IsAny<AgingLoanDetails>());

            var agingLoanReports = await _agingLoanReportsService.GetReportsByCompanyId(It.IsAny<string>(), product, It.IsAny<string>(), default);

            agingLoanReports.Count.ShouldBe(lightLoanEntities.Length);
            agingLoanReports.ShouldAllBe(x => loanIds.Contains(x.LoanId));
            agingLoanReports.ShouldAllBe(x => x.AgingDetails!.Count == ValidAgingReportItems.AgingReportItems.Length);
            _loanRepositoryMock.Verify(x => x.GetByCompanyId(It.IsAny<string>(), product, It.IsAny<string>(), default), Times.Once());
            _loanRepositoryMock.VerifyNoOtherCalls();
        }

        [Fact]
        public async Task GetReportsByCompanyId_InvalidData_ShouldDoesNotThrowException()
        {
            _loanRepositoryMock
                .Setup(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<ProductType>(), It.IsAny<string>(), default))
                .ReturnsAsync(new List<LightLoanEntity>());

            _strategyProviderMock
                .Setup(x => x.GetAgingLoanDetailsStrategy(It.IsAny<string>()))
                .Returns(_agingLoanDetailsStrategyMock.Object);

            _agingLoanDetailsStrategyMock
                .Setup(x => x.Create(It.IsAny<AgingReportItem>(), It.IsAny<LightLoanEntity>()))
                .Returns(It.IsAny<AgingLoanDetails>());

            await _agingLoanReportsService.GetReportsByCompanyId(It.IsAny<string>(), It.IsAny<ProductType>(), It.IsAny<string>(), default);
            _loanRepositoryMock.Verify(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<ProductType>(), It.IsAny<string>(), default), Times.Once());
            _loanRepositoryMock.VerifyNoOtherCalls();
        }
    }
}
