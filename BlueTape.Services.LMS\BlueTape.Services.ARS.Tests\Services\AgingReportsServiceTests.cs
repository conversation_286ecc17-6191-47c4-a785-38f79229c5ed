﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Abstractions.Strategies.AgingReports;
using BlueTape.Services.ARS.Application.Services;
using BlueTape.Services.ARS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Models.AgingReportDetails;
using BlueTape.Services.ARS.Tests.Models.AgingReportItems;
using BlueTape.Services.ARS.Tests.Models.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;
using Microsoft.Extensions.Options;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.Tests.Services
{
    public class AgingReportsServiceTests
    {
        private readonly AgingReportsService _agingReportsService;
        private readonly Mock<ILightLoanRepository> _loanRepositoryMock = new();
        private readonly Mock<IAgingStrategiesProvider> _strategyProviderMock = new();
        private readonly IOptions<AgingReportOptions> _agingReportOptions = Options.Create(new AgingReportOptions { AgingReportItems = ValidAgingReportItems.AgingReportItems });
        private readonly Mock<IAgingDetailsStrategy> _agingDetailsStrategyMock = new();

        public AgingReportsServiceTests()
        {
            _agingReportsService = new AgingReportsService(_loanRepositoryMock.Object, _agingReportOptions, _strategyProviderMock.Object);
        }

        [Fact]
        public async Task GetByCompanyId_HasData_ReturnsAgingReport()
        {
            var index = 0;
            var loanEntities = ValidLightLoanEntities.LightLoanEntities;

            _loanRepositoryMock
                .Setup(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<ProductType>(), It.IsAny<string>(), default))
                            .ReturnsAsync(ValidLightLoanEntities.LightLoanEntities);

            _strategyProviderMock
                .Setup(x => x.GetAgingDetailsStrategy(It.IsAny<string>()))
                .Returns(_agingDetailsStrategyMock.Object);

            _agingDetailsStrategyMock
                .Setup(x => x.Create(It.IsAny<AgingReportItem>(), ValidLightLoanEntities.LightLoanEntities))
                .Returns(() => ValidAgingDetails.AgingDetails[Interlocked.Increment(ref index) - 1]);

            var agingReport = await _agingReportsService.GetByCompanyId(It.IsAny<string>(), It.IsAny<ProductType>(), It.IsAny<string>(), default);

            agingReport.AgingDetails!.Count.ShouldBe(ValidAgingDetails.AgingDetails.Count);
            agingReport.CompanyCount.ShouldBe(1);
            agingReport.LoanCount.ShouldBe(loanEntities.Length);
            _strategyProviderMock.Verify(x => x.GetAgingDetailsStrategy(It.IsAny<string>()), Times.Exactly(ValidAgingReportItems.AgingReportItems.Length));
            _agingDetailsStrategyMock.Verify(x => x.Create(It.IsAny<AgingReportItem>(), ValidLightLoanEntities.LightLoanEntities), Times.Exactly(ValidAgingReportItems.AgingReportItems.Length));
            _loanRepositoryMock.Verify(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<ProductType>(), It.IsAny<string>(), default), Times.Once());
            _loanRepositoryMock.VerifyNoOtherCalls();
            _strategyProviderMock.VerifyNoOtherCalls();
        }

        [Fact]
        public async Task GetByCompanyId_EmptyLoanList_ReturnsAgingReport()
        {
            var index = 0;

            _loanRepositoryMock
                .Setup(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<ProductType>(), It.IsAny<string>(), default))
                .ReturnsAsync(new List<LightLoanEntity>());

            _strategyProviderMock
                .Setup(x => x.GetAgingDetailsStrategy(It.IsAny<string>()))
                .Returns(_agingDetailsStrategyMock.Object);

            _agingDetailsStrategyMock
                .Setup(x => x.Create(It.IsAny<AgingReportItem>(), It.IsAny<IReadOnlyCollection<LightLoanEntity>>()))
                .Returns(() => ValidAgingDetails.EmptyAgingDetails[Interlocked.Increment(ref index) - 1]);

            var agingReport = await _agingReportsService.GetByCompanyId(It.IsAny<string>(), It.IsAny<ProductType>(), It.IsAny<string>(), default);

            agingReport.AgingDetails!.Count.ShouldBe(ValidAgingDetails.EmptyAgingDetails.Count);
            agingReport.CompanyCount.ShouldBe(0);
            agingReport.LoanCount.ShouldBe(0);
            _strategyProviderMock.Verify(x => x.GetAgingDetailsStrategy(It.IsAny<string>()), Times.Exactly(ValidAgingReportItems.AgingReportItems.Length));
            _agingDetailsStrategyMock.Verify(x => x.Create(It.IsAny<AgingReportItem>(), It.IsAny<IReadOnlyCollection<LightLoanEntity>>()), Times.Exactly(ValidAgingReportItems.AgingReportItems.Length));
            _loanRepositoryMock.Verify(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<ProductType>(), It.IsAny<string>(), default), Times.Once());
            _loanRepositoryMock.VerifyNoOtherCalls();
            _strategyProviderMock.VerifyNoOtherCalls();
        }

        [Fact]
        public async Task GetAllReturnsAgingReport()
        {
            var index = 0;
            var loanEntities = ValidLightLoanEntities.LightLoanEntities;

            _loanRepositoryMock
                .Setup(x => x.GetAll(It.IsAny<ProductType>(), It.IsAny<string>(), default))
                .ReturnsAsync(ValidLightLoanEntities.LightLoanEntities);

            _strategyProviderMock
                .Setup(x => x.GetAgingDetailsStrategy(It.IsAny<string>()))
                .Returns(_agingDetailsStrategyMock.Object);

            _agingDetailsStrategyMock
                .Setup(x => x.Create(It.IsAny<AgingReportItem>(), ValidLightLoanEntities.LightLoanEntities))
                .Returns(() => ValidAgingDetails.AgingDetails[Interlocked.Increment(ref index) - 1]);

            var agingReport = await _agingReportsService.GetAll(It.IsAny<ProductType>(), It.IsAny<string>(), default);

            agingReport.AgingDetails!.Count.ShouldBe(ValidAgingDetails.AgingDetails.Count);
            agingReport.CompanyCount.ShouldBe(loanEntities.Select(x => x.CompanyId).Distinct().Count());
            agingReport.LoanCount.ShouldBe(loanEntities.Length);
            _strategyProviderMock.Verify(x => x.GetAgingDetailsStrategy(It.IsAny<string>()), Times.Exactly(ValidAgingReportItems.AgingReportItems.Length));
            _agingDetailsStrategyMock.Verify(x => x.Create(It.IsAny<AgingReportItem>(), ValidLightLoanEntities.LightLoanEntities), Times.Exactly(ValidAgingReportItems.AgingReportItems.Length));
            _loanRepositoryMock.Verify(x => x.GetAll(It.IsAny<ProductType>(), It.IsAny<string>(), default), Times.Once());
            _loanRepositoryMock.VerifyNoOtherCalls();
            _strategyProviderMock.VerifyNoOtherCalls();
        }
    }
}
