﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingLoanReports;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;
using BlueTape.Services.ARS.Tests.Models.AgingReportItems;
using BlueTape.Services.ARS.Tests.Models.LightLoanEntities;
using BlueTape.Utilities.Providers;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.Tests.Strategies.AgingLoanReports
{
    public class DueLoanStrategyTests
    {
        private readonly DueLoanStrategy _dueLoanStrategy;
        private readonly Mock<IDateProvider> _dateProviderMock = new();
        private readonly Mock<IReportNamingService> _reportNamingServiceMock = new();

        public DueLoanStrategyTests()
        {
            _dueLoanStrategy = new DueLoanStrategy(_dateProviderMock.Object, _reportNamingServiceMock.Object);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetails()
        {
            var lightLoanEntity = ValidLoanEntity.ValidSingleLoanEntity;
            var agingReportItem = ValidAgingReportItem.DueAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2023, 10, 01));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.Due);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns(AgingDetailsConstants.DueName);

            var result = _dueLoanStrategy.Create(agingReportItem, lightLoanEntity);

            VerifyMockCalls();

            result.ShouldNotBeNull();
            result.Name.ShouldBe(AgingDetailsConstants.DueName);
            result.Code.ShouldBe(AgingItemsConstants.Due);
            result.Amount.ShouldBe(100.23m);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetailsZeroAmount()
        {
            var lightLoanEntity = ValidLoanEntity.ValidSingleLoanEntity;
            var agingReportItem = ValidAgingReportItem.DueAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2023, 1, 10));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.Due);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns(AgingDetailsConstants.DueName);

            var result = _dueLoanStrategy.Create(agingReportItem, lightLoanEntity);

            VerifyMockCalls();

            result.ShouldNotBeNull();
            result.Name.ShouldBe(AgingDetailsConstants.DueName);
            result.Code.ShouldBe(AgingItemsConstants.Due);
            result.Amount.ShouldBe(0);
        }

        private void VerifyMockCalls()
        {
            _dateProviderMock.Verify(x => x.CurrentDate, Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateCode(It.IsAny<AgingReportItem>()), Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateName(It.IsAny<AgingReportItem>()), Times.Once);

            _dateProviderMock.VerifyNoOtherCalls();
            _reportNamingServiceMock.VerifyNoOtherCalls();
        }
    }
}
