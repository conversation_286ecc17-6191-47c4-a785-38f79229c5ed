﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingLoanReports;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;
using BlueTape.Services.ARS.Tests.Models.AgingReportItems;
using BlueTape.Services.ARS.Tests.Models.LightLoanEntities;
using BlueTape.Utilities.Providers;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.Tests.Strategies.AgingLoanReports
{
    public class PastDueLoanStrategyTests
    {
        private readonly PastDueLoanStrategy _pastDueLoanStrategy;
        private readonly Mock<IDateProvider> _dateProviderMock = new();
        private readonly Mock<IReportNamingService> _reportNamingServiceMock = new();

        public PastDueLoanStrategyTests()
        {
            _pastDueLoanStrategy = new PastDueLoanStrategy(_dateProviderMock.Object, _reportNamingServiceMock.Object);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetails()
        {
            var lightLoanEntity = ValidLoanEntity.ValidSingleLoanEntity;
            var agingReportItem = ValidAgingReportItem.PastDueAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2023, 11, 25));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.PastDue);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns("past due");

            var result = _pastDueLoanStrategy.Create(agingReportItem, lightLoanEntity);

            VerifyMockCalls();

            result.ShouldNotBeNull();
            result.Code.ShouldBe(AgingItemsConstants.PastDue);
            result.Name.ShouldBe("past due");
            result.Amount.ShouldBe(586m);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetailsZeroAmount()
        {
            var lightLoanEntity = ValidLoanEntity.ValidSingleLoanEntity;
            var agingReportItem = ValidAgingReportItem.DueAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2024, 01, 01));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.PastDue);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns("past due");

            var result = _pastDueLoanStrategy.Create(agingReportItem, lightLoanEntity);

            VerifyMockCalls();

            result.ShouldNotBeNull();
            result.Code.ShouldBe(AgingItemsConstants.PastDue);
            result.Name.ShouldBe("past due");
            result.Amount.ShouldBe(0);
        }

        private void VerifyMockCalls()
        {
            _dateProviderMock.Verify(x => x.CurrentDate, Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateCode(It.IsAny<AgingReportItem>()), Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateName(It.IsAny<AgingReportItem>()), Times.Once);

            _dateProviderMock.VerifyNoOtherCalls();
            _reportNamingServiceMock.VerifyNoOtherCalls();
        }
    }
}
