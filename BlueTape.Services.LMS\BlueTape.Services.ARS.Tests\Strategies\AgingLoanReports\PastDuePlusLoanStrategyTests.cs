﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingLoanReports;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;
using BlueTape.Services.ARS.Tests.Models.AgingReportItems;
using BlueTape.Services.ARS.Tests.Models.LightLoanEntities;
using BlueTape.Utilities.Providers;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.Tests.Strategies.AgingLoanReports
{
    public class PastDuePlusLoanStrategyTests
    {
        private readonly PastDuePlusLoanStrategy _pastDuePlusLoanStrategy;
        private readonly Mock<IDateProvider> _dateProviderMock = new();
        private readonly Mock<IReportNamingService> _reportNamingServiceMock = new();

        public PastDuePlusLoanStrategyTests()
        {
            _pastDuePlusLoanStrategy = new PastDuePlusLoanStrategy(_dateProviderMock.Object, _reportNamingServiceMock.Object);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetails()
        {
            var lightLoanEntity = ValidLoanEntity.ValidSingleLoanEntityPastDuePlus;
            var agingReportItem = ValidAgingReportItem.PastDuePlusAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2023, 12, 27));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.PastDuePlus);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns("past due plus");

            var result = _pastDuePlusLoanStrategy.Create(agingReportItem, lightLoanEntity);

            VerifyMockCalls();

            result.ShouldNotBeNull();
            result.Code.ShouldBe(AgingItemsConstants.PastDuePlus);
            result.Name.ShouldBe("past due plus");
            result.Amount.ShouldBe(190.23m);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetailsZeroAmount()
        {
            var lightLoanEntity = ValidLoanEntity.ValidSingleLoanEntity;
            var agingReportItem = ValidAgingReportItem.PastDuePlusAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2023, 11, 20));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.PastDuePlus);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns("past due plus");

            var result = _pastDuePlusLoanStrategy.Create(agingReportItem, lightLoanEntity);

            VerifyMockCalls();

            result.ShouldNotBeNull();
            result.Code.ShouldBe(AgingItemsConstants.PastDuePlus);
            result.Name.ShouldBe("past due plus");
            result.Amount.ShouldBe(0);
        }

        private void VerifyMockCalls()
        {
            _dateProviderMock.Verify(x => x.CurrentDate, Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateCode(It.IsAny<AgingReportItem>()), Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateName(It.IsAny<AgingReportItem>()), Times.Once);

            _dateProviderMock.VerifyNoOtherCalls();
            _reportNamingServiceMock.VerifyNoOtherCalls();
        }
    }
}
