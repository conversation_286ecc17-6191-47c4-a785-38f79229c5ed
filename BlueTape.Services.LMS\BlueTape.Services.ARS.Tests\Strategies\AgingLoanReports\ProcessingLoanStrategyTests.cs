﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingLoanReports;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;
using BlueTape.Services.ARS.Tests.Models.AgingReportItems;
using BlueTape.Services.ARS.Tests.Models.LightLoanEntities;
using BlueTape.Utilities.Providers;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.Tests.Strategies.AgingLoanReports
{
    public class ProcessingLoanStrategyTests
    {
        private readonly ProcessingLoanStrategy _processingLoanStrategy;
        private readonly Mock<IDateProvider> _dateProviderMock = new();
        private readonly Mock<IReportNamingService> _reportNamingServiceMock = new();

        public ProcessingLoanStrategyTests()
        {
            _processingLoanStrategy = new ProcessingLoanStrategy(_dateProviderMock.Object, _reportNamingServiceMock.Object);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetails()
        {
            var lightLoanEntity = ValidLoanEntity.ValidSingleLoanEntity;
            var agingReportItem = ValidAgingReportItem.ProcessingAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2023, 12, 01));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.Processing);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns(AgingDetailsConstants.ProcessingName);

            var result = _processingLoanStrategy.Create(agingReportItem, lightLoanEntity);

            result.ShouldNotBeNull();
            result.Code.ShouldBe(AgingItemsConstants.Processing);
            result.Name.ShouldBe(AgingDetailsConstants.ProcessingName);
            result.Amount.ShouldBe(264);
        }


        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetailsEmptyList()
        {
            var lightLoanEntity = ValidLoanEntity.ValidSingleLoanEntityWithoutProcessingPayments;
            var agingReportItem = ValidAgingReportItem.ProcessingAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2023, 09, 30));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.Processing);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns(AgingDetailsConstants.ProcessingName);

            var result = _processingLoanStrategy.Create(agingReportItem, lightLoanEntity);

            VerifyMockCalls();

            result.ShouldNotBeNull();
            result.Code.ShouldBe(AgingItemsConstants.Processing);
            result.Name.ShouldBe(AgingDetailsConstants.ProcessingName);
            result.Amount.ShouldBe(0);
        }

        private void VerifyMockCalls()
        {
            _dateProviderMock.Verify(x => x.CurrentDate, Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateCode(It.IsAny<AgingReportItem>()), Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateName(It.IsAny<AgingReportItem>()), Times.Once);

            _dateProviderMock.VerifyNoOtherCalls();
            _reportNamingServiceMock.VerifyNoOtherCalls();
        }
    }
}
