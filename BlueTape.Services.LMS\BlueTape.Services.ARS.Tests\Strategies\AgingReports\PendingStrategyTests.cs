﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingReports;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;
using BlueTape.Services.ARS.Tests.Models.AgingReportItems;
using BlueTape.Services.ARS.Tests.Models.LightLoanEntities;
using BlueTape.Utilities.Providers;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.Tests.Strategies.AgingReports
{
    public class PendingStrategyTests
    {
        private readonly PendingStrategy _pendingStrategy;
        private readonly Mock<IDateProvider> _dateProviderMock = new();
        private readonly Mock<IReportNamingService> _reportNamingServiceMock = new();

        public PendingStrategyTests()
        {
            _pendingStrategy = new PendingStrategy(_dateProviderMock.Object, _reportNamingServiceMock.Object);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetails()
        {
            var lightLoanEntity = ValidLoanListEntities.ValidListLoanEntity;
            var agingReportItem = ValidAgingReportItem.PendingAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2023, 12, 01));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.Pending);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns(AgingDetailsConstants.PendingName);

            var result = _pendingStrategy.Create(agingReportItem, lightLoanEntity);

            VerifyMockCalls();

            result.ShouldNotBeNull();
            result.Code.ShouldBe(AgingItemsConstants.Pending);
            result.Name.ShouldBe(AgingDetailsConstants.PendingName);
            result.Amount.ShouldBe(600.46m);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetailsZeroAmount()
        {
            var lightLoanEntity = ValidLoanListEntities.ValidListLoanEntity;
            var agingReportItem = ValidAgingReportItem.PendingAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2024, 02, 20));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.Pending);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns(AgingDetailsConstants.PendingName);

            var result = _pendingStrategy.Create(agingReportItem, lightLoanEntity);

            VerifyMockCalls();

            result.ShouldNotBeNull();
            result.Code.ShouldBe(AgingItemsConstants.Pending);
            result.Name.ShouldBe(AgingDetailsConstants.PendingName);
            result.Amount.ShouldBe(0);
        }

        private void VerifyMockCalls()
        {
            _dateProviderMock.Verify(x => x.CurrentDate, Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateCode(It.IsAny<AgingReportItem>()), Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateName(It.IsAny<AgingReportItem>()), Times.Once);

            _dateProviderMock.VerifyNoOtherCalls();
            _reportNamingServiceMock.VerifyNoOtherCalls();
        }
    }
}
