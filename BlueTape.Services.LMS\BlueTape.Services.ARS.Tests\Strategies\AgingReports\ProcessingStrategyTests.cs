﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingReports;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Tests.Constants;
using BlueTape.Services.ARS.Tests.Models.AgingReportItems;
using BlueTape.Services.ARS.Tests.Models.LightLoanEntities;
using BlueTape.Utilities.Providers;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.ARS.Tests.Strategies.AgingReports
{
    public class ProcessingStrategyTests
    {
        private readonly ProcessingStrategy _processingStrategy;
        private readonly Mock<IDateProvider> _dateProviderMock = new();
        private readonly Mock<IReportNamingService> _reportNamingServiceMock = new();

        public ProcessingStrategyTests()
        {
            _processingStrategy = new ProcessingStrategy(_dateProviderMock.Object, _reportNamingServiceMock.Object);
        }

        [Fact]
        public void Create_ValidData_ReturnsAgingLoanDetails()
        {
            var lightLoanEntity = ValidLoanListEntities.ValidListLoanEntity;
            var agingReportItem = ValidAgingReportItem.ProcessingAgingReportItem;

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2023, 12, 01));
            _reportNamingServiceMock.Setup(x => x.CreateCode(It.IsAny<AgingReportItem>())).Returns(AgingItemsConstants.Processing);
            _reportNamingServiceMock.Setup(x => x.CreateName(It.IsAny<AgingReportItem>())).Returns(AgingDetailsConstants.ProcessingName);

            var result = _processingStrategy.Create(agingReportItem, lightLoanEntity);

            result.ShouldNotBeNull();
            result.Code.ShouldBe(AgingItemsConstants.Processing);
            result.Name.ShouldBe(AgingDetailsConstants.ProcessingName);
            result.Amount.ShouldBe(10400m);
            VerifyMockCalls();
        }

        private void VerifyMockCalls()
        {
            _dateProviderMock.Verify(x => x.CurrentDate, Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateCode(It.IsAny<AgingReportItem>()), Times.Once);
            _reportNamingServiceMock.Verify(x => x.CreateName(It.IsAny<AgingReportItem>()), Times.Once);

            _dateProviderMock.VerifyNoOtherCalls();
            _reportNamingServiceMock.VerifyNoOtherCalls();
        }
    }
}
