﻿using BlueTape.Services.ARS.Models.Models.AgingReports;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.ARS.Application.Abstractions.Services
{
    public interface IAgingReportsService
    {
        Task<AgingReport> GetAll(ProductType? product, string merchantId, CancellationToken ct);
        Task<AgingReport> GetByCompanyId(string companyId, ProductType? product, string merchantId, CancellationToken ct);
    }
}
