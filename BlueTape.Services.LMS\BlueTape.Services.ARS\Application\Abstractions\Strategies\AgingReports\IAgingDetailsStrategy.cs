﻿using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Models.Models.AgingReports;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;

namespace BlueTape.Services.ARS.Application.Abstractions.Strategies.AgingReports
{
    public interface IAgingDetailsStrategy
    {
        string Type { get; }
        AgingDetails Create(AgingReportItem item, IReadOnlyCollection<LightLoanEntity> loans);
    }
}
