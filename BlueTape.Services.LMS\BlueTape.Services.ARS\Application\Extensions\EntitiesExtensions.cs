﻿using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.ARS.Application.Extensions;
public static class EntitiesExtensions
{
    public static decimal CalculateTotalProcessingAmount(this LightLoanEntity loan) =>
        loan.Payments.Where(x => x.IsProcessingRepayment()).Sum(x => x.Amount);

    public static bool IsProcessingRepayment(this LightPaymentEntity payment) =>
        payment?.Status is PaymentStatus.Processing && payment?.SubType is PaymentSubType.Repayment;

    public static decimal CalculateTotalOutstandingAmount(this IEnumerable<LightLoanReceivableEntity> receivables) =>
        receivables.Sum(x => x.OutstandingAmount);

    public static bool IsActiveAndNotPaid(this LightLoanReceivableEntity receivable) =>
        receivable.Status is not LoanReceivableStatus.Paid
                        && receivable.Status is not LoanReceivableStatus.Canceled
                        && receivable.ScheduleStatus is ScheduleStatus.Current;
}
