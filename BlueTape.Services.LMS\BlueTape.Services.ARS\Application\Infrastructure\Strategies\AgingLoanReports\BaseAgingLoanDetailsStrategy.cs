﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Abstractions.Strategies.AgingLoanReports;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Models.Models.AgingLoanReports;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Utilities.Providers;

namespace BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingLoanReports
{
    public abstract class BaseAgingLoanDetailsStrategy : IAgingLoanDetailsStrategy
    {
        public abstract string Type { get; }

        private readonly IDateProvider _dateProvider;
        private readonly IReportNamingService _reportNamingService;

        protected BaseAgingLoanDetailsStrategy(IDateProvider dateProvider, IReportNamingService reportNamingService)
        {
            _dateProvider = dateProvider;
            _reportNamingService = reportNamingService;
        }

        public AgingLoanDetails Create(AgingReportItem item, LightLoanEntity loan)
        {
            var amount = CalculateAmount(item, loan, _dateProvider.CurrentDate);
            return new AgingLoanDetails()
            {
                Code = _reportNamingService.CreateCode(item),
                Name = _reportNamingService.CreateName(item),
                Amount = amount >= 0 ? Math.Round(amount, 2) : 0m
            };
        }

        protected abstract decimal CalculateAmount(AgingReportItem item, LightLoanEntity loan, DateOnly currentDate);
    }
}
