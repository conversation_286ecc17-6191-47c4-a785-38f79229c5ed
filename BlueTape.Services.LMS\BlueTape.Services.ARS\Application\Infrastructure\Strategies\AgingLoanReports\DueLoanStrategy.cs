﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Constants;
using BlueTape.Services.ARS.Application.Extensions;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;

namespace BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingLoanReports
{
    public class DueLoanStrategy : BaseAgingLoanDetailsStrategy
    {
        public override string Type => AgingItemsConstants.Due;

        public DueLoanStrategy(IDateProvider dateProvider, IReportNamingService reportNamingService) : base(dateProvider, reportNamingService)
        {
        }

        protected override decimal CalculateAmount(AgingReportItem item, LightLoanEntity loan, DateOnly currentDate)
        {
            return loan.LoanReceivables
                .Where(x => x.ExpectedDate == currentDate
                            && x.IsActiveAndNotPaid())
                .Sum(x => x.OutstandingAmount);
        }
    }
}
