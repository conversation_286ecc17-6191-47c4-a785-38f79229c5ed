﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Constants;
using BlueTape.Services.ARS.Application.Extensions;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;

namespace BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingLoanReports;

public class PastDuePlusLoanStrategy : BaseAgingLoanDetailsStrategy
{
    public override string Type => AgingItemsConstants.PastDuePlus;

    public PastDuePlusLoanStrategy(IDateProvider dateProvider, IReportNamingService reportNamingService) : base(dateProvider, reportNamingService)
    {
    }

    protected override decimal CalculateAmount(AgingReportItem item, LightLoanEntity loan, DateOnly currentDate)
    {
        var outstandingAmount = loan.LoanReceivables.Where(x => currentDate > x.ExpectedDate.AddDays(item.FromDate)
                                               && x.IsActiveAndNotPaid()).Sum(x => x.OutstandingAmount);

        return outstandingAmount - loan.CalculateTotalProcessingAmount();
    }
}