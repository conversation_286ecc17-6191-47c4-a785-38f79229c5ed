﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Constants;
using BlueTape.Services.ARS.Application.Extensions;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingLoanReports;

public class ProcessingLoanStrategy : BaseAgingLoanDetailsStrategy
{
    public override string Type => AgingItemsConstants.Processing;

    public ProcessingLoanStrategy(IDateProvider dateProvider, IReportNamingService reportNamingService) : base(dateProvider, reportNamingService)
    {
    }

    protected override decimal CalculateAmount(AgingReportItem item, LightLoanEntity loan, DateOnly currentDate)
    {
        if (loan.Payments.Exists(x => x.Status == PaymentStatus.Processing))
        {
            return loan.CalculateTotalProcessingAmount();
        }

        return 0m;
    }
}
