﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Abstractions.Strategies.AgingReports;
using BlueTape.Services.ARS.Application.Models;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Models.Models.AgingReports;
using BlueTape.Utilities.Providers;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;

namespace BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingReports;

public abstract class BaseAgingDetailsStrategy : IAgingDetailsStrategy
{
    private readonly IDateProvider _dateProvider;
    private readonly IReportNamingService _namingService;

    public abstract string Type { get; }

    protected BaseAgingDetailsStrategy(IDateProvider dateProvider, IReportNamingService namingService)
    {
        _dateProvider = dateProvider;
        _namingService = namingService;
    }

    public AgingDetails Create(AgingReportItem item, IReadOnlyCollection<LightLoanEntity> loans)
    {
        var strategyResult = GetLoans(item, loans, _dateProvider.CurrentDate);
        var agingLoans = strategyResult.ApplicableLoans;
        return new AgingDetails
        {
            Code = _namingService.CreateCode(item),
            Name = _namingService.CreateName(item),
            Amount = Math.Round(strategyResult.Amount, 2),
            CompanyCount = agingLoans.Select(x => x.CompanyId).Distinct().Count(),
            LoanCount = agingLoans.Select(x => x.Id).Distinct().Count(),
            LoansIds = agingLoans.Select(x => x.Id).ToList()
        };
    }

    protected abstract AgingReportStrategyResult GetLoans(AgingReportItem item, IReadOnlyCollection<LightLoanEntity> loans, DateOnly currentDate);
}