﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Constants;
using BlueTape.Services.ARS.Application.Extensions;
using BlueTape.Services.ARS.Application.Models;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using TinyHelpers.Extensions;

namespace BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingReports;

public class DueStrategy : BaseAgingDetailsStrategy
{
    public override string Type => AgingItemsConstants.Due;

    public DueStrategy(IDateProvider dateProvider, IReportNamingService reportNamingService) : base(dateProvider, reportNamingService)
    {
    }

    protected override AgingReportStrategyResult GetLoans(AgingReportItem item, IReadOnlyCollection<LightLoanEntity> loans, DateOnly currentDate)
    {
        decimal dueAmount = 0;
        var applicableLoans = new List<LightLoanEntity>();
        loans.ForEach(loan =>
        {
            var applicableReceivables = loan.LoanReceivables
                .Where(x => x.ExpectedDate == currentDate && x.IsActiveAndNotPaid()).ToList();
            if (applicableReceivables.Count == 0) return;

            dueAmount += applicableReceivables.Sum(x => x.OutstandingAmount);
            applicableLoans.Add(loan);
        });

        return new AgingReportStrategyResult()
        {
            ApplicableLoans = applicableLoans,
            Amount = dueAmount
        };
    }
}