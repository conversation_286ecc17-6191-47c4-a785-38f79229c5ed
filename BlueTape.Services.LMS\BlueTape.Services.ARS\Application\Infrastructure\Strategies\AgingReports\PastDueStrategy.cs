﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Constants;
using BlueTape.Services.ARS.Application.Extensions;
using BlueTape.Services.ARS.Application.Models;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using TinyHelpers.Extensions;

namespace BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingReports;

public class PastDueStrategy : BaseAgingDetailsStrategy
{
    public override string Type => AgingItemsConstants.PastDue;

    public PastDueStrategy(IDateProvider dateProvider, IReportNamingService reportNamingService) : base(dateProvider, reportNamingService)
    {
    }

    protected override AgingReportStrategyResult GetLoans(AgingReportItem item, IReadOnlyCollection<LightLoanEntity> loans, DateOnly currentDate)
    {
        decimal pastDueAmount = 0;
        var applicableLoans = new List<LightLoanEntity>();
        loans.ForEach(loan =>
        {
            var applicableReceivables = loan.LoanReceivables.Where(x => currentDate > x.ExpectedDate.AddDays(item.FromDate)
                && currentDate <= x.ExpectedDate.AddDays(item.ToDate) && x.IsActiveAndNotPaid()).ToList();
            if (applicableReceivables.Count == 0) return;

            var processingAmount = loan.CalculateTotalProcessingAmount();
            var receivablesOutstandingAmount = applicableReceivables.CalculateTotalOutstandingAmount();
            if (receivablesOutstandingAmount - processingAmount <= 0) return;

            pastDueAmount += receivablesOutstandingAmount;
            applicableLoans.Add(loan);
        });

        return new AgingReportStrategyResult()
        {
            ApplicableLoans = applicableLoans,
            Amount = pastDueAmount - applicableLoans.Sum(x => x.CalculateTotalProcessingAmount())
        };
    }

}
