﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Constants;
using BlueTape.Services.ARS.Application.Extensions;
using BlueTape.Services.ARS.Application.Models;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using TinyHelpers.Extensions;

namespace BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingReports;

public class PendingStrategy : BaseAgingDetailsStrategy
{
    public override string Type => AgingItemsConstants.Pending;

    public PendingStrategy(IDateProvider dateProvider, IReportNamingService reportNamingService) : base(dateProvider,
        reportNamingService)
    {
    }

    protected override AgingReportStrategyResult GetLoans(AgingReportItem item,
        IReadOnlyCollection<LightLoanEntity> loans, DateOnly currentDate)
    {
        decimal pendingAmount = 0;
        var applicableLoans = new List<LightLoanEntity>();
        loans.ForEach(loan =>
        {
            var applicableReceivables = loan.LoanReceivables
                .Where(x => x.IsActiveAndNotPaid() && x.ExpectedDate > currentDate)
                .ToList();
            if (applicableReceivables.Count == 0) return;

            pendingAmount += applicableReceivables.Sum(x => x.OutstandingAmount);
            applicableLoans.Add(loan);
        });

        return new AgingReportStrategyResult()
        {
            ApplicableLoans = applicableLoans,
            Amount = pendingAmount
        };
    }
}
