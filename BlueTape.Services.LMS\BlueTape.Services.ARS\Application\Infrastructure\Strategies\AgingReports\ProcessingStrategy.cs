﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Constants;
using BlueTape.Services.ARS.Application.Extensions;
using BlueTape.Services.ARS.Application.Models;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using TinyHelpers.Extensions;

namespace BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingReports;

public class ProcessingStrategy : BaseAgingDetailsStrategy
{
    public override string Type => AgingItemsConstants.Processing;

    public ProcessingStrategy(IDateProvider dateProvider, IReportNamingService reportNamingService) : base(dateProvider, reportNamingService)
    {
    }

    protected override AgingReportStrategyResult GetLoans(AgingReportItem item, IReadOnlyCollection<LightLoanEntity> loans, DateOnly currentDate)
    {
        var applicableLoans = new List<LightLoanEntity>();
        loans.ForEach(loan =>
        {
            if (!loan.Payments.Exists(x => x.IsProcessingRepayment())) return;
            applicableLoans.Add(loan);
        });

        return new AgingReportStrategyResult()
        {
            ApplicableLoans = applicableLoans,
            Amount = applicableLoans.Sum(x => x.CalculateTotalProcessingAmount())
        };
    }
}