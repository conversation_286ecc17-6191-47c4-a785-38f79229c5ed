﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Abstractions.Strategies.AgingLoanReports;
using BlueTape.Services.ARS.Application.Abstractions.Strategies.AgingReports;

namespace BlueTape.Services.ARS.Application.Providers
{
    public class AgingStrategiesProvider : IAgingStrategiesProvider
    {
        private readonly IEnumerable<IAgingDetailsStrategy> _agingDetailsStrategies;
        private readonly IEnumerable<IAgingLoanDetailsStrategy> _agingLoanDetailsStrategies;

        public AgingStrategiesProvider(
            IEnumerable<IAgingDetailsStrategy> agingDetailsStrategies,
            IEnumerable<IAgingLoanDetailsStrategy> agingLoanDetailsStrategies)
        {
            _agingDetailsStrategies = agingDetailsStrategies;
            _agingLoanDetailsStrategies = agingLoanDetailsStrategies;
        }
        public IAgingDetailsStrategy GetAgingDetailsStrategy(string type)
        {
            return _agingDetailsStrategies.First(x => x.Type == type);
        }

        public IAgingLoanDetailsStrategy GetAgingLoanDetailsStrategy(string type)
        {
            return _agingLoanDetailsStrategies.First(x => x.Type == type);
        }
    }
}
