﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Constants;
using BlueTape.Services.ARS.Infrastructure.Options;
using System.Text;

namespace BlueTape.Services.ARS.Application.Providers
{
    public class ReportNamingService : IReportNamingService
    {
        public string CreateCode(AgingReportItem item)
        {
            var stringBuilder = new StringBuilder();

            return item.Type switch
            {
                AgingItemsConstants.Pending => stringBuilder.Append("PENDING").ToString(),
                AgingItemsConstants.Due => stringBuilder.Append("DUE").ToString(),
                AgingItemsConstants.Processing => stringBuilder.Append("PROCESSING").ToString(),
                AgingItemsConstants.PastDue => stringBuilder.Append($"PASTDUE{item.ToDate}").ToString(),
                AgingItemsConstants.PastDuePlus => stringBuilder.Append($"PASTDUE{item.FromDate}PLUS").ToString(),
                _ => string.Empty
            };
        }

        public string CreateName(AgingReportItem item)
        {
            var stringBuilder = new StringBuilder();

            return item.Type switch
            {
                AgingItemsConstants.Pending => stringBuilder.Append("pending").ToString(),
                AgingItemsConstants.Due => stringBuilder.Append("due").ToString(),
                AgingItemsConstants.Processing => stringBuilder.Append("processing").ToString(),
                AgingItemsConstants.PastDue => stringBuilder.Append($"past due {item.FromDate}-{item.ToDate} days").ToString(),
                AgingItemsConstants.PastDuePlus => stringBuilder.Append($"past due {item.FromDate}+ days").ToString(),
                _ => string.Empty
            };
        }
    }
}
