﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Abstractions.Services;
using BlueTape.Services.ARS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Models.Models.AgingLoanReports;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace BlueTape.Services.ARS.Application.Services
{
    public class AgingLoanReportsService : IAgingLoanReportsService
    {
        private readonly ILightLoanRepository _lightLoanRepository;
        private readonly IAgingStrategiesProvider _agingStrategiesProvider;
        private readonly AgingReportOptions _agingReportOptions;

        public AgingLoanReportsService(
            ILightLoanRepository lightLoanRepository,
            IOptions<AgingReportOptions> agingReportOptions,
            IAgingStrategiesProvider agingStrategiesProvider)
        {
            _lightLoanRepository = lightLoanRepository;
            _agingStrategiesProvider = agingStrategiesProvider;
            _agingReportOptions = agingReportOptions.Value;

        }

        public async Task<IReadOnlyCollection<AgingLoanReport>> GetReportsByCompanyId(string companyId, ProductType? product,
            string merchantId, CancellationToken ct)
        {
            var loans = await _lightLoanRepository.GetByCompanyId(companyId, product, merchantId, ct);
            var agingLoanReports = new ConcurrentDictionary<long, AgingLoanReport>();
            Parallel.ForEach(loans, (loan, _, loanIndex) =>
                {
                    agingLoanReports.TryAdd(loanIndex, new AgingLoanReport { AgingDetails = BuildAgingLoanDetails(loan), LoanId = loan.Id });
                });

            return agingLoanReports.Values.ToList();
        }

        private List<AgingLoanDetails> BuildAgingLoanDetails(LightLoanEntity loan)
        {
            var agingLoanDetails = new ConcurrentDictionary<long, AgingLoanDetails>();
            Parallel.ForEach(_agingReportOptions.AgingReportItems, (item, _, itemIndex) =>
            {
                var agingDetailStrategy = _agingStrategiesProvider.GetAgingLoanDetailsStrategy(item.Type);
                agingLoanDetails.TryAdd(itemIndex, agingDetailStrategy.Create(item, loan));
            });

            return agingLoanDetails.Values.ToList();
        }
    }
}
