﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Abstractions.Services;
using BlueTape.Services.ARS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.ARS.Infrastructure.Options;
using BlueTape.Services.ARS.Models.Models.AgingReports;
using BlueTape.Services.LMS.Domain.Entities.ArsEntities.LightLoanEntities;
using BlueTape.Services.LMS.Domain.Enums;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;


namespace BlueTape.Services.ARS.Application.Services;

public class AgingReportsService : IAgingReportsService
{
    private readonly ILightLoanRepository _lightLoanRepository;
    private readonly IAgingStrategiesProvider _agingStrategiesProvider;
    private readonly AgingReportOptions _agingReportOptions;

    public AgingReportsService(
        ILightLoanRepository lightLoanRepository,
        IOptions<AgingReportOptions> agingReportOptions,
        IAgingStrategiesProvider agingStrategiesProvider)
    {
        _lightLoanRepository = lightLoanRepository;
        _agingStrategiesProvider = agingStrategiesProvider;
        _agingReportOptions = agingReportOptions.Value;
    }

    public async Task<AgingReport> GetByCompanyId(string companyId, ProductType? product, string merchantId, CancellationToken ct)
    {
        var loans = await _lightLoanRepository.GetByCompanyId(companyId, product, merchantId, ct);
        var agingDetails = BuildAgingDetails(loans);

        return new AgingReport
        {
            AgingDetails = agingDetails,
            CompanyCount = loans.Select(x => x.CompanyId).Distinct().Count(),
            LoanCount = loans.Count
        };
    }

    public async Task<AgingReport> GetAll(ProductType? product, string merchantId, CancellationToken ct)
    {
        var loans = await _lightLoanRepository.GetAll(product, merchantId, ct);
        var agingDetails = BuildAgingDetails(loans);

        return new AgingReport
        {
            AgingDetails = agingDetails,
            CompanyCount = loans.Select(x => x.CompanyId).Distinct().Count(),
            LoanCount = loans.Count
        };
    }

    private List<AgingDetails> BuildAgingDetails(IReadOnlyCollection<LightLoanEntity> loans)
    {
        var agingDetails = new ConcurrentDictionary<long, AgingDetails>();
        Parallel.ForEach(_agingReportOptions.AgingReportItems, (item, _, itemIndex) =>
                {
                    var agingDetailStrategy = _agingStrategiesProvider.GetAgingDetailsStrategy(item.Type);
                    agingDetails.TryAdd(itemIndex, agingDetailStrategy.Create(item, loans));
                });

        return agingDetails.Values.ToList();
    }
}
