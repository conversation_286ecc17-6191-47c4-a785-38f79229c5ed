<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="bluetape.utilities" Version="1.4.6" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlueTape.Services.ARS.DataAccess\BlueTape.Services.ARS.DataAccess.csproj" />
    <ProjectReference Include="..\BlueTape.Services.ARS.Models\BlueTape.Services.ARS.Models.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.Application\BlueTape.Services.LMS.Application.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.Domain\BlueTape.Services.LMS.Domain.csproj" />
  </ItemGroup>

</Project>
