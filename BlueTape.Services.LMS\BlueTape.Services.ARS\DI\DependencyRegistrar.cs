﻿using BlueTape.Services.ARS.Application.Abstractions.Providers;
using BlueTape.Services.ARS.Application.Abstractions.Services;
using BlueTape.Services.ARS.Application.Abstractions.Strategies.AgingLoanReports;
using BlueTape.Services.ARS.Application.Abstractions.Strategies.AgingReports;
using BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingLoanReports;
using BlueTape.Services.ARS.Application.Infrastructure.Strategies.AgingReports;
using BlueTape.Services.ARS.Application.Providers;
using BlueTape.Services.ARS.Application.Services;
using BlueTape.Services.ARS.DataAccess.DI;
using BlueTape.Services.ARS.Infrastructure.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.ARS.DI
{
    public static class DependencyRegistrar
    {
        public static void AddAgingReportsDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddArsDataAccessDependencies(config);

            services.AddTransient<IAgingReportsService, AgingReportsService>();
            services.AddTransient<IAgingLoanReportsService, AgingLoanReportsService>();
            services.AddTransient<IReportNamingService, ReportNamingService>();

            services.Configure<AgingReportOptions>(config.GetSection(nameof(AgingReportOptions)));

            services.AddTransient<IAgingStrategiesProvider, AgingStrategiesProvider>();

            services.AddTransient<IAgingDetailsStrategy, ProcessingStrategy>();
            services.AddTransient<IAgingDetailsStrategy, PendingStrategy>();
            services.AddTransient<IAgingDetailsStrategy, DueStrategy>();
            services.AddTransient<IAgingDetailsStrategy, PastDueStrategy>();
            services.AddTransient<IAgingDetailsStrategy, PastDuePlusStrategy>();

            services.AddTransient<IAgingLoanDetailsStrategy, DueLoanStrategy>();
            services.AddTransient<IAgingLoanDetailsStrategy, PastDueLoanStrategy>();
            services.AddTransient<IAgingLoanDetailsStrategy, PastDuePlusLoanStrategy>();
            services.AddTransient<IAgingLoanDetailsStrategy, PendingLoanStrategy>();
            services.AddTransient<IAgingLoanDetailsStrategy, ProcessingLoanStrategy>();
        }
    }
}
