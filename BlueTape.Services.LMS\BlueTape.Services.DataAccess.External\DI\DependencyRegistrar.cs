﻿using BlueTape.CompanyClient.DI;
using BlueTape.InvoiceClient.DI;
using BlueTape.LinqpalClient.DI;
using BlueTape.OBS.Client.Extensions;
using BlueTape.PaymentService.DI;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.DataAccess.External.DI;

public static class DependencyRegistrar
{
    public static void AddExternalDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddCompanyServiceClient(configuration);
        services.AddInvoiceServiceClient(configuration);
        services.AddDataAccessObsDependencies(configuration);
        services.AddPaymentServiceMessageSenders(configuration);
        services.AddLinqpalServiceClient(configuration);
    }
}