﻿using BlueTape.Services.LMS.API.BackgroundServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.OverDueServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Quartz;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.BackgroundServices
{
    public class OverDueDetectorJobTests
    {
        private readonly OverDueDetectorJob _overDueDetectorJob;
        private readonly Mock<ILogger<OverDueDetectorJob>> _loggerMock = new();
        private readonly Mock<IServiceScope> _serviceScopeMock = new();
        private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock = new();
        private readonly Mock<IOverDueDetectorService> _overDueDetectorServiceMock = new();

        public OverDueDetectorJobTests()
        {
            _overDueDetectorJob = new OverDueDetectorJob(_loggerMock.Object, _serviceScopeFactoryMock.Object);
        }

        [Fact]
        public Task Execute_Valid_ShouldNotThrows()
        {
            Mock<IJobExecutionContext> context = new();

            _overDueDetectorServiceMock.Setup(x => x.OverdueChecking(CancellationToken.None, null)).Throws(new ApplicationException());

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton(_overDueDetectorServiceMock.Object);

            var serviceProvider = serviceCollection.BuildServiceProvider();

            _serviceScopeMock.SetupGet<IServiceProvider>(s => s.ServiceProvider)
                .Returns(serviceProvider);

            _serviceScopeFactoryMock.Setup(s => s.CreateScope())
                .Returns(_serviceScopeMock.Object);

            return _overDueDetectorJob.Execute(context.Object).ShouldNotThrowAsync();
        }

        [Fact]
        public Task Execute_Invalid_ShouldNotThrows()
        {
            Mock<IJobExecutionContext> context = new();

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton(_overDueDetectorServiceMock.Object);

            var serviceProvider = serviceCollection.BuildServiceProvider();

            _serviceScopeMock.SetupGet<IServiceProvider>(s => s.ServiceProvider)
                .Returns(serviceProvider);

            _serviceScopeFactoryMock.Setup(s => s.CreateScope())
                .Returns(_serviceScopeMock.Object);

            return _overDueDetectorJob.Execute(context.Object).ShouldNotThrowAsync();
        }

    }
}
