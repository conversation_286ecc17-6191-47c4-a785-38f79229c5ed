﻿using BlueTape.Services.LMS.API.Tests.ViewModels.CalculatorLoans;
using BlueTape.Services.LMS.API.Validators.Loan;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class CalculatorLoanDtoValidatorTests
    {

        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new CalculatorLoanDtoValidator();

            var model = ValidCalculatorLoanDto.CalculatorLoanViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_AmountLessThanZero_ReturnsFalse()
        {
            var validator = new CalculatorLoanDtoValidator();

            var model = InvalidCalculatorLoanDto.AmountLessThanZeroCalculatorLoanViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.Amount));
        }

        [Fact]
        public void Validate_EmptyLoanTemplateId_ReturnsFalse()
        {
            var validator = new CalculatorLoanDtoValidator();

            var model = InvalidCalculatorLoanDto.LoanTemplateIdEmptyCalculatorLoanViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.LoanTemplateId));
        }
    }
}
