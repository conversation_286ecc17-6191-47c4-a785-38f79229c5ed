﻿using BlueTape.Services.LMS.API.Tests.ViewModels.LoanReceivables;
using BlueTape.Services.LMS.API.Validators.ReceivableFee;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class ChangeExpectedAmountViewModelValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new UpdateReceivableFeeDtoValidator();
            var model = ValidUpdateReceivableLatePaymentFeeViewModel.ValidExpectedAmount;

            // Act
            var result = validator.Validate(model);

            // Assert 
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_AmountLessThanZero_ReturnsFalse()
        {
            var validator = new UpdateReceivableFeeDtoValidator();
            var model = InvalidChangeExpectedAmountViewModel.UpdateReceivableViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.ExpectedAmount));
        }
    }
}
