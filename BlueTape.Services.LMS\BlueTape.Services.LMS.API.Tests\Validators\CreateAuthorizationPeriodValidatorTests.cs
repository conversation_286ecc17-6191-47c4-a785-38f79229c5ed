using BlueTape.Services.LMS.API.Tests.ViewModels.AuthorizationPeriods;
using BlueTape.Services.LMS.API.Tests.ViewModels.LoanReceivables;
using BlueTape.Services.LMS.API.Validators.AuthorizationPeriod;
using BlueTape.Services.LMS.API.Validators.ReceivableFee;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators;

public class CreateAuthorizationPeriodValidatorTests
{
    [Fact]
    public void Validate_ValidModel_ReturnsTrue()
    {
        var validator = new CreateAuthorizationPeriodValidator();
        var model = ValidCreateAuthorizationPeriodRequest.ValidModel;

        // Act
        var result = validator.Validate(model);

        // Assert 
        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public void Validate_AmountLessThanZero_ReturnsFalse()
    {
        var validator = new CreateAuthorizationPeriodValidator();
        var model = InvalidCreateAuthorizationPeriodRequest.InvalidCreditHoldAmount;

        // Act
        var result = validator.Validate(model);

        // Assert
        result.Errors.ShouldNotBeEmpty();
        result.Errors.ShouldContain(x => x.PropertyName == nameof(model.CreditHoldAmount));
    }
    
    [Fact]
    public void Validate_StartDateNotToday_ReturnsFalse()
    {
        var validator = new CreateAuthorizationPeriodValidator();
        var model = InvalidCreateAuthorizationPeriodRequest.InvalidStartDate;

        // Act
        var result = validator.Validate(model);

        // Assert
        result.Errors.ShouldNotBeEmpty();
        result.Errors.ShouldContain(x => x.PropertyName == nameof(model.StartDate));
    }
    
    [Fact]
    public void Validate_EndDateErlierThanToday_ReturnsFalse()
    {
        var validator = new CreateAuthorizationPeriodValidator();
        var model = InvalidCreateAuthorizationPeriodRequest.InvalidEndDate;

        // Act
        var result = validator.Validate(model);

        // Assert
        result.Errors.ShouldNotBeEmpty();
        result.Errors.ShouldContain(x => x.PropertyName == nameof(model.EndDate));
    }
    [Fact]
    public void Validate_CreditIdIsEmptyGuid_ReturnsFalse()
    {
        var validator = new CreateAuthorizationPeriodValidator();
        var model = InvalidCreateAuthorizationPeriodRequest.CreditIdIsGuidEmpty;

        // Act
        var result = validator.Validate(model);

        // Assert
        result.Errors.ShouldNotBeEmpty();
        result.Errors.ShouldContain(x => x.PropertyName == nameof(model.CreditId));
    }
}