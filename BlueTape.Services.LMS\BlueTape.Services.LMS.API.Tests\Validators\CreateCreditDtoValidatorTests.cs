﻿using BlueTape.Services.LMS.API.Tests.ViewModels.Credit;
using BlueTape.Services.LMS.API.Validators.Credit;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class CreateCreditDtoValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new CreateCreditDtoValidator();

            var model = CreateCreditDtos.ValidCreateCreditViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_CompanyIdIsEmpty_ReturnsFalse()
        {
            var validator = new CreateCreditDtoValidator();

            var model = CreateCreditDtos.CompanyIdIsEmptyCreateCreditViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.CompanyId));
        }

        [Fact]
        public void Validate_CreditApplicationIdIsEmpty_ReturnsFalse()
        {
            var validator = new CreateCreditDtoValidator();

            var model = CreateCreditDtos.CreditApplicationIdIsEmptyCreateCreditViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.CreditApplicationId));
        }

        [Fact]
        public void Validate_EmptyLoanTemplateId_ReturnsFalse()
        {
            var validator = new CreateCreditDtoValidator();

            var model = CreateCreditDtos.CreditLimitIsEmptyCreateCreditViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.CreditLimit));
        }
    }
}
