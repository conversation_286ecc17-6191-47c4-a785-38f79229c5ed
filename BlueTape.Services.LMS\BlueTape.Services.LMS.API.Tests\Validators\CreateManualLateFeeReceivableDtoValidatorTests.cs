﻿using BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees;
using BlueTape.Services.LoanService.API.Validators.Admin;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LoanService.API.Tests.Validators;
public class CreateManualLateFeeReceivableDtoValidatorTests
{
    [Fact]
    public void Validate_ValidModel_ReturnsTrue()
    {
        var validator = new CreateFeeReceivableDtoValidator();

        var model = new CreateFeeReceivableDto()
        {
            LoanId = Guid.NewGuid(),
            ExpectedAmount = 100,
            ExpectedDate = DateOnly.MaxValue
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeTrue();
    }

    [Fact]
    public void Validate_LoanIdEmpty_ReturnsFalse()
    {
        var validator = new CreateFeeReceivableDtoValidator();

        var model = new CreateFeeReceivableDto()
        {
            LoanId = Guid.Empty,
            ExpectedAmount = 100,
            ExpectedDate = DateOnly.MaxValue
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }

    [Fact]
    public void Validate_AmountZero_ReturnsFalse()
    {
        var validator = new CreateFeeReceivableDtoValidator();

        var model = new CreateFeeReceivableDto()
        {
            LoanId = Guid.NewGuid(),
            ExpectedAmount = 0,
            ExpectedDate = DateOnly.MaxValue
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }

    [Fact]
    public void Validate_EmptyModel_ReturnsFalse()
    {
        var validator = new CreateFeeReceivableDtoValidator();

        var model = new CreateFeeReceivableDto();

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }

    [Fact]
    public void Validate_DateEmpty_ReturnsFalse()
    {
        var validator = new CreateFeeReceivableDtoValidator();

        var model = new CreateFeeReceivableDto()
        {
            LoanId = Guid.NewGuid(),
            ExpectedAmount = 10,
        };

        // Act
        var result = validator.Validate(model);

        // Assert
        result.IsValid.ShouldBeFalse();
    }
}
