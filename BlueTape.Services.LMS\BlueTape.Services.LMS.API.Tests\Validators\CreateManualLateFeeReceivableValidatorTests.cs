﻿using BlueTape.Services.LMS.API.Tests.Dtos.LoanReceivables;
using BlueTape.Services.LMS.API.Validators.LoanReceivables;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class CreateManualLateFeeReceivableValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new CreateManualLateFeeReceivableViewModelValidator();
            var model = ValidCreateManualLateFeeReceivableDto.CreateManualLateFeeReceivable;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_ExpectedDateIsEmpty_ReturnsFalse()
        {
            var validator = new CreateManualLateFeeReceivableViewModelValidator();
            var model = InvalidCreateManualLateFeeReceivableDto.CreateManualLateFeeReceivableWithEmptyExpectedDate;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }


        [Fact]
        public void Validate_ExpectedAmountIsEmpty_ReturnsFalse()
        {
            var validator = new CreateManualLateFeeReceivableViewModelValidator();
            var model = InvalidCreateManualLateFeeReceivableDto.CreateManualLateFeeReceivableWithEmptyExpectedAmount;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_ExpectedAmountIsZero_ReturnsFalse()
        {
            var validator = new CreateManualLateFeeReceivableViewModelValidator();
            var model = InvalidCreateManualLateFeeReceivableDto.CreateManualLateFeeReceivableWithZeroExpectedAmount;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_ExpectedAmountIsNegative_ReturnsFalse()
        {
            var validator = new CreateManualLateFeeReceivableViewModelValidator();
            var model = InvalidCreateManualLateFeeReceivableDto.CreateManualLateFeeReceivableWithNegativeExpectedAmount;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_IdIsEmpty_ReturnsFalse()
        {
            var validator = new CreateManualLateFeeReceivableViewModelValidator();
            var model = InvalidCreateManualLateFeeReceivableDto.UpdateLoanReceivablesDtoWithEmptyId;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }
    }
}
