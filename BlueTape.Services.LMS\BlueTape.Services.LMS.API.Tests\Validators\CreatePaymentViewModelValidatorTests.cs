﻿using BlueTape.Services.LMS.API.Tests.ViewModels.Payments;
using BlueTape.Services.LMS.API.Validators.Payment;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class CreatePaymentViewModelValidatorTests
    {
        private readonly CreatePaymentDtoValidator _validator = new();

        [Fact]
        public void Validate_AmountIsLessThanNeeded_ReturnsFalse()
        {
            var model = InvalidCreatePaymentDto.AmountIsLessThanNeeded;

            var result = _validator.Validate(model);

            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_AmountIsHaveMoreDigitsThanNeeded_ReturnsFalse()
        {
            var model = InvalidCreatePaymentDto.AmountIsHaveMoreDigitsThanNeeded;

            var result = _validator.Validate(model);

            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var model = ValidCreatePaymentDto.ValidModel;

            var result = _validator.Validate(model);

            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_AmountIsEqualMinimumAllowed_ReturnsTrue()
        {
            var model = ValidCreatePaymentDto.AmountIsEqualMinimumAllowed;

            var result = _validator.Validate(model);

            result.IsValid.ShouldBeTrue();
        }
    }
}
