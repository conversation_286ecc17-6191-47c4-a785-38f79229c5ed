﻿using BlueTape.LS.DTOs.PenaltyInterest;
using BlueTape.Services.LoanService.API.Validators.Admin;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LoanService.API.Tests.Validators
{
    public class CreatePenaltyInterestFeeReceivableDtoValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new CreatePenaltyInterestFeeReceivableDtoValidator();

            var model = new CreatePenaltyInterestFeeReceivableDto()
            {
                ExpectedDate = DateOnly.FromDateTime(DateTime.Now),
                ExpectedAmount = 10,
                LoanId = Guid.NewGuid(),
                PenaltyStartDate = DateOnly.FromDateTime(DateTime.Now),
                PenaltyEndDate = DateOnly.FromDateTime(DateTime.Now),
                Note = "note"
            };

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_ExpectedDateNotCorrect_ReturnsFalse()
        {
            var validator = new CreatePenaltyInterestFeeReceivableDtoValidator();

            var model = new CreatePenaltyInterestFeeReceivableDto()
            {
                ExpectedAmount = 10,
                LoanId = Guid.NewGuid(),
                PenaltyStartDate = DateOnly.FromDateTime(DateTime.Now),
                PenaltyEndDate = DateOnly.FromDateTime(DateTime.Now),
                Note = "note"
            };

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.ExpectedDate));
        }

        [Fact]
        public void Validate_ExpectedAmountZero_ReturnsFalse()
        {
            var validator = new CreatePenaltyInterestFeeReceivableDtoValidator();

            var model = new CreatePenaltyInterestFeeReceivableDto()
            {
                ExpectedDate = DateOnly.FromDateTime(DateTime.Now),
                ExpectedAmount = 0,
                LoanId = Guid.NewGuid(),
                PenaltyStartDate = DateOnly.FromDateTime(DateTime.Now),
                PenaltyEndDate = DateOnly.FromDateTime(DateTime.Now),
                Note = "note"
            };

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.ExpectedAmount));
        }

        [Fact]
        public void Validate_EmptyLoanId_ReturnsFalse()
        {
            var validator = new CreatePenaltyInterestFeeReceivableDtoValidator();

            var model = new CreatePenaltyInterestFeeReceivableDto()
            {
                ExpectedDate = DateOnly.FromDateTime(DateTime.Now),
                ExpectedAmount = 10,
                PenaltyStartDate = DateOnly.FromDateTime(DateTime.Now),
                PenaltyEndDate = DateOnly.FromDateTime(DateTime.Now),
                Note = "note"
            };

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.LoanId));
        }

        [Fact]
        public void Validate_IncorrectPenaltyStartDate_ReturnsFalse()
        {
            var validator = new CreatePenaltyInterestFeeReceivableDtoValidator();

            var model = new CreatePenaltyInterestFeeReceivableDto()
            {
                ExpectedDate = DateOnly.FromDateTime(DateTime.Now),
                ExpectedAmount = 10,
                LoanId = Guid.NewGuid(),
                PenaltyEndDate = DateOnly.FromDateTime(DateTime.Now),
                Note = "note"
            };

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.PenaltyStartDate));
        }

        [Fact]
        public void Validate_IncorrectPenaltyEndDate_ReturnsFalse()
        {
            var validator = new CreatePenaltyInterestFeeReceivableDtoValidator();

            var model = new CreatePenaltyInterestFeeReceivableDto()
            {
                ExpectedDate = DateOnly.FromDateTime(DateTime.Now),
                ExpectedAmount = 10,
                LoanId = Guid.NewGuid(),
                PenaltyStartDate = DateOnly.FromDateTime(DateTime.Now),
                Note = "note"
            };

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.PenaltyEndDate));
        }

        [Fact]
        public void Validate_EmptyNote_ReturnsFalse()
        {
            var validator = new CreatePenaltyInterestFeeReceivableDtoValidator();

            var model = new CreatePenaltyInterestFeeReceivableDto()
            {
                ExpectedDate = DateOnly.FromDateTime(DateTime.Now),
                ExpectedAmount = 10,
                LoanId = Guid.NewGuid(),
                PenaltyStartDate = DateOnly.FromDateTime(DateTime.Now),
                PenaltyEndDate = DateOnly.FromDateTime(DateTime.Now),
            };

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.Note));
        }
    }
}
