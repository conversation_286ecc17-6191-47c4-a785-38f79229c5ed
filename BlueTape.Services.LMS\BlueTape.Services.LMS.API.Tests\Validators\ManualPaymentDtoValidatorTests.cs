﻿using BlueTape.Services.LMS.API.Tests.ViewModels.Payments;
using BlueTape.Services.LMS.API.Validators.Payment;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class ManualPaymentDtoValidatorTests
    {
        private readonly ManualPaymentDtoValidator _validator = new();

        [Fact]
        public void Validate_AmountIsLessThanNeeded_ReturnsFalse()
        {
            var model = InvalidManualPaymentDto.AmountIsLessThanNeeded;

            var result = _validator.Validate(model);

            result.IsValid.ShouldBeFalse();
        }


        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var model = ValidManualPaymentDto.ValidModel;

            var result = _validator.Validate(model);

            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_AmountIsEqualMinimumAllowed_ReturnsTrue()
        {
            var model = ValidManualPaymentDto.AmountIsEqualMinimumAllowed;

            var result = _validator.Validate(model);

            result.IsValid.ShouldBeTrue();
        }
    }
}
