using BlueTape.Services.LMS.API.Tests.ViewModels.AuthorizationPeriods;
using BlueTape.Services.LMS.API.Validators.AuthorizationPeriod;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators;

public class PatchCreditHoldExpirationDateValidatorTests
{
    [Fact]
    public void Validate_ValidModel_ReturnsTrue()
    {
        var validator = new PatchCreditHoldExpirationDateValidator();
        var model = InvalidPatchExpirationDateRequest.DateLessThanToday;

        // Act
        var result = validator.Validate(model);

        // Assert 
        result.IsValid.ShouldBeFalse();
        result.Errors.ShouldContain(x => x.PropertyName == nameof(model.NewExpirationDate));
    }

    [Fact]
    public void Validate_InvalidModel_ReturnsFalse()
    {
        var validator = new PatchCreditHoldExpirationDateValidator();
        var model = ValidPatchExpirationDateRequest.ValidRequest;

        // Act
        var result = validator.Validate(model);

        // Assert 
        result.IsValid.ShouldBeTrue();
    }
}