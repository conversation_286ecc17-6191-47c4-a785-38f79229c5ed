﻿using BlueTape.LS.DTOs.PenaltyInterest.BasisPoint;
using BlueTape.Services.LMS.API.Tests.ViewModels.BasisPoint;
using BlueTape.Services.LMS.API.Validators.BasisPoint;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class ShortBasisPointDtoValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new ShortBasisPointDtoValidator();
            var model = ValidShortBasisPointDto.WithBasisPointValueAndDate;

            var result = validator.Validate(model);

            result.IsValid.ShouldBeTrue();
        }

        [Theory]
        [ClassData(typeof(InvalidShortBasisPointDto))]
        public void Validate_InvalidModel_ReturnsFalse(ShortBasisPointDto model)
        {
            var validator = new ShortBasisPointDtoValidator();
            var result = validator.Validate(model);

            result.IsValid.ShouldBeFalse();
        }
    }
}
