﻿using BlueTape.Services.LMS.API.Tests.ViewModels.LoanTemplates;
using BlueTape.Services.LMS.API.Validators.LoanTemplate;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class ShortLoanTemplateViewModelValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new ShortLoanTemplateViewModelValidator();

            var model = ValidLoanTemplateViewModels.ShortLoanTemplateViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_LoanReceivablesNumberLessThanZero_ReturnsFalse()
        {
            var validator = new ShortLoanTemplateViewModelValidator();

            var model = InvalidLoanTemplateViewModels.InstallmentsNumberLessThanZeroShortLoanTemplateViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.InstallmentsNumber));
        }

        [Fact]
        public void Validate_PaymentIntervalInDaysLessThanZero_ReturnsFalse()
        {
            var validator = new ShortLoanTemplateViewModelValidator();

            var model = InvalidLoanTemplateViewModels.PaymentIntervalInDaysLessThanZeroShortLoanTemplateViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.PaymentIntervalInDays));
        }

        [Fact]
        public void Validate_FeePercentLessThanZero_ReturnsFalse()
        {
            var validator = new ShortLoanTemplateViewModelValidator();

            var model = InvalidLoanTemplateViewModels.FeePercentLessThanZeroShortLoanTemplateViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.LoanFeePercentage));
        }

        [Fact]
        public void Validate_FeePercentGreaterThanZero_ReturnsFalse()
        {
            var validator = new ShortLoanTemplateViewModelValidator();

            var model = InvalidLoanTemplateViewModels.FeePercentGreaterThanZeroShortLoanTemplateViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.LoanFeePercentage));
        }

        [Fact]
        public void Validate_GracePeriodLessThanZero_ReturnsFalse()
        {
            var validator = new ShortLoanTemplateViewModelValidator();

            var model = InvalidLoanTemplateViewModels.GracePeriodLessThanZeroShortLoanTemplateViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.GracePeriodInDays));
        }

        [Fact]
        public void Validate_TotalDurationLessThanZero_ReturnsFalse()
        {
            var validator = new ShortLoanTemplateViewModelValidator();

            var model = InvalidLoanTemplateViewModels.TotalDurationLessThanZeroShortLoanTemplateViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.TotalDurationInDays));
        }
    }
}
