﻿using BlueTape.Services.LMS.API.Tests.ViewModels.Loans;
using BlueTape.Services.LMS.API.Validators.Loan;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class ShortLoanViewModelValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new ShortLoanDtoValidator();

            var model = ValidLoanViewModels.CreateLoanViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_AmountLessThanZero_ReturnsFalse()
        {
            var validator = new ShortLoanDtoValidator();

            var model = InvalidLoanViewModels.AmountLessThanZeroCreateLoanViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.Amount));
        }

        [Fact]
        public void Validate_EmptyLoanTemplateId_ReturnsFalse()
        {
            var validator = new ShortLoanDtoValidator();

            var model = InvalidLoanViewModels.LoanTemplateIdEmptyCreateLoanViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.LoanTemplateId));
        }

        [Fact]
        public void Validate_EmptyCompanyId_ReturnsFalse()
        {
            var validator = new ShortLoanDtoValidator();

            var model = InvalidLoanViewModels.CompanyIdEmptyCreateLoanViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.CompanyId));
        }
    }
}
