﻿using BlueTape.Services.LMS.API.Tests.ViewModels.LoanReceivables;
using BlueTape.Services.LMS.API.Validators.Installment;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class UpdateLoanReceivableDateViewModelValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new UpdateLoanReceivableDateValidator();

            var model = ValidUpdateLoanReceivableDateViewModel.UpdateLoanReceivableDateModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_DateIsInvalid_ReturnsFalse()
        {
            var validator = new UpdateLoanReceivableDateValidator();

            var model = InvalidUpdateLoanReceivableDateViewModel.InvalidDateUpdateModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_UpdateLoanReceivableDateViewModelWithEmptyNote_ReturnsFalse()
        {
            var validator = new UpdateLoanReceivableDateValidator();

            var model = InvalidUpdateLoanReceivableDateViewModel.UpdateLoanReceivableDateViewModelWithEmptyNote;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_UpdateLoanReceivableDateViewModelWithNullNote_ReturnsFalse()
        {
            var validator = new UpdateLoanReceivableDateValidator();

            var model = InvalidUpdateLoanReceivableDateViewModel.UpdateLoanReceivableDateViewModelWithNullNote;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }
    }
}
