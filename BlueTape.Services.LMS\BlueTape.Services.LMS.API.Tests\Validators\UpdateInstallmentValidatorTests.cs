﻿using BlueTape.Services.LMS.API.Tests.Dtos.LoanReceivables;
using BlueTape.Services.LMS.API.Validators.Installment;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class UpdateLoanReceivableValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new UpdateLoanReceivablesValidator();

            var model = ValidUpdateLoanReceivableDto.updateLoanReceivablesDto;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_DateIsInvalid_ReturnsFalse()
        {
            var validator = new UpdateLoanReceivablesValidator();

            var model = InvalidUpdateLoanReceivableDto.UpdateLoanReceivablesDtoDateInValid;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }


        [Fact]
        public void Validate_AmountIsInvalid_ReturnsFalse()
        {
            var validator = new UpdateLoanReceivablesValidator();

            var model = InvalidUpdateLoanReceivableDto.UpdateLoanReceivablesDtoAmountInValid;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_UpdateLoanLoanReceivablesDtoWithEmptyNote_ReturnsFalse()
        {
            var validator = new UpdateLoanReceivablesValidator();

            var model = InvalidUpdateLoanReceivableDto.UpdateLoanReceivablesDtoWithEmptyNote;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_UpdateLoanLoanReceivablesDtoWithNullNote_ReturnsFalse()
        {
            var validator = new UpdateLoanReceivablesValidator();

            var model = InvalidUpdateLoanReceivableDto.UpdateLoanReceivablesDtoWithNullNote;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeFalse();
        }
    }
}
