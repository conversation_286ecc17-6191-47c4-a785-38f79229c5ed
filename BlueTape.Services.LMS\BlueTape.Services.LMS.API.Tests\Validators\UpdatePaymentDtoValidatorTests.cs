﻿using BlueTape.Services.LMS.API.Tests.ViewModels.Payments;
using BlueTape.Services.LMS.API.Validators.Payment;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class UpdatePaymentDtoValidatorTests
    {
        [Fact]
        public void Validate_ValidUpdateStatusModel_ReturnsTrue()
        {
            var validator = new UpdatePaymentDtoValidator();
            var model = ValidUpdatePaymentDto.UpdatePaymentStatusViewModel;

            var result = validator.Validate(model);

            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_ValidUpdateTransactionNumberModel_ReturnsTrue()
        {
            var validator = new UpdatePaymentDtoValidator();
            var model = ValidUpdatePaymentDto.UpdatePaymentTransactionNumberViewModel;

            var result = validator.Validate(model);

            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_EmptyModel_ReturnsFalse()
        {
            var validator = new UpdatePaymentDtoValidator();
            var model = InvalidUpdatePaymentDto.UpdatePaymentViewModel;

            var result = validator.Validate(model);

            result.IsValid.ShouldBeFalse();
        }


        [Fact]
        public void Validate_EmptyTransactionNumber_ReturnsFalse()
        {
            var validator = new UpdatePaymentDtoValidator();
            var model = InvalidUpdatePaymentDto.UpdatePaymentTransactionNumberEmptyViewModel;

            var result = validator.Validate(model);

            result.IsValid.ShouldBeFalse();
        }

        [Fact]
        public void Validate_NullTransactionNumber_ReturnsFalse()
        {
            var validator = new UpdatePaymentDtoValidator();
            var model = InvalidUpdatePaymentDto.UpdatePaymentTransactionNumberNullViewModel;

            var result = validator.Validate(model);

            result.IsValid.ShouldBeFalse();
        }


        [Fact]
        public void Validate_NullStatus_ReturnsFalse()
        {
            var validator = new UpdatePaymentDtoValidator();
            var model = InvalidUpdatePaymentDto.UpdatePaymentStatusNullViewModel;

            var result = validator.Validate(model);

            result.IsValid.ShouldBeFalse();
        }
    }
}
