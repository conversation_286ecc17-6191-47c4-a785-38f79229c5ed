﻿using BlueTape.Services.LMS.API.Tests.ViewModels.PenaltyInterest;
using BlueTape.Services.LMS.API.Validators.PenaltyFee;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.API.Tests.Validators
{
    public class UpdatePenaltyInterestFeeReceivableDtoValidatorTests
    {
        [Fact]
        public void Validate_ValidModel_ReturnsTrue()
        {
            var validator = new UpdatePenaltyInterestFeeReceivableDtoValidator();

            var model = PenaltyInterestReceivableDto.ValidPenaltyInterestFeeReceivableViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.IsValid.ShouldBeTrue();
        }

        [Fact]
        public void Validate_InvalidModel_ReturnsFalse()
        {
            var validator = new UpdatePenaltyInterestFeeReceivableDtoValidator();

            var model = PenaltyInterestReceivableDto.InvalidPenaltyInterestFeeReceivableViewModel;

            // Act
            var result = validator.Validate(model);

            // Assert
            result.Errors.ShouldNotBeEmpty();
            result.Errors.ShouldContain(x => x.PropertyName == nameof(model.ExpectedAmount));
        }
    }
}
