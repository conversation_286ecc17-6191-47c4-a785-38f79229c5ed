using BlueTape.LS.DTOs.AuthorizationPeriods;

namespace BlueTape.Services.LMS.API.Tests.ViewModels.AuthorizationPeriods;

public static class ValidCreateAuthorizationPeriodRequest
{
    private static readonly DateTime CurrentDateTime = DateTime.Now;

    public static readonly CreateAuthorizationPeriodDto ValidModel = new()
    {
        CreditId = Guid.NewGuid(),
        CreditHoldAmount = 10,
        StartDate = CurrentDateTime,
        EndDate = CurrentDateTime.AddDays(3)
    };
}