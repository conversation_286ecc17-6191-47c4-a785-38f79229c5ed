﻿using BlueTape.Services.LMS.Application.Abstractions.Services.OverDueServices;
using Quartz;

namespace BlueTape.Services.LMS.API.BackgroundServices;
[DisallowConcurrentExecution]
public class OverDueDetectorJob : IJob
{
    private const string MessageTemplate = $"Execution of {nameof(OverDueDetectorJob)}";

    private readonly ILogger<OverDueDetectorJob> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public OverDueDetectorJob(ILogger<OverDueDetectorJob> logger, IServiceScopeFactory serviceScopeFactory)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        _logger.LogInformation("{template} started", MessageTemplate);

        try
        {
            await <PERSON>le();
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{template} failed with an error", MessageTemplate);
        }

        _logger.LogInformation("{template} successfully finished.",MessageTemplate);
    }

    private async Task Handle()
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var overDueDetectorService = scope.ServiceProvider.GetRequiredService<IOverDueDetectorService>();
        await overDueDetectorService.OverdueChecking(CancellationToken.None);
    }
}