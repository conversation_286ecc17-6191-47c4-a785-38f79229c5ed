﻿namespace BlueTape.Services.LMS.API.Constants;

internal static class ControllersConstants
{
    public const string LoanReceivables = "LoanReceivables";
    public const string Loans = "Loans";
    public const string LoanTemplates = "LoanTemplates";
    public const string Payments = "Payments";
    public const string Admin = "Admin";
    public const string BasisPoint = "BasisPoint";
    public const string Credit = "Credits";
    public const string Calculator = "Calculator";
    public const string OverDueDetector = "OverDueDetector";
    public const string PenaltyDetector = "PenaltyDetector";
    public const string MonitoringService = "MonitoringService";
    public const string CreditStatusDetector = "CreditStatusDetector";
    public const string ChangeLogs = "ChangeLogs";
    public const string AutopayLoan = "AutopayLoans";
    public const string AutopayIHCLoan = "AutopayIHCLoan";
    public const string LoanParameters = "LoanParameters";
    public const string CreditHolds = "creditHolds";
    public const string AuthorizationPeriod = "authorizationPeriods";
    public const string LoanPricingPackages = "LoanPricingPackages";
    public const string CardPricingPackages = "CardPricingPackages";
}