﻿namespace BlueTape.Services.LMS.API.Constants;

internal static class EndpointConstants
{
    public const string Id = "{id:Guid}";
    public const string IdString = "{id}";
    public const string Amount = "{amount:decimal}";
    public const string ChangeExpectedDate = "LoanReceivables/{id:Guid}";
    public const string PatchAdminPayment = "Payments/{id:Guid}";
    public const string AdminLoanReceivablesByLoanId = "Loans/{id:Guid}/LoanReceivables";
    public const string Payment = "Payments";
    public const string Refund = "Refunds";
    public const string LatePaymentFee = "LatePaymentFee";
    public const string ExtensionFee = "ExtensionFee";
    public const string PenaltyInterestFee = "PenaltyInterest";
    public const string Loans = "Loans";
    public const string Company = "Company";
    public const string AutoPayment = "AutoPayment";
    public const string LoansById = "Loans/{id:Guid}";
    public const string QueryByCompanyId = "Company/{id}";
    public const string UpdateLatePaymentFeeReceivable = "LatePaymentFee/{id:Guid}";
    public const string UpdateExtensionFeeReceivable = "ExtensionFee/{id:Guid}";
    public const string LoanSettings = "Loans/{id:Guid}/Settings";
    public const string LoanAutoCollection = "Loans/{id:Guid}/autocollection";
    public const string UpdatePenaltyInterestFeeReceivable = "PenaltyInterest/{id:Guid}";
    public const string UpdateCreditDetails = "Credits/{id:Guid}/Details";
    public const string UpdateManualCreditStatus = "Credits/{id:Guid}/status";
    public const string RescheduleReceivables = "Loans/{id:Guid}/LoanReceivables/Reschedule";
    public const string LoanReceivablesPaymentsTimeline = "Payments";
    public const string Cancel = "cancel";
    public const string ChangePaymentProcessTemplate = "{id:Guid}/PaymentProcessTemplate/{templateId:Guid}";
    public const string LoanPricingPackages = "LoanPricingPackages";
    public const string UpdateLoanPricingPackages = "LoanPricingPackages/{id}";
    public const string CardPricingPackages = "CardPricingPackages";
    public const string RefinanceLoans = "Loans/Refinance";
    public const string UpdateCardPricingPackages = "CardPricingPackages/{id}";
    public const string PayablesDetailsByLoanId = "{id:Guid}/Payables/Details";
    public const string DeleteManualCreditStatus = "Credits/{id:Guid}/status/delete";
}