﻿using AutoMapper;
using BlueTape.LS.DTOs.CardPricingPackages;
using BlueTape.LS.DTOs.Credit;
using BlueTape.LS.DTOs.Credit.Note;
using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.Loan.ReceivablesReschedule;
using BlueTape.LS.DTOs.LoanPricingPackages;
using BlueTape.LS.DTOs.LoanReceivable;
using BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees;
using BlueTape.LS.DTOs.Payment;
using BlueTape.LS.DTOs.PenaltyInterest;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.ViewModels.Ids;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Abstractions.Services.CreditServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.OverDueServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.PaymentServices;
using BlueTape.Services.LMS.Application.Abstractions.Services.PenaltyServices;
using BlueTape.Services.LMS.Application.Models.CardPricingPackages;
using BlueTape.Services.LMS.Application.Models.Credits;
using BlueTape.Services.LMS.Application.Models.LoanPricingPackages;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Application.Models.LoanReceivables.LatePaymentFees;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Application.Models.Payments;
using BlueTape.Services.LMS.Application.Models.PenaltyInterest;
using BlueTape.Services.LMS.Domain.Enums;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.Admin)]
    [Authorize]
    public class AdminController(
        ILoanReceivableService _loanReceivableService,
        IManualPaymentService _manualPaymentService,
        ILoanService _loanService,
        ILateFeeReceivableService _feeReceivableService,
        IMapper _mapper,
        IValidator<UpdateReceivableFeeDto> _updateReceivableFeeViewModelValidator,
        IValidator<UpdatePenaltyInterestFeeDto> _updatePenaltyInterestFeeViewModelValidator,
        IValidator<CreateRefundPaymentDto> _createRefundPaymentViewModelValidator,
        IReceivablesRescheduleService _receivablesRescheduleService,
        IRefundsProcessService _refundsProcessService,
        IPenaltyInterestReceivableService _penaltyInterestReceivableService,
        IValidator<ManualPaymentDto> _manualPaymentViewModelValidator,
        ICreditService _creditService,
        ILoanPricingPackageService _loanPricingPackageService,
        ICardPricingPackageService _cardPricingPackageService,
        IValidator<CreateFeeReceivableDto> _createFeeReceivableDtoValidator,
        IValidator<CreatePenaltyInterestFeeReceivableDto> _createPenaltyInterestFeeReceivableDtoValidator)
        : ControllerBase
    {
        /// <summary>
        /// Create manual payment with Custom type
        /// </summary>
        /// <param name="userId">External user id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /Admin/Payments
        ///     {
        ///        "LoanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///        "Amount": 100,
        ///        "Date": "2022-10-20",
        ///        "Note": "Reason for manual payment"
        ///     }
        ///
        /// </remarks>
        [HttpPost(EndpointConstants.Payment)]
        public async Task<ChangePaymentResultDto> CreateManualPayment([FromBody] ManualPaymentDto manualPaymentViewModel, [FromHeader, Required] string userId, CancellationToken ct)
        {
            await _manualPaymentViewModelValidator.ValidateAndThrowAsync(manualPaymentViewModel, ct);

            var manualPayment = _mapper.Map<ManualPayment>(manualPaymentViewModel);
            var result = await _manualPaymentService.CreateManualPayment(manualPayment, userId, ct);

            return _mapper.Map<ChangePaymentResultDto>(result);
        }

        /// <summary>
        /// Create refund
        /// </summary>
        /// <param name="userId">External user id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /Admin/Refunds
        ///     {
        ///        "LoanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///        "Amount": 500,
        ///        "Date": "2022-10-20",
        ///        "Note": "Reason for refund"
        ///     }
        ///
        /// </remarks>
        [HttpPost(EndpointConstants.Refund)]
        public async Task<ShortPaymentDto> CreateRefundPayment([FromBody] CreateRefundPaymentDto refundPaymentViewModel, [FromHeader, Required] string userId, CancellationToken ct)
        {
            await _createRefundPaymentViewModelValidator.ValidateAndThrowAsync(refundPaymentViewModel, ct);

            var createRefundModel = _mapper.Map<CreatePaymentModel>(refundPaymentViewModel);
            createRefundModel.UserId = userId;
            var refundPayment = await _refundsProcessService.PerformRefundPayment(createRefundModel, ct);

            return _mapper.Map<ShortPaymentDto>(refundPayment);
        }

        /// <summary>
        /// Create manual late payment fee 
        /// </summary>
        /// <param name="userId">External user id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /Admin/LatePaymentFee
        ///     {
        ///        "loanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///        "expectedAmount": 100,
        ///        "expectedDate": "2022-10-20",
        ///        "note": "Reason for manual late fee creation"
        ///     }
        ///
        /// </remarks>
        [HttpPost(EndpointConstants.LatePaymentFee)]
        public async Task<LoanReceivableDto> CreateManualLatePaymentFee([FromBody] CreateFeeReceivableDto createManualLateFeeReceivableViewModel,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            await _createFeeReceivableDtoValidator.ValidateAndThrowAsync(createManualLateFeeReceivableViewModel, ct);
            var manualLateFeeReceivable = _mapper.Map<CreateFeeReceivable>(createManualLateFeeReceivableViewModel);
            manualLateFeeReceivable.FeeType = CreateManualFeeType.ManualLatePaymentFee;

            var createdLateFeeReceivable = await _feeReceivableService.CreateManualFee(manualLateFeeReceivable, userId, ct);

            return _mapper.Map<LoanReceivableDto>(createdLateFeeReceivable);
        }

        /// <summary>
        /// Create extension fee 
        /// </summary>
        /// <param name="userId">External user id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /Admin/ExtensionFee
        ///     {
        ///        "loanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///        "expectedAmount": 100,
        ///        "expectedDate": "2022-10-20",
        ///        "note": "Reason for manual extension fee creation"
        ///     }
        ///
        /// </remarks>
        [HttpPost(EndpointConstants.ExtensionFee)]
        public async Task<LoanReceivableDto> CreateExtensionFee([FromBody] CreateFeeReceivableDto createExtensionFeeReceivableViewModel,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            await _createFeeReceivableDtoValidator.ValidateAndThrowAsync(createExtensionFeeReceivableViewModel, ct);
            var extensionFeeReceivable = _mapper.Map<CreateFeeReceivable>(createExtensionFeeReceivableViewModel);
            extensionFeeReceivable.FeeType = CreateManualFeeType.ExtensionFee;

            var createdExtensionFeeReceivable = await _feeReceivableService.CreateManualFee(extensionFeeReceivable, userId, ct);

            return _mapper.Map<LoanReceivableDto>(createdExtensionFeeReceivable);
        }

        /// <summary>
        /// Create penalty interest fee 
        /// </summary>
        /// <param name="userId">External user id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /Admin/PenaltyInterest
        ///     {
        ///        "expectedDate": "2022-10-20",
        ///        "expectedAmount": 100,
        ///        "loanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///        "penaltyStartDate": "2022-10-20",
        ///        "penaltyEndDate": "2022-10-20",
        ///        "note": "Reason for manual penalty interest fee creation"
        ///     }
        ///
        /// </remarks>
        [HttpPost(EndpointConstants.PenaltyInterestFee)]
        public async Task<LoanReceivableDto> CreatePenaltyInterestFee([FromBody] CreatePenaltyInterestFeeReceivableDto createPenaltyFeeReceivableViewModel,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            await _createPenaltyInterestFeeReceivableDtoValidator.ValidateAndThrowAsync(createPenaltyFeeReceivableViewModel, ct);

            var penaltyInterestFeeReceivable = _mapper.Map<CreatePenaltyInterestFeeReceivable>(createPenaltyFeeReceivableViewModel);

            var createdPenaltyInterestFeeReceivable = await _penaltyInterestReceivableService.CreatePenaltyInterestFee(penaltyInterestFeeReceivable, userId, ct);

            return _mapper.Map<LoanReceivableDto>(createdPenaltyInterestFeeReceivable);
        }

        /// <summary>
        /// Change receivable expected date 
        /// </summary>
        /// <param name="userId">External user id</param>
        /// <param name="id">LoanReceivable id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /Admin/LoanReceivables/{id}
        ///     {
        ///        "Date": "2022-10-20",
        ///        "Note": "Reason for change date"
        ///     }
        ///
        /// </remarks>
        /// <returns></returns>
        [HttpPatch(EndpointConstants.ChangeExpectedDate)]
        public async Task<LoanReceivableDto> ChangeExpectedDate([FromRoute] Guid id, [FromBody] UpdateLoanReceivableDateDto updateLoanReceivableViewModel, [FromHeader, Required] string userId, CancellationToken ct)
        {
            var result = await _loanReceivableService.UpdateExpectedDate(id, _mapper.Map<UpdateLoanReceivableDateModel>(updateLoanReceivableViewModel), userId, ct);
            return _mapper.Map<LoanReceivableDto>(result);
        }

        /// <summary>
        /// Cancel payment 
        /// </summary>
        /// <param name="userId">External user id</param>
        /// <param name="id">Payment id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /Admin/Payments/{id}
        ///     {
        ///        "Status": "Canceled",
        ///        "Note": "Reason for change date"
        ///     }
        ///
        /// </remarks>
        [HttpPatch(EndpointConstants.PatchAdminPayment)]
        public async Task<ChangePaymentResultDto> CancelPayment([FromRoute] Guid id, [FromBody] UpdateAdminPaymentDto updateAdminPaymentViewModel, [FromHeader, Required] string userId, CancellationToken ct)
        {
            if (updateAdminPaymentViewModel.Status is BlueTape.LS.Domain.Enums.PaymentStatus.Canceled)
            {
                var result = await _manualPaymentService.CancelPayment(id, updateAdminPaymentViewModel.Note!, userId, ct);
                return _mapper.Map<ChangePaymentResultDto>(result);
            }

            return await Task.FromResult(new ChangePaymentResultDto());
        }

        /// <summary>
        /// Change loan status to defaulted / recovered / refinanced / canceled
        /// </summary>
        /// <param name="userId">External user id</param>
        /// <param name="id">Loan id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /Admin/Loans/{id}
        ///     {
        ///        "Status": "Defaulted",
        ///        "Note": "Reason for change status"
        ///     }
        ///
        /// </remarks>
        [HttpPatch(EndpointConstants.LoansById)]
        public Task ChangeLoanStatus([FromRoute] Guid id, [FromBody] UpdateLoanStatusAdminDto updateLoanStatusAdminViewModel, [FromHeader, Required] string userId,
            CancellationToken ct)
        {
            var changeLoanStatusModel = _mapper.Map<ChangeLoanStatusModel>(updateLoanStatusAdminViewModel);
            changeLoanStatusModel.Id = id;
            changeLoanStatusModel.UserId = userId;
            return _loanService.ChangeLoanStatus(changeLoanStatusModel, ct);
        }

        /// <summary>
        /// Get Loans by array of Ids
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     POST /Admin/Loans
        ///     {
        ///        "ids": [
        ///          {
        ///            "id": "e7fc1d7a-9f4c-472b-8419-7bb14ca101c6"
        ///          },
        ///          {
        ///            "id": "7f5a25b5-e147-44ba-969e-0d52a6157a79"
        ///          }
        ///       ]
        ///     }
        ///     
        /// </remarks>
        /// <returns></returns>
        [HttpPost(EndpointConstants.Loans)]
        public async Task<IEnumerable<LoanDto>> GetLoansByIds(IdsViewModel idsViewModel, [FromQuery] bool? detailed, CancellationToken ct)
        {
            if (idsViewModel.Ids is null)
            {
                return Enumerable.Empty<LoanDto>();
            }

            var ids = idsViewModel.Ids.Select(x => x.Id).ToList();

            var result = await _loanService.GetByIds(ids, detailed, ct);

            return _mapper.Map<IEnumerable<LoanDto>>(result);
        }

        /// <summary>
        /// Sync loans with MongoDb
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     PATCH /Admin/Loans
        ///         
        /// </remarks>
        /// <returns></returns>
        [HttpPatch(EndpointConstants.Loans)]
        public async Task<IEnumerable<LoanDto>?> GetLoansToSync([FromQuery] bool? detailed, CancellationToken ct)
        {
            var loans = await _loanService.GetLoansToSync(detailed, ct);
            return _mapper.Map<IEnumerable<LoanDto>>(loans);
        }

        /// <summary>
        /// Array LoanReceivables by loan id
        /// </summary>
        /// <param name="id">Loan id</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /Admin/Loans/{id}/LoanReceivables
        ///     
        /// </remarks>
        /// <returns></returns>
        [HttpGet(EndpointConstants.AdminLoanReceivablesByLoanId)]
        public async Task<IEnumerable<LoanReceivableDto>> GetByLoanId([FromRoute] Guid id, CancellationToken ct)
        {
            var loanReceivables = await _loanReceivableService.GetByLoanId(id, ct);
            return _mapper.Map<IEnumerable<LoanReceivableDto>>(loanReceivables);
        }

        /// <summary>
        /// Update loan receivables
        /// </summary>
        /// <param name="userId">External user id</param>
        /// <param name="id">Receivable id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /Admin/Loans/{id}/LoanReceivables
        ///     {
        ///        "loanReceivables": [
        ///          {
        ///            "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///            "expectedDate": "2023-06-06",
        ///            "status": "None",
        ///            "expectedAmount": 0,
        ///            "paidAmount": 0
        ///          }
        ///        ],
        ///        "note": "string"
        ///     }
        ///
        /// </remarks>
        /// <returns></returns>
        [HttpPut(EndpointConstants.AdminLoanReceivablesByLoanId)]
        public async Task<LoanDto> UpdateLoanReceivablesByLoanId([FromRoute] Guid id, [FromBody] UpdateLoanReceivablesDto updateLoanLoanReceivables, [FromHeader, Required] string userId, CancellationToken ct)
        {
            var loanReceivables = _mapper.Map<IEnumerable<LoanReceivable>>(updateLoanLoanReceivables.LoanReceivables);

            var loan = await _loanService.UpdateLoanReceivablesForLoan(loanReceivables, id, userId, ct, updateLoanLoanReceivables.Note);

            return _mapper.Map<LoanDto>(loan);
        }

        /// <summary>
        /// Reschedule Receivables
        /// </summary>
        /// <param name="id">Loan id</param>
        /// <param name="userId">External user id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /Admin/Loans/{id}/LoanReceivables/Reschedule
        ///     {
        ///        "loanReceivables": [
        ///          {
        ///            "id": "8867a199-02d1-46ca-ada0-e9e286fb6841",
        ///            "receivableType": "installment",
        ///            "newExpectedDate": "2023-01-01T00:00:00.000Z",
        ///            "newScheduleStatus": "postponed",
        ///            "newExpectedAmount": 1000
        ///          }
        ///       ],
        ///         "note": "Extending duration"
        ///     }
        ///
        /// </remarks>
        /// <returns></returns>
        [HttpPost(EndpointConstants.RescheduleReceivables)]
        public async Task<LoanDto> RescheduleReceivables([FromRoute] Guid id,
            [FromBody] ReceivablesRescheduleDto receivablesRescheduleViewModel,
            [FromHeader][Required] string userId, CancellationToken cancellationToken)
        {
            var receivablesReschedule = _mapper.Map<ReceivablesReschedule>(receivablesRescheduleViewModel);
            var loan = _mapper.Map<LoanDto>(
                await _receivablesRescheduleService.RescheduleReceivables(receivablesReschedule, id, userId,
                    cancellationToken));

            return loan;
        }

        /// <summary>
        /// Change LatePaymentFee's expected amount or status
        /// </summary>
        /// <param name="id">LatePaymentFee id</param>
        /// /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /Admin/LatePaymentFee/{id}
        ///     {
        ///         "status": "Canceled",
        ///         "note": "Note for changing status"
        ///     }
        /// 
        ///     PATCH /Admin/LatePaymentFee/{id}
        ///     {
        ///         "expectedAmount": 1000,
        ///         "note": "Note for changing amount"
        ///     }
        /// 
        /// </remarks>
        [HttpPatch(EndpointConstants.UpdateLatePaymentFeeReceivable)]
        public async Task<LoanReceivableDto> UpdateLatePaymentFeeReceivable([FromRoute] Guid id, [FromBody] UpdateReceivableFeeDto updateReceivableFeeViewModel,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            await _updateReceivableFeeViewModelValidator.ValidateAndThrowAsync(updateReceivableFeeViewModel, ct);

            var updateLateFeeModel =
                _mapper.Map<UpdateReceivableFeeModel>(updateReceivableFeeViewModel);
            updateLateFeeModel.UserId = userId;

            if (updateReceivableFeeViewModel.Status.HasValue)
            {
                var receivable = await _feeReceivableService.ChangeStatus(id,
                    updateLateFeeModel, ct);

                return _mapper.Map<LoanReceivableDto>(receivable);
            }

            if (!updateReceivableFeeViewModel.ExpectedAmount.HasValue)
                return _mapper.Map<LoanReceivableDto>(null);

            var latePaymentFee = await _feeReceivableService.ChangeExpectedAmount(id,
                updateLateFeeModel, ct);

            return _mapper.Map<LoanReceivableDto>(latePaymentFee);
        }

        /// <summary>
        /// Change ExtensionFee's expected amount or status
        /// </summary>
        /// <param name="id">Extension id</param>
        /// /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /Admin/ExtensionFee/{id}
        ///     {
        ///         "status": "Canceled",
        ///         "note": "Note for changing status"
        ///     }
        /// 
        ///     PATCH /Admin/ExtensionFee/{id}
        ///     {
        ///         "expectedAmount": 1000,
        ///         "note": "Note for changing amount"
        ///     }
        /// 
        /// </remarks>
        [HttpPatch(EndpointConstants.UpdateExtensionFeeReceivable)]
        public async Task<LoanReceivableDto> UpdateExtensionFeeReceivable([FromRoute] Guid id, [FromBody] UpdateReceivableFeeDto updateExtensionFeeViewModel,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            await _updateReceivableFeeViewModelValidator.ValidateAndThrowAsync(updateExtensionFeeViewModel, ct);

            var updateExtensionFeeModel =
                _mapper.Map<UpdateReceivableFeeModel>(updateExtensionFeeViewModel);
            updateExtensionFeeModel.UserId = userId;

            if (updateExtensionFeeViewModel.Status.HasValue)
            {
                var receivable = await _feeReceivableService.ChangeStatus(id,
                    updateExtensionFeeModel, ct);

                return _mapper.Map<LoanReceivableDto>(receivable);
            }

            if (!updateExtensionFeeViewModel.ExpectedAmount.HasValue)
                return _mapper.Map<LoanReceivableDto>(null);

            var extensionFee = await _feeReceivableService.ChangeExpectedAmount(id,
                updateExtensionFeeModel, ct);

            return _mapper.Map<LoanReceivableDto>(extensionFee);
        }

        /// <summary>
        /// Change PenaltyInterestFee's status or other fields
        /// </summary>
        /// <param name="id">PenaltyInterestFee id</param>
        /// /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /Admin/PenaltyInterest/{id}
        ///     {
        ///         "status": "Canceled",
        ///         "note": "Note for changing status"
        ///     }
        /// 
        ///     PATCH /Admin/PenaltyInterest/{id}
        ///     {
        ///         "expectedAmount": 0,
        ///         "expectedDate": "2023-09-15",
        ///         "penaltyStartDate": "2023-09-15",
        ///         "penaltyEndDate": "2023-09-15",
        ///         "note": "Note for changing"
        ///     }
        /// 
        /// </remarks>
        [HttpPatch(EndpointConstants.UpdatePenaltyInterestFeeReceivable)]
        public async Task<LoanReceivableDto> UpdatePenaltyInterestFeeReceivable([FromRoute] Guid id, [FromBody] UpdatePenaltyInterestFeeDto updatePenaltyInterestFeeViewModel,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            await _updatePenaltyInterestFeeViewModelValidator.ValidateAndThrowAsync(updatePenaltyInterestFeeViewModel, ct);

            var updatePenaltyInterestFeeModel = _mapper.Map<UpdatePenaltyInterestFeeReceivable>(updatePenaltyInterestFeeViewModel);

            var receivable = await _penaltyInterestReceivableService.UpdatePenaltyInterestFee(id, updatePenaltyInterestFeeModel, userId, ct);

            return _mapper.Map<LoanReceivableDto>(receivable);
        }

        /// <summary>
        /// Update credit details
        /// </summary>
        /// <param name="id">Credit id</param>
        /// /// <remarks>
        /// Sample request:
        /// {
        ///  "creditLimit": 0,
        ///  "purchaseType": "inventory",
        /// "revenueFallPercentage": 0,
        ///  "notes": "string"
        /// }
        /// </remarks>
        [HttpPatch(EndpointConstants.UpdateCreditDetails)]
        public async Task<CreditDto> UpdateCreditDetails([FromRoute] Guid id, [FromBody] UpdateCreditDetailsDto updateCreditDetailsViewModel,
    [FromHeader, Required] string userId, CancellationToken ct)
        {
            var updateCreditDetailsModel = _mapper.Map<UpdateCreditDetailsModel>(updateCreditDetailsViewModel);
            updateCreditDetailsModel.Id = id;
            updateCreditDetailsModel.UserId = userId;

            var credit = await _creditService.UpdateCreditDetails(updateCreditDetailsModel, ct);

            return _mapper.Map<CreditDto>(credit);
        }

        [HttpPatch(EndpointConstants.UpdateManualCreditStatus)]
        public async Task<CreditDto> UpdateManualAccountStatus(
            [FromRoute][Required] Guid id,
            [FromHeader(Name = "userId")][Required] string userId,
            [FromBody] UpdateManualCreditStatusDto viewModel,
            CancellationToken ct)
        {
            var credit = await _creditService.UpdateManualCreditStatus(new UpdateManualCreditStatusModel
            {
                NewManualCreditStatus = _mapper.Map<CreditStatus>(viewModel.NewCreditStatus),
                UpdatedBy = userId,
                Note = viewModel.Note,
                CreditId = id
            }, ct);

            return _mapper.Map<CreditDto>(credit);
        }

        [HttpPatch(EndpointConstants.DeleteManualCreditStatus)]
        public async Task<CreditDto> DeleteManualCreditStatus([FromRoute, Required] Guid id,
            [FromHeader, Required] string userId,
            [FromBody][Required] NoteDto noteDto,
            CancellationToken ct)
        {
            var credit = await _creditService.DeleteManualCreditStatus(new UpdateManualCreditStatusModel()
            {
                CreditId = id,
                UpdatedBy = userId,
                NewManualCreditStatus = null,
                Note = noteDto.Note
            }, ct);

            return _mapper.Map<CreditDto>(credit);
        }


        /// <summary>
        /// Updates a loan pricing package (admin function) All fields are optional. Which field is sent, that field to update.
        /// </summary>
        /// <param name="id">LoanPricingPackage id</param>
        ///  /// <remarks>
        /// Sample request:
        ///
        /// 
        ///     PATCH /Admin/LoanPricingPackages/{id}
        /// {
        ///     "id": "bdafd605-fb56-4d32-9c9b-3b58c174dd79",
        ///     "createdAt": "2024-10-10T10:21:44.277Z",
        ///     "createdBy": "string",
        ///     "updatedAt": "2024-10-10T10:21:44.277Z",
        ///     "updatedBy": "string",
        ///     "name": "string",
        ///     "title": "string",
        ///     "description": "string",
        ///     "code": "string",
        ///     "product": "LineOfCredit",
        ///     "status": "active",
        ///     "order": 0,
        ///     "individual": true,
        ///     "metaData": {
        ///         "merchant": 0,
        ///         "maxAmountReceived": 0,
        ///         "advanceRate": 0,
        ///         "finalPayment": 0,
        ///         "merchantRebate": 0,
        ///         "merchantFeeAfterRebate": 0,
        ///         "maxAmountReceivedAfterRebate": 0,
        ///         "customerFees30": 0,
        ///         "customerFees6090": "string",
        ///         "advancePaymentDay": "TD0",
        ///        "finalPaymentDay": "TD30"
        ///     }
        ///   }
        /// 
        /// </remarks>
        [HttpPatch(EndpointConstants.UpdateLoanPricingPackages)]
        public async Task<LoanPricingPackageDto> UpdateLoanPricingPackage([FromRoute] string id, [FromBody] ShortLoanPricingPackageDto loanPricingPackageUpdate,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            var loanPricingPackageUpdateModel = _mapper.Map<LoanPricingPackage>(loanPricingPackageUpdate);
            loanPricingPackageUpdateModel.Id = id;

            var pricingPackage = await _loanPricingPackageService.Update(loanPricingPackageUpdateModel, userId, ct);

            return _mapper.Map<LoanPricingPackageDto>(pricingPackage);
        }

        /// <summary>
        /// Adds a new loan pricing package (admin function)
        /// </summary>
        ///  /// <remarks>
        /// Sample request:
        ///
        /// 
        ///     POST /Admin/LoanPricingPackages
        /// {
        ///     "id": "bdafd605-fb56-4d32-9c9b-3b58c174dd79",
        ///     "createdAt": "2024-10-10T10:21:44.277Z",
        ///     "createdBy": "string",
        ///     "updatedAt": "2024-10-10T10:21:44.277Z",
        ///     "updatedBy": "string",
        ///     "name": "string",
        ///     "title": "string",
        ///     "description": "string",
        ///     "code": "string",
        ///     "product": "LineOfCredit",
        ///     "status": "active",
        ///     "order": 0,
        ///     "individual": true,
        ///     "metaData": {
        ///         "merchant": 0,
        ///         "maxAmountReceived": 0,
        ///         "advanceRate": 0,
        ///         "finalPayment": 0,
        ///         "merchantRebate": 0,
        ///         "merchantFeeAfterRebate": 0,
        ///         "maxAmountReceivedAfterRebate": 0,
        ///         "customerFees30": 0,
        ///         "customerFees6090": "string",
        ///         "advancePaymentDay": "TD0",
        ///        "finalPaymentDay": "TD30"
        ///     }
        ///     }
        /// 
        /// </remarks>

        [HttpPost(EndpointConstants.LoanPricingPackages)]
        public async Task<LoanPricingPackageDto> AddLoanPricingPackage([FromBody] ShortLoanPricingPackageDto loanPricingPackageAdd,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            var loanPricingPackageAddModel = _mapper.Map<ShortLoanPricingPackage>(loanPricingPackageAdd);

            var pricingPackage = await _loanPricingPackageService.Add(loanPricingPackageAddModel, userId, ct);

            return _mapper.Map<LoanPricingPackageDto>(pricingPackage);
        }

        /// <summary>
        /// Updates a card pricing package (admin function) All fields are optional. Which field is sent, that field to update.
        /// </summary>
        /// <param name="id">CardPricingPackage id</param>
        ///  /// <remarks>
        /// Sample request:
        ///
        /// 
        ///     PATCH /Admin/CardPricingPackage/{id}
        /// 
        /// </remarks>
        [HttpPatch(EndpointConstants.UpdateCardPricingPackages)]
        public async Task<CardPricingPackageDto> UpdateCardPricingPackage([FromRoute] string id, [FromBody] ShortCardPricingPackageDto cardPricingPackageUpdate,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            var cardPricingPackageUpdateModel = _mapper.Map<CardPricingPackage>(cardPricingPackageUpdate);
            cardPricingPackageUpdateModel.Id = id;

            var pricingPackage = await _cardPricingPackageService.Update(cardPricingPackageUpdateModel, userId, ct);

            return _mapper.Map<CardPricingPackageDto>(pricingPackage);
        }

        /// <summary>
        /// Adds a new card pricing package (admin function)
        /// </summary>
        ///  /// <remarks>
        /// Sample request:
        /// 
        ///     POST /Admin/CardPricingPackages
        /// 
        /// </remarks>

        [HttpPost(EndpointConstants.CardPricingPackages)]
        public async Task<CardPricingPackageDto> AddCardPricingPackage([FromBody] ShortCardPricingPackageDto cardPricingPackageAdd,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            var cardPricingPackageAddModel = _mapper.Map<ShortCardPricingPackage>(cardPricingPackageAdd);

            var pricingPackage = await _cardPricingPackageService.Add(cardPricingPackageAddModel, userId, ct);

            return _mapper.Map<CardPricingPackageDto>(pricingPackage);
        }

        /// <summary>
        /// Refinances Loans (creates a new loan by existing loan ids)
        /// </summary>
        ///  /// <remarks>
        /// Sample request:
        /// 
        ///     POST /Admin/Loans/Refinance
        /// 
        /// </remarks>

        [HttpPost(EndpointConstants.RefinanceLoans)]
        public async Task<LoanDto> RefinanceLoans([FromBody] RefinanceLoanDto refinanceLoansDto,
            [FromHeader, Required] string userId, CancellationToken ct)
        {
            var refinanceLoansModel = _mapper.Map<RefinanceLoanModel>(refinanceLoansDto);
            var loans = await _loanService.Refinance(refinanceLoansModel, userId, ct);

            return _mapper.Map<LoanDto>(loans);
        }

        [HttpPatch(EndpointConstants.LoanSettings)]
        public async Task<LoanDto> SetupLoanSettings([FromRoute] Guid id, [FromHeader, Required] string userId,
            [FromBody] LoanSettingsDto loanSettingsDto, CancellationToken ct)
        {
            var loanSettingsModel = _mapper.Map<LoanSettingsModel>(loanSettingsDto);
            var loans = await _loanService.SetupLoanSettings(id, userId, loanSettingsModel, ct);

            return _mapper.Map<LoanDto>(loans);
        }

        [HttpPatch(EndpointConstants.LoanAutoCollection)]
        public async Task<IActionResult> ChangeLoanAutoCollection([FromRoute] Guid id,
            [FromHeader, Required] string userId, [FromBody] bool isAutoCollectionPaused, CancellationToken ct)
        {
            await _loanService.ChangeLoanAutoCollection(id, userId, isAutoCollectionPaused, ct);
            return Ok();
        }
    }
}
