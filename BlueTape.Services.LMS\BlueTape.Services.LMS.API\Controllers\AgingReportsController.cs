﻿using AutoMapper;
using BlueTape.LS.Domain.Enums;
using BlueTape.Services.ARS.Application.Abstractions.Services;
using BlueTape.Services.ARS.Models.Models.AgingLoanReports;
using BlueTape.Services.ARS.Models.Models.AgingReports;
using BlueTape.Services.LMS.API.Constants;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.Admin)]
    [Authorize]
    public class AgingReportsController : ControllerBase
    {
        private readonly IAgingReportsService _agingReportsService;
        private readonly IAgingLoanReportsService _agingLoanReportsService;
        private readonly IMapper _mapper;

        public AgingReportsController(
            IAgingReportsService agingReportsService,
            IAgingLoanReportsService agingLoanReportsService,
            IMapper mapper)
        {
            _agingReportsService = agingReportsService ?? throw new ArgumentNullException(nameof(agingReportsService));
            _agingLoanReportsService = agingLoanReportsService ?? throw new ArgumentNullException(nameof(agingLoanReportsService));
            _mapper = mapper;
        }

        [HttpGet(ArsEndpointConstants.LoansAgingList)]
        public Task<AgingReport> Get([FromQuery] string? companyId, [FromQuery] ProductType? product, [FromQuery] string? merchantId, CancellationToken ct)
        {
            if (!string.IsNullOrEmpty(companyId))
            {
                return _agingReportsService.GetByCompanyId(companyId, _mapper.Map<Domain.Enums.ProductType?>(product), merchantId, ct);
            }

            return _agingReportsService.GetAll(_mapper.Map<Domain.Enums.ProductType?>(product), merchantId, ct);
        }

        [HttpGet(ArsEndpointConstants.LoansAgingListCompany)]
        public Task<IReadOnlyCollection<AgingLoanReport>> GetLoansReports([FromRoute] string companyId, [FromQuery] ProductType? product, [FromQuery] string? merchantId, CancellationToken ct)
        {
            if (!companyId.All(x => char.IsLetterOrDigit(x)) || companyId.Length != ValidatorConstants.CompanyIdLength)
                throw new ValidationException("Invalid companyId");
            return _agingLoanReportsService.GetReportsByCompanyId(companyId, _mapper.Map<Domain.Enums.ProductType?>(product), merchantId, ct);
        }
    }
}
