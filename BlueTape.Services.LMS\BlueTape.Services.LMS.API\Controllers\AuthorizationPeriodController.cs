using AutoMapper;
using BlueTape.LS.DTOs.AuthorizationPeriods;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.AuthorizationPeriods;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers;

[ApiController]
[Route(ControllersConstants.AuthorizationPeriod)]
[Authorize]
public class AuthorizationPeriodController(IMapper mapper, IAuthorizationPeriodService periodService) : ControllerBase
{
    [HttpGet(EndpointConstants.Id)]
    public async Task<AuthorizationPeriodDto> Get([FromRoute] Guid id, CancellationToken ct)
        => mapper.Map<AuthorizationPeriodDto>(await periodService.GetAuthPeriod(id, ct));

    [HttpGet]
    public async Task<IEnumerable<AuthorizationPeriodDto>?> Get([FromQuery] AuthorizationPeriodQueryDto queryRequest, CancellationToken ct)
    {
        var result = await periodService.GetAuthPeriodByFilters(mapper.Map<AuthorizationPeriodQueryModel>(queryRequest), ct);
        return mapper.Map<IEnumerable<AuthorizationPeriodDto>>(result);
    }
}