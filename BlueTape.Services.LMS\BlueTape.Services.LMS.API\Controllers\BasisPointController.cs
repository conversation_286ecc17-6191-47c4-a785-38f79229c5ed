﻿using AutoMapper;
using BlueTape.LS.DTOs.PenaltyInterest.BasisPoint;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.Query;
using BlueTape.Services.LMS.Application.Abstractions.Services.PenaltyServices;
using BlueTape.Services.LMS.Application.Models.PenaltyInterest.BasisPoint;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.BasisPoint)]
    [Authorize]
    public class BasisPointController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IBasisPointService _basisPointService;
        private readonly IValidator<ShortBasisPointDto> _basisPointValidator;

        public BasisPointController(IBasisPointService basisPointService, IMapper mapper, IValidator<ShortBasisPointDto> basisPointValidator)
        {
            _basisPointService = basisPointService;
            _mapper = mapper;
            _basisPointValidator = basisPointValidator;
        }

        [HttpGet]
        public async Task<IReadOnlyList<BasisPointDto>> GetHistoryOrByParameters([FromQuery] BasisPointQuery basisPointQuery, CancellationToken ct)
        {
            if (basisPointQuery.Id.HasValue)
            {
                var basisPoint = _mapper.Map<BasisPointDto>(await _basisPointService.GetById(basisPointQuery.Id.Value, ct));
                return new List<BasisPointDto>() { basisPoint };
            }

            if (basisPointQuery.OnDate.HasValue)
            {
                var basisPoint = _mapper.Map<BasisPointDto>(await _basisPointService.GetValidOnDate(ct, basisPointQuery.OnDate.Value));
                return new List<BasisPointDto>() { basisPoint };
            }

            var basisPoints = await _basisPointService.Get(ct);
            return _mapper.Map<IReadOnlyList<BasisPointDto>>(basisPoints);
        }

        [HttpPost]
        public async Task<BasisPointDto> Create(ShortBasisPointDto createBasisPointViewModel, CancellationToken ct)
        {
            await _basisPointValidator.ValidateAndThrowAsync(createBasisPointViewModel, ct);

            var createBasisPoint = _mapper.Map<BasisPoint>(createBasisPointViewModel);
            var basisPoint = await _basisPointService.Add(createBasisPoint, ct);

            return _mapper.Map<BasisPointDto>(basisPoint);
        }

        [HttpPut(EndpointConstants.Id)]
        public async Task<BasisPointDto> Update([FromRoute] Guid id, ShortBasisPointDto updateBasisPointViewModel, CancellationToken ct)
        {
            await _basisPointValidator.ValidateAndThrowAsync(updateBasisPointViewModel, ct);

            var updateBasisPoint = _mapper.Map<BasisPoint>(updateBasisPointViewModel);
            updateBasisPoint.Id = id;
            var basisPoint = await _basisPointService.Update(updateBasisPoint, ct);

            return _mapper.Map<BasisPointDto>(basisPoint);
        }

        [HttpDelete(EndpointConstants.Id)]
        public Task DeleteById([FromRoute] Guid id, CancellationToken ct)
        {
            return _basisPointService.Delete(id, ct);
        }
    }
}