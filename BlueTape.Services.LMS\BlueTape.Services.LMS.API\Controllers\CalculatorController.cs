using AutoMapper;
using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.Plan;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.Resources.ViewModels.Loan;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.Loans;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.Calculator)]
    [Authorize]
    public class CalculatorController : ControllerBase
    {
        private readonly ICalculatorService _calculatorService;
        private readonly IMapper _mapper;
        private readonly IValidator<CalculatorLoanDto> _calculatorLoanViewModelValidator;

        public CalculatorController(ICalculatorService calculatorService, IMapper mapper, IValidator<CalculatorLoanDto> calculatorLoanViewModelValidator)
        {
            _calculatorService = calculatorService ?? throw new ArgumentNullException(nameof(calculatorService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _calculatorLoanViewModelValidator = calculatorLoanViewModelValidator ?? throw new ArgumentNullException(nameof(calculatorLoanViewModelValidator));
        }

        /// <summary>
        /// Calculate loan fee amount and installments by template Id
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /Calculator?Amount=1&amp;LoanTemplateId={id}
        ///     
        /// </remarks>
        /// <returns></returns>
        [HttpGet]
        public async Task<LoanReceivablePlanDto> GetPaymentPlan([FromQuery] CalculatorLoanDto calculatorLoan, CancellationToken ct)
        {
            await _calculatorLoanViewModelValidator.ValidateAndThrowAsync(calculatorLoan, ct);

            var paymentPlan = await _calculatorService.GetPaymentPlan(_mapper.Map<CalculatorLoan>(calculatorLoan), ct);

            return _mapper.Map<LoanReceivablePlanDto>(paymentPlan);
        }

        /// <summary>
        /// Calculate loan fee amount and installments
        /// </summary>
        /// <param name="amount">Loan amount</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /Calculator/{amount}
        ///     
        /// </remarks>
        /// <returns></returns>
        [HttpGet(EndpointConstants.Amount)]
        public async Task<IEnumerable<LoanReceivablePlanDto>> GetPaymentPlan(decimal amount, CancellationToken ct)
        {
            if (amount <= 0) throw new ValidationException(LoanValidatorResources.AmountGreaterThanZero);
            var paymentPlans = await _calculatorService.GetPaymentPlan(amount, ct);

            return _mapper.Map<IEnumerable<LoanReceivablePlanDto>>(paymentPlans);
        }
    }
}