﻿using AutoMapper;
using BlueTape.DataAccess.Mongo.Documents.Filters;
using BlueTape.LS.DTOs.CardPricingPackages;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers;


[ApiController]
[Route(ControllersConstants.CardPricingPackages)]
[Authorize]
public class CardPricingPackagesController(IMapper mapper, ICardPricingPackageService packageService) : ControllerBase
{
    /// <summary>
    /// Gets all card pricing packages (lists only active)
    /// </summary>
    /// <param name="query">Card pricing package params</param>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /CardPricingPackages?{filter}
    ///
    /// </remarks>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<CardPricingPackageDto>> GetByFilter([FromQuery] CardPricingPackagesFilter query, CancellationToken ct)
    {

        return mapper.Map<IEnumerable<CardPricingPackageDto>>(await packageService.GetByFilter(query, ct));
    }

    /// <summary>
    /// Gets card pricing package by id (for compatibility)
    /// </summary>
    /// <param name="id">card pricing package id</param>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /CardPricingPackages/{id}
    ///
    /// </remarks>
    /// <returns></returns>
    [HttpGet(EndpointConstants.IdString)]
    public async Task<CardPricingPackageDto> GetById([FromRoute] string id, CancellationToken ct)
    {
        return mapper.Map<CardPricingPackageDto>(await packageService.GetById(id, ct));
    }
}
