﻿using AutoMapper;
using BlueTape.LS.DTOs.ChangeLog;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.ChangeLog;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{

    [ApiController]
    [Route(ControllersConstants.ChangeLogs)]
    public class ChangeLogController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IChangeLogService _changeLogService;

        public ChangeLogController(IChangeLogService changeLogService, IMapper mapper)
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _changeLogService = changeLogService ?? throw new ArgumentNullException(nameof(changeLogService));
        }

        /// <summary>
        /// Get change logs by loanId
        /// </summary>
        /// <param name="loanId">Loan Id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /ChangeLogs?loanId={id}
        /// 
        /// </remarks>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<ChangeLogDto>> Get([FromQuery] Guid loanId, CancellationToken ct)
        {
            return _mapper.Map<IEnumerable<ChangeLogDto>>(await _changeLogService.Get(l => l.LoanId == loanId, ct));
        }

        [HttpPost]
        [Authorize]
        public async Task<ChangeLogDto> CreateChangeLog([FromBody] CreateChangeLogDto dto, CancellationToken ct)
        {
            var changeLog = await _changeLogService.Add(_mapper.Map<ChangeLog>(dto), ct);
            return _mapper.Map<ChangeLogDto>(changeLog);
        }
    }
}
