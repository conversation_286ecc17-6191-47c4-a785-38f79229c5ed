﻿using AutoMapper;
using BlueTape.LS.DTOs.Credit;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services.CreditServices;
using BlueTape.Services.LMS.Application.Models.Credits;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.Credit)]
    [Authorize]
    public class CreditController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly ICreditService _creditService;
        private readonly IValidator<CreateCreditDto> _createCreditValidator;
        private readonly ICreditStatusDetectorService _creditStatusDetectorService;

        public CreditController(IMapper mapper, ICreditService creditService, IValidator<CreateCreditDto> createCreditValidator, ICreditMigrationService creditMigrationService, ICreditStatusDetectorService creditStatusDetectorService)
        {
            _mapper = mapper;
            _creditService = creditService;
            _createCreditValidator = createCreditValidator;
            _creditStatusDetectorService = creditStatusDetectorService;
        }

        /// <summary>
        /// Create a credit
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /Credits
        ///     {
        ///        "CompanyId": "12345",
        ///        "CreditApplicationId": "12345",
        ///        "ProjectId": "12345",
        ///        "StartDate": "2023-09-26",
        ///        "CreditLimit": 10000,
        ///        "Currency": "USD"
        ///     }
        ///
        /// </remarks>
        [HttpPost]
        public async Task<CreditDto> CreateCredit([FromBody] CreateCreditDto createCreditViewModel, CancellationToken ct)
        {
            await _createCreditValidator.ValidateAndThrowAsync(createCreditViewModel, ct);

            var credit = _mapper.Map<Credit>(createCreditViewModel);

            var result = await _creditService.Add(credit, ct);

            return _mapper.Map<CreditDto>(result);
        }

        [HttpGet(EndpointConstants.Id)]
        public async Task<CreditDto> Get([FromRoute] Guid id, [FromQuery] bool? detailed, CancellationToken ct)
        {
            var credit = await _creditService.GetById(id, detailed, ct);
            return _mapper.Map<CreditDto>(credit);
        }

        [HttpGet(EndpointConstants.QueryByCompanyId)]
        public async Task<IEnumerable<CreditDto>> GetByCompanyId([FromRoute] string id, [FromQuery] bool? detailed, CancellationToken ct)
        {
            var credits = await _creditService.GetByCompanyId(id, detailed, ct);
            return _mapper.Map<IEnumerable<CreditDto>>(credits);
        }

        [HttpGet]
        public async Task<IEnumerable<CreditDto>?> Get([FromQuery] CreditFilterDto filter, CancellationToken ct)
        {
            var credits = await _creditService.GetByFilter(_mapper.Map<CreditFilterModel>(filter), ct);
            return _mapper.Map<IEnumerable<CreditDto>>(credits);
        }

        [HttpPost("byProjectIds")]
        public async Task<IEnumerable<CreditDto>?> GetByProjectIds([FromBody] IEnumerable<string> projectIds, [FromQuery] bool? detailed, CancellationToken ct)
        {
            var credits = await _creditService.GetByProjectIds(projectIds, detailed, ct);
            return _mapper.Map<IEnumerable<CreditDto>>(credits);
        }

        [HttpPost(EndpointConstants.Company)]
        public async Task<IEnumerable<CreditDto>> GetByCompanyIdArray([FromBody] CompanyIdsDto model, [FromQuery] bool? detailed, CancellationToken ct)
        {
            if (model.Ids?.Length == 0 || model.Ids == null) return Enumerable.Empty<CreditDto>();
            var credits = await _creditService.GetByCompanyIdArray(model.Ids, detailed, ct);
            return _mapper.Map<IEnumerable<CreditDto>>(credits);
        }

        [HttpPatch(EndpointConstants.QueryByCompanyId)]
        public async Task<CreditDto> UpdateRevenueFallPercentage([FromRoute] string id,
            [FromQuery] double revenueFallPercentage, CancellationToken ct)
        {
            var credit = await _creditService.UpdateRevenueFallPercentage(id, revenueFallPercentage, ct);
            return _mapper.Map<CreditDto>(credit);
        }
    }
}
