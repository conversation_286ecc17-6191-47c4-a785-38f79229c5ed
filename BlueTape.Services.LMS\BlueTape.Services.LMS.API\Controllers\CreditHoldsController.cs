using AutoMapper;
using BlueTape.LS.DTOs.AuthorizationPeriods;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services.CreditServices;
using BlueTape.Services.LMS.Application.Models.AuthorizationPeriods;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers;

[ApiController]
[Route(ControllersConstants.CreditHolds)]
[Authorize]
public class CreditHoldsController(IMapper mapper,
    ICreditHoldService creditHoldService,
    IValidator<CreateAuthorizationPeriodDto> createAuthorizationPeriodValidator,
    IValidator<PatchCreditHoldExpirationDateDto> patchExpirationDateValidator) : ControllerBase
{
    [HttpGet(EndpointConstants.Id)]
    public async Task<AuthorizationPeriodDto> Get([FromRoute] Guid id, CancellationToken ct)
    {
        var result = await creditHoldService.GetCreditHold(id, ct);
        return mapper.Map<AuthorizationPeriodDto>(result);
    }

    [HttpGet]
    public async Task<IEnumerable<AuthorizationPeriodDto>> Get([FromQuery] CreditHoldQueryDto queryRequest, CancellationToken ct)
    {
        var result = await creditHoldService.GetCreditHoldsByFilters(mapper.Map<CreditHoldQueryModel>(queryRequest), ct);
        return mapper.Map<IEnumerable<AuthorizationPeriodDto>>(result);
    }

    [HttpPost]
    public async Task<AuthorizationPeriodDto?> Add([FromBody] CreateAuthorizationPeriodDto request, CancellationToken ct)
    {
        await createAuthorizationPeriodValidator.ValidateAndThrowAsync(request, ct);
        var result = await creditHoldService.CreateCreditHold(mapper.Map<CreateAuthorizationPeriodModel>(request), ct);
        return mapper.Map<AuthorizationPeriodDto>(result);
    }

    [HttpPatch($"{EndpointConstants.Id}/{EndpointConstants.Cancel}")]
    public async Task<AuthorizationPeriodDto?> Patch([FromRoute] Guid id, [FromHeader] string? userId, CancellationToken ct)
        => mapper.Map<AuthorizationPeriodDto>(await creditHoldService.CancelCreditHold(id, userId, ct));

    [HttpPatch($"{EndpointConstants.Id}")]
    public async Task<AuthorizationPeriodDto?> Patch([FromRoute] Guid id, [FromBody] PatchCreditHoldExpirationDateDto patchRequest, CancellationToken ct)
    {
        await patchExpirationDateValidator.ValidateAndThrowAsync(patchRequest, ct);
        var result = await creditHoldService.PatchExpirationDate(id, mapper.Map<PatchCreditHoldExpirationDateModel>(patchRequest), ct);
        return mapper.Map<AuthorizationPeriodDto>(result);
    }
}