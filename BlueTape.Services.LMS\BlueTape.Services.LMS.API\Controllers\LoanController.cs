using AutoMapper;
using BlueTape.LS.Domain.Models;
using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.Loan.PayablesDetails;
using BlueTape.LS.DTOs.Plan;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Models.Filters;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Application.Models.Plan;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Utilities.Models;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TinyHelpers.Extensions;
using ValidationException = BlueTape.Services.LMS.Infrastructure.Exceptions.ValidationException;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.Loans)]
    [Authorize]
    public class LoanController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly ILoanService _loanService;
        private readonly ILoanPayablesDetailsService _loanPayablesDetailsService;
        private readonly IValidator<CreateLoanDto> _shortLoanViewModelValidator;
        private readonly IValidator<LoanReplanDto> _loanReplanViewModelValidator;
        private readonly ILoanReplanService _loanReplanService;

        public LoanController(ILoanService loanService, IMapper mapper,
            IValidator<CreateLoanDto> shortLoanViewModelValidator,
            IValidator<LoanReplanDto> loanReplanViewModelValidator, ILoanReplanService loanReplanService, ILoanPayablesDetailsService loanPayablesDetailsService)
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _loanService = loanService ?? throw new ArgumentNullException(nameof(loanService));
            _shortLoanViewModelValidator = shortLoanViewModelValidator ?? throw new ArgumentNullException(nameof(shortLoanViewModelValidator));
            _loanReplanViewModelValidator = loanReplanViewModelValidator ?? throw new ArgumentNullException(nameof(loanReplanViewModelValidator));
            _loanReplanService = loanReplanService ?? throw new ArgumentNullException(nameof(loanReplanService));
            _loanPayablesDetailsService = loanPayablesDetailsService;
        }

        /// <summary>
        /// Get Loan by Id
        /// </summary>
        /// <param name="id">Loan Id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /Loans/{id}
        /// 
        /// </remarks>
        /// <returns></returns>
        [HttpGet(EndpointConstants.Id)]
        public async Task<LoanDto> Get([FromRoute] Guid id, [FromQuery] bool? detailed, CancellationToken ct)
        {
            var loan = await _loanService.GetById(id, detailed, ct);
            var result = _mapper.Map<LoanDto>(loan);

            return result;
        }

        /// <summary>
        /// Array of Loans by query params 
        /// </summary>
        /// <param name="loanQuery"></param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /Loans?UpcomingDate=2022-10-20&amp;CountDaysForUpcoming=3,
        ///     GET /Loans?FromDate=2022-10-20&amp;ToDate=2022-10-20&amp;LoanStatus=Pending,
        ///     GET /Loans
        ///     
        /// </remarks>
        /// <returns>Array of AutoPayLoan</returns>
        [HttpGet]
        public async Task<IEnumerable<LoanDto>?> Get([FromQuery] LoanQueryDto loanQuery, CancellationToken ct)
        {
            if (loanQuery.ShowLateOnly.HasValue && loanQuery.ShowLateOnly.Value)
            {
                var overdueLoans = await GetOverdue(loanQuery, ct);
                return _mapper.Map<IEnumerable<LoanDto>>(overdueLoans);
            }

            if (loanQuery.FromDate.HasValue && loanQuery.ToDate.HasValue)
            {
                var paidLoans = await GetPaid(loanQuery, ct);
                return _mapper.Map<IEnumerable<LoanDto>>(paidLoans);
            }

            if (loanQuery.EinHash.HasValue() || loanQuery.LoanStatus.HasValue)
            {
                var loans = await _loanService.GetByEinHashAndStatus(
                    _mapper.Map<LoansByEinHashAndStatusFilter>(loanQuery), loanQuery.Detailed, ct);

                return _mapper.Map<IEnumerable<LoanDto>?>(loans);
            }

            if (loanQuery.ProjectId.HasValue())
            {
                return _mapper.Map<IEnumerable<LoanDto>>(await _loanService.GetByProjectId(loanQuery.ProjectId, loanQuery.Detailed, ct));
            }

            if (loanQuery.Product.HasValue)
            {
                return _mapper.Map<IEnumerable<LoanDto>>(await _loanService.GetByProduct((ProductType)loanQuery.Product.Value, loanQuery.Detailed, ct));
            }

            if (!string.IsNullOrEmpty(loanQuery.PayableId))
            {
                return _mapper.Map<IEnumerable<LoanDto>>(await _loanService.GetByPayableId(loanQuery.PayableId, loanQuery.Detailed, ct));
            }

            if (loanQuery is { DrawApprovalIds.Length: > 0, DrawApprovalIds: not null })
            {
                return _mapper.Map<IEnumerable<LoanDto>>(await _loanService.GetByDrawApprovalIds(loanQuery.DrawApprovalIds, loanQuery.Detailed, ct));
            }

            if (!string.IsNullOrEmpty(loanQuery.DrawApprovalId))
            {
                return _mapper.Map<IEnumerable<LoanDto>>(await _loanService.GetByDrawApprovalId(loanQuery.DrawApprovalId, loanQuery.Detailed, ct));
            }

            if (loanQuery is { DownPaymentStatus.Length: > 0, DownPaymentStatus: not null })
            {
                return _mapper.Map<IEnumerable<LoanDto>>(await _loanService.GetByDownPaymentStatuses(loanQuery.DownPaymentStatus, loanQuery.Detailed, ct));
            }

            if (loanQuery.DownPaymentExpireStart.HasValue && loanQuery.DownPaymentExpireEnd.HasValue)
            {
                var downPaymentExpireStart = loanQuery.DownPaymentExpireStart ?? DateOnly.FromDateTime(DateTime.MinValue);
                var downPaymentExpireEnd = loanQuery.DownPaymentExpireEnd!.Value;
                return _mapper.Map<IEnumerable<LoanDto>>(await _loanService
                    .GetByDownPaymentExpireDate(downPaymentExpireStart, downPaymentExpireEnd, loanQuery.Detailed, ct));
            }

            var loan = await _loanService.Get(loanQuery.Detailed, ct);
            return _mapper.Map<IEnumerable<LoanDto>>(loan);
        }

        [HttpGet("query")]
        public async Task<PaginatedResponse<LoanDto>> GetByQuery([FromQuery] PaginatedLoanQuery queryParams, CancellationToken ct)
        {
            return _mapper.Map<PaginatedResponse<LoanDto>>(await _loanService.GetByQuery(_mapper.Map<Domain.Entities.PaginatedLoanQuery>(queryParams), ct));
        }

        [HttpPost("getByIds")]
        public async Task<PaginatedResponse<LoanDto>> GetByIds([FromBody] string[] ids, [FromQuery] PaginatedLoanQuery queryParams, CancellationToken cancellationToken)
        {
            queryParams.SearchIds = ids;
            var loans = await _loanService.GetByQuery(_mapper.Map<Domain.Entities.PaginatedLoanQuery>(queryParams), cancellationToken);

            return _mapper.Map<PaginatedResponse<LoanDto>>(loans);
        }

        /*        [HttpGet(EndpointConstants.AutoPayment)]
                public async Task<IEnumerable<LoanAutoPaymentViewModel>?> GetLoansForAutoPayment([FromQuery] AutoPayLoansQuery loanQuery, CancellationToken ct)
                {
                    if (loanQuery.UpcomingDate.HasValue)
                    {
                        var date = loanQuery.UpcomingDate.Value;

                        var result = await _loanService.GetUpcomingAndOverdueLoans(date.AddDays(loanQuery.CountDaysForUpcoming), null, ct);
                        return _mapper.Map<IEnumerable<LoanAutoPaymentViewModel>>(result);
                    }

                    var loan = await _loanService.Get(ct);
                    return _mapper.Map<IEnumerable<LoanAutoPaymentViewModel>>(loan);
                }*/

        private async Task<IEnumerable<LoanDto>?> GetPaid(LoanQueryDto loanQuery, CancellationToken ct)
        {
            var fromDate = loanQuery.FromDate!.Value;
            var toDate = loanQuery.ToDate!.Value;
            var paidLoans = await _loanService.GetPaid(fromDate, toDate, loanQuery.Detailed, ct);
            if (loanQuery.LoanStatus.HasValue && paidLoans is not null)
            {
                var status = loanQuery.LoanStatus.Value;
                var filteredLoans = paidLoans.Where(x => x.Status == (LoanStatus)status);

                return _mapper.Map<IEnumerable<LoanDto>?>(filteredLoans);
            }

            return _mapper.Map<IEnumerable<LoanDto>?>(paidLoans);
        }


        private async Task<IEnumerable<LoanDto>?> GetOverdue(LoanQueryDto loanQuery, CancellationToken ct)
        {
            var fromDate = loanQuery.FromDate ?? DateOnly.FromDateTime(DateTime.MinValue);
            var toDate = loanQuery.ToDate!.Value;
            var overdueLoans = await _loanService.GetOverdue(fromDate, toDate, loanQuery.Detailed, ct);
            if (loanQuery.LoanStatus.HasValue && overdueLoans is not null)
            {
                var status = loanQuery.LoanStatus.Value;
                var filteredLoans = overdueLoans.Where(x => x.Status == (LoanStatus)status);

                return _mapper.Map<IEnumerable<LoanDto>?>(filteredLoans);
            }
            return _mapper.Map<IEnumerable<LoanDto>?>(overdueLoans);
        }

        /// <summary>
        /// Create loan  
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     POST /Loans
        ///     {
        ///         "companyId": "string",
        ///         "amount": 0,
        ///         "loanTemplateId": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        ///     }
        /// 
        /// </remarks>
        [HttpPost]
        public async Task<LoanDto> Create(CreateLoanDto loanViewModel, CancellationToken ct)
        {
            await _shortLoanViewModelValidator.ValidateAndThrowAsync(loanViewModel, ct);

            var loan = await _loanService.Create(_mapper.Map<CreateLoan>(loanViewModel), ct);
            return _mapper.Map<LoanDto>(loan);
        }

        /// <summary>
        /// Replan loan
        /// </summary>
        /// <param name="id">Loan Id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /Loans/{id}
        ///     {
        ///        "newLoanTemplateId": "3ee4cbd6-935a-436e-b2d1-041ac9a5dc11",
        ///        "replanDate": "2023-05-24"
        ///     }
        ///
        /// </remarks>
        [HttpPut(EndpointConstants.Id)]
        public async Task<LoanDto> ReplanLoan([FromRoute] Guid id, LoanReplanDto loanReplanViewModel, CancellationToken ct)
        {
            await _loanReplanViewModelValidator.ValidateAndThrowAsync(loanReplanViewModel, ct);

            var loanReplan = _mapper.Map<LoanReplan>(loanReplanViewModel);
            var loan = await _loanReplanService.Replan(id, loanReplan, ct);

            return _mapper.Map<LoanDto>(loan);
        }

        /// <summary>
        /// Mark loan as deleted  
        /// </summary>
        /// <param name="id">Loan Id</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     DELETE /Loans/{id}
        /// 
        /// </remarks>
        [HttpDelete(EndpointConstants.Id)]
        public Task Delete([FromRoute] Guid id, CancellationToken ct)
        {
            return _loanService.Delete(id, ct);
        }

        /// <summary>
        /// Hard delete loans by company id and date
        /// </summary>
        /// <param name="id">Company Id</param>
        /// <param name="date">Loans created before this date will be deleted</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     DELETE /Loans/Company/{id}?date=2023-11-20
        /// 
        /// </remarks>
        [HttpDelete(EndpointConstants.QueryByCompanyId)]
        public Task DeleteByCompanyId([FromRoute] string id, [FromQuery] DateTime date, CancellationToken ct)
        {
            return _loanService.HardDeleteByCompanyId(id, date, ct);
        }

        /// <summary>
        /// Change loan status  
        /// </summary>
        /// <param name="id">Loan Id</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     PATCH /Loans/{id}
        ///     {
        ///         "status": "Started"
        ///
        ///     } 
        /// </remarks>
        [HttpPatch(EndpointConstants.Id)]
        public Task ChangeLoanStatus([FromRoute] Guid id, [FromBody] UpdateLoanDto updateLoanViewModel, CancellationToken ct)
        {
            if (updateLoanViewModel.Status.HasValue)
            {
                var changeLoanStatusModel = _mapper.Map<ChangeLoanStatusModel>(updateLoanViewModel);
                changeLoanStatusModel.Id = id;
                return _loanService.ChangeLoanStatus(changeLoanStatusModel, ct);
            }
            else
            {
                throw new ValidationException(ExceptionConstants.LoanStatusNotDefinedMessage);
            }
        }

        [HttpPut(EndpointConstants.ChangePaymentProcessTemplate)]
        public Task ChangeLoanPaymentProcessTemplate([FromRoute] Guid id, [FromRoute] Guid templateId,
            CancellationToken ct)
        {
            return _loanService.ChangePaymentProcessTemplate(id, templateId, ct);
        }


        /// <summary>
        /// Get Loan Payables Details
        /// </summary>
        /// <param name="id">Loan Id</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /Loans/{id}/Payables/Details?date=2001-01-01
        /// 
        /// </remarks>
        /// <returns></returns>
        [HttpGet(EndpointConstants.PayablesDetailsByLoanId)]
        public async Task<LoanPayablesDetailsDto> GetLoanPayablesDetails([FromRoute] Guid id, [FromQuery] DateOnly? date, CancellationToken ct)
        {
            var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);
            var payablesDetails = await _loanPayablesDetailsService.GetByLoanId(id, date ?? currentDate, ct);

            return _mapper.Map<LoanPayablesDetailsDto>(payablesDetails);
        }
    }
}