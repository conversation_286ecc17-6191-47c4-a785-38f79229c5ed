﻿using AutoMapper;
using BlueTape.LS.DTOs.LoanParameters;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.Query;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.LoanParameters)]
    [Authorize]
    public class LoanParametersController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly ILoanParametersService _loanParametersService;

        public LoanParametersController(IMapper mapper, ILoanParametersService loanParametersService)
        {
            _loanParametersService = loanParametersService;
            _mapper = mapper;
        }

        /// <summary>
        /// Get all parameter templates for specific loan
        /// </summary>
        /// <param name="query">Loan Id</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /LoanParameters?LoanId={id}
        ///
        /// </remarks>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<LoanParametersDto>> GetAll([FromQuery] LoanParametersQuery query, CancellationToken ct)
        {
            if (query.LoanId.HasValue)
            {
                var loanParameters = await _loanParametersService.GetByLoanId(query.LoanId.Value, ct);

                return _mapper.Map<IEnumerable<LoanParametersDto>>(loanParameters);
            }

            return _mapper.Map<IEnumerable<LoanParametersDto>>(await _loanParametersService.Get(ct));
        }

        /// <summary>
        /// Get parameter template by Id
        /// </summary>
        /// <param name="id">Loan Parameters Id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /LoanParameters/{id}
        /// 
        /// </remarks>
        /// <returns></returns>
        [HttpGet(EndpointConstants.Id)]
        public async Task<LoanParametersDto> GetById([FromRoute] Guid id, CancellationToken ct)
        {
            var loanParameters = _mapper.Map<LoanParametersDto>(await _loanParametersService.GetById(id, ct));

            return loanParameters;
        }
    }
}
