﻿using AutoMapper;
using BlueTape.LS.Domain.Models;
using BlueTape.LS.DTOs.LoanPricingPackages;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers;


[ApiController]
[Route(ControllersConstants.LoanPricingPackages)]
[Authorize]
public class LoanPricingPackagesController(IMapper mapper, ILoanPricingPackageService packageService) : ControllerBase
{
    /// <summary>
    /// Gets all loan pricing packages (lists only active)
    /// </summary>
    /// <param name="query">Loan Id</param>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /LoanPricingPackages?{filter}
    ///
    /// </remarks>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<LoanPricingPackageDto>> GetByFilter([FromQuery] LoanPricingPackagesFilter query, CancellationToken ct)
    {
        var mappedQuery = mapper.Map<BlueTape.DataAccess.Mongo.Documents.Filters.LoanPricingPackagesFilter>(query);
        return mapper.Map<IEnumerable<LoanPricingPackageDto>>(await packageService.GetByFilter(mappedQuery, ct));
    }

    /// <summary>
    /// Gets loan pricing package by id (for compatibility)
    /// </summary>
    /// <param name="id">Loan pricing package id</param>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /LoanPricingPackages/{id}
    ///
    /// </remarks>
    /// <returns></returns>
    [HttpGet(EndpointConstants.IdString)]
    public async Task<LoanPricingPackageDto> GetById([FromRoute] string id, CancellationToken ct)
    {
        return mapper.Map<LoanPricingPackageDto>(await packageService.GetById(id, ct));
    }
}
