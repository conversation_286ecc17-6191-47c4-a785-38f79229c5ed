﻿using AutoMapper;
using BlueTape.LS.DTOs;
using BlueTape.LS.DTOs.LoanReceivable;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.Query;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Infrastructure.Pagination;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.LoanReceivables)]
    [Authorize]
    public class LoanReceivableController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly ILoanReceivableService _loanReceivableService;
        private readonly ILoanReceivablePaymentTimelineService _timelineService;

        public LoanReceivableController(ILoanReceivableService loanReceivableService, ILoanReceivablePaymentTimelineService timelineService, IMapper mapper)
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _timelineService = timelineService ?? throw new ArgumentNullException(nameof(mapper));
            _loanReceivableService = loanReceivableService ?? throw new ArgumentNullException(nameof(loanReceivableService));
        }

        /// <summary>
        /// Get array of Receivables by query params 
        /// </summary>
        /// <param name="loanReceivableQuery">Loan Id</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /LoanReceivables?LoanId={id}
        ///     GET /LoanReceivables
        ///     
        /// </remarks>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<LoanReceivableDto>> Get([FromQuery] LoanReceivableQuery loanReceivableQuery, CancellationToken ct)
        {
            IEnumerable<LoanReceivable> loanReceivables;

            if (loanReceivableQuery.LoanId.HasValue)
            {
                loanReceivables = await _loanReceivableService.GetByLoanId(loanReceivableQuery.LoanId.Value, ct);
            }
            else
            {
                loanReceivables = await _loanReceivableService.Get(ct);
            }

            return _mapper.Map<IEnumerable<LoanReceivableDto>>(loanReceivables);
        }

        /// <summary>
        /// Get payables timeline items of loan by Loan Id 
        /// </summary>
        /// <param name="timelineItemsQuery">Loan Id</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /LoanReceivables/Payments?LoanId={id}
        ///     
        /// </remarks>
        /// <returns></returns>
        [HttpGet(EndpointConstants.LoanReceivablesPaymentsTimeline)]
        public async Task<IEnumerable<LoanReceivablesPaymentsTimelineItemDto>> GetReceivablesPaymentsTimelineItems(
            [FromQuery] TimelineItemsQuery timelineItemsQuery, CancellationToken ct)
        {
            var timelineItems = await _timelineService.GetReceivablesPaymentsTimelineItems(timelineItemsQuery.LoanId, ct);
            var timelineItemsDto = _mapper.Map<IEnumerable<LoanReceivablesPaymentsTimelineItemDto>>(timelineItems);

            return timelineItemsDto;
        }

        /// <summary>
        /// Get array of Receivables by query params 
        /// </summary>
        /// <param name="query">Params to search loan receivables</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     POST /LoanReceivables/Company
        ///     {
        ///        "pageNumber": 0,
        ///        "pageSize": 0,
        ///        "companyId": "string",
        ///        "dateFrom": "2024-01-10",
        ///        "dateTo": "2024-01-10",
        /// "loanIds": [ 
        ///        "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        ///        ],
        ///        "isOverdue": true
        ///    }
        /// /// </remarks>
        /// <returns></returns>
        [HttpPost(EndpointConstants.Company)]
        public async Task<ResultWithPaginationDto<LoanReceivableDto>> GetByCompanyIdAndStatusWithPagination(
            [FromBody] UpcomingDueReceivablesByCompanyIdQuery query, CancellationToken ct)
        {
            var result = await _loanReceivableService.GetReceivablesByCompanyIdWithPagination(query, ct);

            return _mapper.Map<ResultWithPaginationDto<LoanReceivableDto>>(result);
        }
    }
}
