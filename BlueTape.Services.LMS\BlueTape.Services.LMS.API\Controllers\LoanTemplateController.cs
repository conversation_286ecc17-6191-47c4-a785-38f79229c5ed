﻿using AutoMapper;
using BlueTape.LS.Domain.Models;
using BlueTape.LS.DTOs.LoanTemplates;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.LoanTemplates;
using BlueTape.Services.LMS.Domain.Entities.Filters;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.LoanTemplates)]
    [Authorize]
    public class LoanTemplateController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly ILoanTemplateService _loanTemplateService;
        private readonly IValidator<ShortLoanTemplateDto> _shortLoanTemplateViewModelValidator;

        public LoanTemplateController(ILoanTemplateService loanTemplateService, IMapper mapper, IValidator<ShortLoanTemplateDto> shortLoanTemplateViewModelValidator)
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _loanTemplateService = loanTemplateService ?? throw new ArgumentNullException(nameof(loanTemplateService));
            _shortLoanTemplateViewModelValidator = shortLoanTemplateViewModelValidator ?? throw new ArgumentNullException(nameof(shortLoanTemplateViewModelValidator));
        }

        /// <summary>
        /// Get loan template by Id
        /// </summary>
        /// <param name="id">Loan template Id</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /LoanTemplates/{id}
        ///
        /// </remarks>
        /// <returns></returns>
        [HttpGet(EndpointConstants.Id)]
        public async Task<LoanTemplateDto> GetById([FromRoute] Guid id, CancellationToken ct)
        {
            var loanTemplate = _mapper.Map<LoanTemplateDto>(await _loanTemplateService.GetById(id, ct));
            return loanTemplate;
        }

        /// <summary>
        /// Get all loan templates
        /// </summary>
        /// <param name="query">Loan template query</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     GET /LoanTemplates?LegacyId={legacyId}&amp;Code={code}&amp;Product={product}
        ///
        /// </remarks>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<LoanTemplateDto>> Get([FromQuery] LoanTemplatesQuery query, CancellationToken ct)
        {
            var filter = _mapper.Map<LoanTemplateFilter>(query);
            var dto = await _loanTemplateService.Get(filter, ct);
            return _mapper.Map<IEnumerable<LoanTemplateDto>>(dto);
        }

        /// <summary>
        /// Create loan template
        /// </summary>
        /// <param name="loanTemplateDto"></param>
        /// 
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /LoanTemplates
        ///     {
        ///        "LoanFeePercentage": 0.092,
        ///        "InstallmentsNumber": 1,
        ///        "PaymentDelayInDays": 120,
        ///        "PaymentIntervalInDays": 7,
        ///        "MinimumLateFeeAmount" : 35.5,
        ///        "LateFeePercentage": 1.5,
        ///        "GracePeriodInDays": 20,
        ///        "EarlyPayPeriod": 5,
        ///        "TotalDurationInDays": 10,
        ///        "Code": "120",
        ///     }
        /// 
        /// </remarks>
        [HttpPost]
        public async Task<LoanTemplateDto> Post(ShortLoanTemplateDto loanTemplateDto, CancellationToken ct)
        {
            await _shortLoanTemplateViewModelValidator.ValidateAndThrowAsync(loanTemplateDto, ct);

            var mappedObject = _mapper.Map<LoanTemplate>(loanTemplateDto);
            var result = await _loanTemplateService.Add(mappedObject, ct);
            return _mapper.Map<LoanTemplateDto>(result);
        }

        /// <summary>
        /// Update loan template
        /// </summary>
        /// <param name="id">Loan Id</param>
        /// <param name="loanTemplateDto"></param>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /LoanTemplates/{id}
        ///     {
        ///        "LoanFeePercentage": 0.092,
        ///        "InstallmentsNumber": 1,
        ///        "PaymentDelayInDays": 120,
        ///        "PaymentIntervalInDays": 7,
        ///        "MinimumLateFeeAmount" : 35.5,
        ///        "LateFeePercentage": 1.5,
        ///        "GracePeriodInDays": 20,
        ///        "EarlyPayPeriod": 5,
        ///        "TotalDurationInDays": 10,
        ///        "Code": "120",
        ///     }
        /// 
        /// </remarks>
        [HttpPut(EndpointConstants.Id)]
        public async Task<ShortLoanTemplateDto> Put([FromRoute] Guid id, [FromBody] ShortLoanTemplateDto loanTemplateDto, CancellationToken ct)
        {
            await _shortLoanTemplateViewModelValidator.ValidateAndThrowAsync(loanTemplateDto, ct);

            var mappedObject = _mapper.Map<LoanTemplate>(loanTemplateDto);
            mappedObject.Id = id;
            var result = await _loanTemplateService.Update(mappedObject, ct);
            return _mapper.Map<ShortLoanTemplateDto>(result);
        }

        /// <summary>
        /// Remove loan template
        /// </summary>
        /// <param name="id">Loan template Id</param>
        /// <remarks>
        /// Sample request:
        /// 
        ///     DELETE /LoanTemplates/{id}
        ///
        /// </remarks>
        /// <returns></returns>
        [HttpDelete(EndpointConstants.Id)]
        public Task Delete([FromRoute] Guid id, CancellationToken ct)
        {
            return _loanTemplateService.Delete(id, ct);
        }
    }
}
