﻿using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.MonitoringService.Abstractions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.MonitoringService)]
    [Authorize]
    public class MonitoringServiceController(IProcessingPaymentsService processingPaymentsService,
        ITabapayProcessingCardPaymentService tabapayProcessingCardPaymentService,
        IMissingTabapayReportFilesService missingTabapayReportFilesService) : ControllerBase
    {
        [HttpPatch("ProcessingPayments")]
        public Task CheckProcessingPayments(CancellationToken ct)
        {
            return processingPaymentsService.ProcessingPaymentsChecking(ct);
        }

        [HttpPatch("ProcessingPayments/{id:guid}")]
        public Task CheckProcessingPayments([FromRoute] Guid id, CancellationToken ct)
        {
            return processingPaymentsService.ProcessingPaymentsChecking(ct, id);
        }

        [HttpPatch("TabapayProcessingCardPayment")]
        public Task CheckTabapayProcessingCardPayment(CancellationToken ct)
        {
            return tabapayProcessingCardPaymentService.TabapayProcessingCardPaymentChecking(ct);
        }

        [HttpPatch("TabapayProcessingCardPayment/{id}")]
        public Task CheckTabapayProcessingCardPayment([FromRoute] string id, CancellationToken ct)
        {
            return tabapayProcessingCardPaymentService.TabapayProcessingCardPaymentChecking(ct, id);
        }

        [HttpPatch("MissingTabapayReportFiles")]
        public Task CheckMissingTabapayReportFiles(CancellationToken ct)
        {
            return missingTabapayReportFilesService.MissingTabapayReportFilesChecking(ct);
        }

        [HttpPatch("MissingTabapayReportFiles/{date}")]
        public Task CheckMissingTabapayReportFiles([FromRoute] DateOnly date, CancellationToken ct)
        {
            return missingTabapayReportFilesService.MissingTabapayReportFilesChecking(ct, date);
        }
    }
}
