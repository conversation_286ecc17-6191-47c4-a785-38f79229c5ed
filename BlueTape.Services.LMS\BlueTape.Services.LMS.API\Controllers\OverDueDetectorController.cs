﻿using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services.OverDueServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.OverDueDetector)]
    [Authorize]
    public class OverDueDetectorController : ControllerBase
    {
        private readonly IOverDueDetectorService _overDueDetector;
        public OverDueDetectorController(IOverDueDetectorService overDueDetector)
        {
            _overDueDetector = overDueDetector ?? throw new ArgumentNullException(nameof(overDueDetector));
        }

        /// <summary>
        /// Start Overdue Process
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     PATCH /OverDueDetector
        ///
        /// </remarks>
        [HttpPatch]
        public Task MarkAsOverdue(CancellationToken ct)
        {
            return _overDueDetector.OverdueChecking(ct);
        }

        /// <summary>
        /// Start Overdue Process for specific loan
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     PATCH /OverDueDetector/{id}
        ///
        /// </remarks>
        [HttpPatch(EndpointConstants.Id)]
        public Task MarkAsOverdue([FromRoute] Guid id, CancellationToken ct)
        {
            return _overDueDetector.OverdueChecking(ct, id);
        }
    }
}
