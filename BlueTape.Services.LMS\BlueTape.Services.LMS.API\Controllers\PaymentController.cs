﻿using AutoMapper;
using BlueTape.LS.DTOs.Payment;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.Query;
using BlueTape.Services.LMS.Application.Abstractions.Services.PaymentServices;
using BlueTape.Services.LMS.Application.Models.Payments;
using BlueTape.Services.LMS.Domain.Enums;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TinyHelpers.Extensions;

namespace BlueTape.Services.LMS.API.Controllers
{
    [ApiController]
    [Route(ControllersConstants.Payments)]
    [Authorize]
    public class PaymentController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IPaymentService _paymentService;
        private readonly IValidator<UpdatePaymentDto> _updatePaymentViewModelValidator;
        private readonly IValidator<CreatePaymentDto> _createPaymentViewModelValidator;

        public PaymentController(
            IPaymentService paymentService,
            IMapper mapper,
            IValidator<UpdatePaymentDto> updatePaymentViewModelValidator,
            IValidator<CreatePaymentDto> createPaymentViewModelValidator)
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
            _updatePaymentViewModelValidator = updatePaymentViewModelValidator ?? throw new ArgumentNullException(nameof(updatePaymentViewModelValidator));
            _createPaymentViewModelValidator = createPaymentViewModelValidator;
        }

        /// <summary>
        /// Change payment status or transaction number
        /// </summary>
        /// <param name="updatePaymentViewModel"></param>
        /// <param name="id">Payment id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /Payments/{id}
        ///     {
        ///        "Status": "Success"
        ///     }
        ///
        ///     PATCH /Payments/{id}
        ///     {
        ///        "transactionNumber": ""
        ///     }
        /// 
        /// </remarks>
        [HttpPatch(EndpointConstants.Id)]
        public async Task<ChangePaymentResultDto> UpdatePayment([FromRoute] Guid id, [FromBody] UpdatePaymentDto updatePaymentViewModel, CancellationToken ct)
        {
            await _updatePaymentViewModelValidator.ValidateAndThrowAsync(updatePaymentViewModel, ct);

            if (updatePaymentViewModel.Status.HasValue)
            {
                var result = await UpdateStatus(updatePaymentViewModel, id, ct);
                return _mapper.Map<ChangePaymentResultDto>(result);
            }

            if (!updatePaymentViewModel.TransactionNumber.IsNullOrEmpty())
            {
                var transactionNumber = updatePaymentViewModel.TransactionNumber;
                return _mapper.Map<ChangePaymentResultDto>(await _paymentService.ChangeTransactionNumber(id, transactionNumber!, ct));
            }

            return await Task.FromResult(new ChangePaymentResultDto());
        }

        /// <summary>
        /// Get payments by Loan id 
        /// </summary>
        /// <param name="paymentQuery">Loan Id</param>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /Payments?LoanId={id}
        ///
        /// </remarks>
        [HttpGet]
        public async Task<IEnumerable<ShortPaymentDto>> GetByLoanId([FromQuery] PaymentQuery paymentQuery, CancellationToken ct)
        {
            var payments = await _paymentService.GetByLoanId(paymentQuery.LoanId, ct);
            return _mapper.Map<IEnumerable<ShortPaymentDto>>(payments);
        }

        /// <summary>
        /// Create processing payment
        /// </summary>
        /// <param name="createPaymentViewModel"></param>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /Payments
        ///     {
        ///        "LoanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///        "Amount": 100,
        ///        "Type": "AutoDebit"
        ///     }
        ///
        /// </remarks>
        [HttpPost]
        public async Task<ShortPaymentDto> PerformPayment([FromBody] CreatePaymentDto createPaymentViewModel, CancellationToken ct)
        {
            await _createPaymentViewModelValidator.ValidateAndThrowAsync(createPaymentViewModel, ct);

            var createPaymentModel = _mapper.Map<CreatePaymentModel>(createPaymentViewModel);

            return _mapper.Map<ShortPaymentDto>(
                await _paymentService.PerformPayment(createPaymentModel, ct));
        }

        private Task<ChangePaymentResult> UpdateStatus(UpdatePaymentDto updatePaymentViewModel, Guid id, CancellationToken ct)
        {
            var status = _mapper.Map<PaymentStatus>(updatePaymentViewModel.Status);
            var changePaymentStatusModel = new ChangePaymentStatusModel()
            {
                PaymentId = id,
                Status = status,
            };

            return _paymentService.ChangePaymentStatus(changePaymentStatusModel, ct);
        }
    }
}
