﻿using AutoMapper;
using BlueTape.LS.DTOs.Loan;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services.PenaltyServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers
{
    [Route(ControllersConstants.PenaltyDetector)]
    [Authorize]
    [ApiController]
    public class PenaltyDetectorController : ControllerBase
    {
        private readonly IPenaltyDetectorService _penaltyDetectorService;
        private readonly IMapper _mapper;

        public PenaltyDetectorController(IPenaltyDetectorService penaltyDetectorService, IMapper mapper)
        {
            _penaltyDetectorService = penaltyDetectorService;
            _mapper = mapper;
        }

        /// <summary>
        /// Start Penalty Issuing Process
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     PATCH /PenaltyDetector
        ///
        /// </remarks>
        [HttpPatch]
        public async Task<IEnumerable<LoanDto>> IssuePenaltyInterest(CancellationToken ct)
        {
            var issuedLoans = await _penaltyDetectorService.IssuePenaltyInterest(ct: ct);
            return _mapper.Map<IEnumerable<LoanDto>>(issuedLoans);
        }

        /// <summary>
        /// Start Penalty Issuing Process for certain loan
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// 
        ///     PATCH /PenaltyDetector/3fa85f64-5717-4562-b3fc-2c963f66afa6
        ///
        /// </remarks>
        [HttpPatch(EndpointConstants.Id)]
        public async Task<LoanDto> IssuePenaltyInterest([FromRoute] Guid id, CancellationToken ct)
        {
            var issuedLoans = await _penaltyDetectorService.IssuePenaltyInterest(id, ct);
            return _mapper.Map<LoanDto>(issuedLoans.FirstOrDefault());
        }
    }
}
