﻿using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Application.Abstractions.Services.CreditServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.Services.LMS.API.Controllers;

[Route(ControllersConstants.CreditStatusDetector)]
[Authorize]
[ApiController]
public class СreditStatusDetectorController
{
    private readonly ICreditStatusDetectorService _creditStatusDetectorService;

    public СreditStatusDetectorController(ICreditStatusDetectorService creditStatusDetectorService)
    {
        _creditStatusDetectorService = creditStatusDetectorService;
    }


    /// <summary>
    /// Start credit status detector for certain credit
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     PATCH /CreditStatusDetector/{id}
    ///
    /// </remarks>
    [HttpPatch(EndpointConstants.Id)]
    public Task TriggerCreditStatusDetector([FromRoute] Guid id, CancellationToken ct)
    {
        return _creditStatusDetectorService.CreditsStatusChecking(ct, creditId: id);
    }
}
