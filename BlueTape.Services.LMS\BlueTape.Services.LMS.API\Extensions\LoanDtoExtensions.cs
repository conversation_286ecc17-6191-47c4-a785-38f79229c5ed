﻿using BlueTape.LS.DTOs.Loan;

namespace BlueTape.Services.LMS.API.Extensions;

public static class LoanDtoExtensions
{
    public static LoanDto CalculateLoanPayablesDetails(this LoanDto loanDto)
    {
        loanDto.PayablesCount = loanDto.LoanPayables!.Count;

        if (loanDto.PayablesCount <= 0) return loanDto;

        loanDto.PayableAmount = loanDto.LoanPayables.Sum(x => x.Amount).GetValueOrDefault();
        var uniqueInvoiceNumbers = loanDto.LoanPayables.DistinctBy(x => x.InvoiceNumber).Select(x => x.InvoiceNumber).ToList();
        loanDto.PayablesNumber = uniqueInvoiceNumbers.Count == 1 ? uniqueInvoiceNumbers[0] : null;

        return loanDto;
    }
}
