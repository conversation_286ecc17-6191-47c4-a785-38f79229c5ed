﻿using System.Security.Claims;
using System.Text.Encodings.Web;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.Infrastructure;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using BlueTape.Services.LMS.Infrastructure.Options;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;

namespace BlueTape.Services.LMS.API.Handlers;

public class
    ApiKeyHandler(
    IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory logger,
    UrlEncoder encoder,
    IKeyVaultService keyVaultService,
    IOptions<HttpInteractionOptions> httpInteractionOptions)
    : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    private readonly ILogger<ApiKeyHandler> _logger = LoggerFactoryExtensions.CreateLogger<ApiKeyHandler>(logger);
    private readonly HttpInteractionOptions _httpInteractionOptions = httpInteractionOptions.Value;

    private const string ApiKeyName = "ApiKey";

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var principal = new ClaimsPrincipal(new ClaimsIdentity(Enumerable.Empty<Claim>(), AuthenticationConstants.ApiKeyAuthScheme));
        var ticket = new AuthenticationTicket(principal, Scheme.Name);

        if (Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment) != EnvironmentConstants.Production) return AuthenticateResult.Success(ticket);
        if (!Request.Headers.TryGetValue(ApiKeyName, out var extractedApiKey))
        {
            return AuthenticateResult.NoResult();
        }

        var apiKey = await keyVaultService.GetSecret(_httpInteractionOptions.LoanManagementServiceApiKey
                                                     ?? throw new VariableNullException(nameof(_httpInteractionOptions.LoanManagementServiceApiKey)));

        if (!apiKey.Equals(extractedApiKey))
        {
            _logger.LogWarning("Authentication failed");
            return AuthenticateResult.Fail("Authentication failed");
        }

        _logger.LogInformation("{extractedApiKey} got access to {method}: {path}",
            extractedApiKey.ToString(),
            Request.Method,
            Request.Path.Value);

        return AuthenticateResult.Success(ticket);
    }
}