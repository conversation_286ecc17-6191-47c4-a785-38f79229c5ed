﻿using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.ViewModels.Error;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using Newtonsoft.Json;

namespace BlueTape.Services.LMS.API.Middlewares
{
    public class ExceptionMiddleware(RequestDelegate next, ILogger<ExceptionMiddleware> logger)
    {
        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await next.Invoke(context);
            }
            catch (VariableNullException ex)
            {
                await HandleExceptionAsync(context, ex, StatusCodes.Status404NotFound, ExceptionConstants.NotFoundExceptionMessage);
            }
            catch (InvalidException ex)
            {
                await HandleExceptionAsync(context, ex, StatusCodes.Status400BadRequest, ExceptionConstants.InvalidExceptionMessage);
            }
            catch (ValidationException ex)
            {
                await HandleExceptionAsync(context, ex, StatusCodes.Status400BadRequest, ExceptionConstants.ValidationExceptionMessage);
            }
            catch (FluentValidation.ValidationException ex)
            {
                await HandleExceptionAsync(context, ex, StatusCodes.Status400BadRequest, ExceptionConstants.ValidationExceptionMessage);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(context, ex, StatusCodes.Status500InternalServerError, ExceptionConstants.ExceptionMessage);
            }
        }

        private Task HandleExceptionAsync(HttpContext context, Exception ex, int statusCode, string? message = null)
        {
            logger.LogError("{message} {newLine} {innerExceptionMessage}", ex.Message, Environment.NewLine, ex.InnerException?.Message);
            logger.LogError("Error query: {query}", context.Request.Path);
            logger.LogError("{stackTrace}", ex.StackTrace);

            context.Response.StatusCode = statusCode;
            context.Response.ContentType = ContentConstants.ApplicationJsonFormat;
            var error = new ErrorViewModel { ErrorDescription = ex.Message, Message = message, StatusCode = statusCode, CorrelationId = context.TraceIdentifier};
            var jsonString = JsonConvert.SerializeObject(error);

            return context.Response.WriteAsync(jsonString);
        }
    }
}
