﻿using BlueTape.Services.LMS.Infrastructure;
using Serilog.Context;

namespace BlueTape.Services.LMS.API.Middlewares;

public class LoggingMiddleware(RequestDelegate next)
{
    public async Task Invoke(HttpContext context)
    {
        using (GlobalLogContext.PushProperty(LoggerConstants.Path, context.Request.Path))
        using (GlobalLogContext.PushProperty(LoggerConstants.Method, context.Request.Method))
        using (GlobalLogContext.PushProperty(LoggerConstants.BlueTapeCorrelationId, context.TraceIdentifier))
        {
            await next.Invoke(context);
        }
    }
}