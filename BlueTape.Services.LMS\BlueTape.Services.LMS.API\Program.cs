using AutoMapper.Extensions.ExpressionMapping;
using Azure.Identity;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.AzureKeyVault.Services;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.ARS.DI;
using BlueTape.Services.ARS.Infrastructure.Exceptions;
using BlueTape.Services.DataAccess.External.DI;
using BlueTape.Services.LMS.API.BackgroundServices;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.Extensions;
using BlueTape.Services.LMS.API.Handlers;
using BlueTape.Services.LMS.API.Mappers;
using BlueTape.Services.LMS.API.Middlewares;
using BlueTape.Services.LMS.API.Validators.Loan;
using BlueTape.Services.LMS.Application.DI;
using BlueTape.Services.LMS.Infrastructure;
using BlueTape.Services.LMS.Infrastructure.Hosting.Extensions;
using BlueTape.Services.LMS.MonitoringService.DI;
using BlueTape.Services.Reporting.DI;
using BlueTape.Services.Utilities.AspNetCore.Tracing;
using BlueTape.Services.Utilities.Options;
using FluentValidation;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Localization;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json.Converters;
using Quartz;
using System.Globalization;
using System.Reflection;
using System.Text.Json.Serialization;
using BlueTape.Services.LMS.AutoPay.DI;
using Log = Serilog.Log;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

var env = builder.Environment;
if (!env.IsDevelopment())
{
    var keyVaultUri = new Uri(Environment.GetEnvironmentVariable(InfrastructureConstants.KeyVaultUri) ??
                              throw new VariableNullException(nameof(InfrastructureConstants.KeyVaultUri)));
    var azureCredentials = new DefaultAzureCredential();

    builder.Configuration.AddAzureKeyVault(keyVaultUri, azureCredentials);
}

builder.Services.AddApplicationInsightsTelemetry(cfg =>
{
    cfg.ConnectionString = builder.Configuration.GetSection(LoggerConstants.AppInsightsConnection).Value;
});

builder.Host.ConfigureBlueTapeSerilog(LoggerConstants.ProjectValue);

builder.Services.AddQuartz(q => { q.AddJobAndTrigger<OverDueDetectorJob>(configuration); });

builder.Services.AddAuthentication(o =>
    {
        o.DefaultScheme = AuthenticationConstants.ApiKeyAuthScheme;
        o.DefaultChallengeScheme = AuthenticationConstants.ApiKeyAuthScheme;
    })
    .AddScheme<AuthenticationSchemeOptions, ApiKeyHandler>(AuthenticationConstants.ApiKeyAuthScheme, o => { });
builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");
builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    var supportedCultures = new[]
    {
        new CultureInfo("en"),
    };

    options.DefaultRequestCulture = new RequestCulture("en");
    options.SupportedCultures = supportedCultures;
    options.SupportedUICultures = supportedCultures;
});
builder.Services.AddBlueTapeTracing();
builder.Services.AddControllers()
    .AddJsonOptions(op =>
    {
        op.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
        op.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
    })
    .AddNewtonsoftJson(options =>
    {
        options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
        options.SerializerSettings.Converters.Add(new StringEnumConverter());
    });

builder.Services.AddScoped<ITraceIdAccessor, TraceIdAccessor>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddDateOnlyTimeOnlyStringConverters();
builder.Services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);
builder.Services.AddValidatorsFromAssemblyContaining<ShortLoanDtoValidator>();
builder.Services.AddAutoMapper(cfg => { cfg.AddExpressionMapping(); }, typeof(ViewModelsProfile));
builder.Services.AddApplicationDependencies(configuration);
builder.Services.AddAgingReportsDependencies(configuration);
builder.Services.AddReportingDependencies(configuration);
builder.Services.AddExternalDependencies(configuration);
builder.Services.AddMonitoringServiceDependencies(configuration);
builder.Services.AddAutoPayDependencies();

builder.Services.Configure<BlueTapeOptions>(configuration.GetSection(nameof(BlueTapeOptions)));

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
    {
        c.UseDateOnlyTimeOnlyStringConverters();

        var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
        c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));
    }
);

builder.Services.AddMemoryCache();
builder.Services.AddScoped<IKeyVaultService, KeyVaultService>();
builder.Services.AddHealthChecks();

var app = builder.Build();

await app.SeedDatabase();
app.UseBlueTapeTracing();

if (!app.Environment.IsEnvironment(EnvironmentConstants.Production))
{
    app.UseSwagger(opt => opt.PreSerializeFilters.Add((swagger, httpReq) =>
    {
        if (!httpReq.Host.Host.Contains(RouteConstants.LocalHost) && !env.IsEnvironment(EnvironmentConstants.Dev))
        {
            swagger.Servers = new List<OpenApiServer>
            {
                new()
                {
                    Url = RouteConstants.Suffix
                }
            };
        }
    }));

    app.UseSwaggerUI();
}

app.UseMiddleware<LoggingMiddleware>();
app.UseMiddleware<ExceptionMiddleware>();

app.UseRequestLocalization();

app.UseHttpsRedirection();

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");
await app.RunAsync();

await Log.CloseAndFlushAsync();