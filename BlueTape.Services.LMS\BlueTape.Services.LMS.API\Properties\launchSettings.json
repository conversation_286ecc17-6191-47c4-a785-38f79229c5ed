{"profiles": {"LoanManagementService DEV": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "dev", "LOGZIO_TOKEN": "********************************", "KEYVAULT_URI": "https://keyvault-dev-17b195c92e.vault.azure.net/"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7134;http://localhost:5134"}, "LoanManagementService QA": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "qa", "LOGZIO_TOKEN": "********************************", "KEYVAULT_URI": "https://keyvault-qa-b2c7f314826.vault.azure.net/", "AWS_REGION": "us-west-1", "AWS_DEFAULT_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************", "Branch": "Local"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7134;http://localhost:5134"}, "LoanManagementService BETA": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "beta", "LOGZIO_TOKEN": "********************************", "KEYVAULT_URI": "https://keyvault-beta-10ea6a9eb6.vault.azure.net/", "AWS_REGION": "us-west-1", "AWS_DEFAULT_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************", "Branch": "Local"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7134;http://localhost:5134"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "LOGZIO_TOKEN": "********************************", "KEYVAULT_URI": "https://keyvault-dev-17b195c92e.vault.azure.net/"}}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:29963", "sslPort": 44344}}}