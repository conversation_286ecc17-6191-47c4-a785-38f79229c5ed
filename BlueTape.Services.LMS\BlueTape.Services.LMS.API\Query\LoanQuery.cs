﻿using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.LMS.API.Query
{
    public class LoanQuery
    {
        public DateOnly? FromDate { get; set; }
        public DateOnly? ToDate { get; set; } = DateOnly.FromDateTime(DateTime.UtcNow);
        public bool? ShowLateOnly { get; set; }
        public string? EinHash { get; set; }
        public LoanStatus? LoanStatus { get; set; }
        public string? ProjectId { get; set; }
        public ProductType? Product { get; set; }
        public string? PayableId { get; set; }
        public string? DrawApprovalId { get; set; }
        public string[]? DrawApprovalIds { get; set; }
        public string[]? DownPaymentStatus { get; set; }
        public DateOnly? DownPaymentExpireStart { get; set; }
        public DateOnly? DownPaymentExpireEnd { get; set; } = DateOnly.FromDateTime(DateTime.UtcNow);
    }
}
