﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class LoanTemplateValidatorResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal LoanTemplateValidatorResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidator" +
                            "Resources", typeof(LoanTemplateValidatorResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EarlyPayPeriod should be greater than or equal 0.
        /// </summary>
        public static string EarlyPayPeriodGreaterThanZero {
            get {
                return ResourceManager.GetString("EarlyPayPeriodGreaterThanZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FeePercent should be between 0 and 1.
        /// </summary>
        public static string FeePercentBetweenZeroAndOne {
            get {
                return ResourceManager.GetString("FeePercentBetweenZeroAndOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GracePeriod should be greater than or equal 0.
        /// </summary>
        public static string GracePeriodGreaterThanZero {
            get {
                return ResourceManager.GetString("GracePeriodGreaterThanZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InstallmentsNumber should be greater than 0.
        /// </summary>
        public static string InstallmentsNumberGreaterThanZero {
            get {
                return ResourceManager.GetString("InstallmentsNumberGreaterThanZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LateFeePercent should be between 0 and 1.
        /// </summary>
        public static string LateFeePercentBetweenZeroAndOne {
            get {
                return ResourceManager.GetString("LateFeePercentBetweenZeroAndOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PaymentDelayInDays should be greater than or equal 0.
        /// </summary>
        public static string PaymentDelayInDaysGreaterThanZero {
            get {
                return ResourceManager.GetString("PaymentDelayInDaysGreaterThanZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PaymentIntervalInDays should be greater than or equal 0.
        /// </summary>
        public static string PaymentIntervalInDaysGreaterThanZero {
            get {
                return ResourceManager.GetString("PaymentIntervalInDaysGreaterThanZero", resourceCulture);
            }
        }
    }
}
