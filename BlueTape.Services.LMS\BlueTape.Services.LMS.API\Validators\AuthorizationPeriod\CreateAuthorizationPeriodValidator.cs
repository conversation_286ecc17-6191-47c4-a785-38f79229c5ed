using BlueTape.LS.DTOs.AuthorizationPeriods;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.AuthorizationPeriod;

public class CreateAuthorizationPeriodValidator : AbstractValidator<CreateAuthorizationPeriodDto>
{
    public CreateAuthorizationPeriodValidator()
    {
        RuleFor(x => x.CreditId)
            .NotEmpty()
            .WithMessage("Credit Id cannot be null")
            .NotEqual(Guid.Empty)
            .WithMessage("Credit Id cannot be empty guid");

        RuleFor(x => x.CreditHoldAmount)
            .GreaterThan(0)
            .WithMessage("Credit hold amount must be greater than zero.");

        RuleFor(x => x.StartDate)
            .NotNull()
            .WithMessage("StartDate cannot be null.")
            .GreaterThanOrEqualTo(DateTime.Today)
            .WithMessage("StartDate cannot be in the past.");

        RuleFor(x => x.EndDate)
            .NotNull()
            .WithMessage("EndDate cannot be null.")
            .GreaterThanOrEqualTo(x => x.StartDate)
            .WithMessage("EndDate cannot be sooner than StartDate.")
            .GreaterThanOrEqualTo(DateTime.Today)
            .WithMessage("EndDate cannot be in the past.");
    }
}