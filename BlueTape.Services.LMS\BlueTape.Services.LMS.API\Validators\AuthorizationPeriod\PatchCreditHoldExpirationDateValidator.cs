using BlueTape.LS.DTOs.AuthorizationPeriods;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.AuthorizationPeriod;

public class PatchCreditHoldExpirationDateValidator : AbstractValidator<PatchCreditHoldExpirationDateDto>
{
    public PatchCreditHoldExpirationDateValidator()
    {
        RuleFor(x => x.NewExpirationDate)
            .NotNull()
            .WithMessage("EndDate cannot be null.")
            .GreaterThanOrEqualTo(DateTime.Today)
            .WithMessage("EndDate cannot be in the past.");
    }
}