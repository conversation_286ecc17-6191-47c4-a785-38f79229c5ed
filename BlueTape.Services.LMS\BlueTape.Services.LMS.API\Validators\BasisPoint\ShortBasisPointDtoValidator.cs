﻿using BlueTape.LS.DTOs.PenaltyInterest.BasisPoint;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.BasisPoint
{
    public class ShortBasisPointDtoValidator : AbstractValidator<ShortBasisPointDto>
    {
        public ShortBasisPointDtoValidator()
        {
            RuleFor(x => x.BasisPointValue).NotEmpty().GreaterThan(0);
            RuleFor(x => x.ValidFrom).NotEmpty();
        }
    }
}
