﻿using BlueTape.LS.DTOs.Credit;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.Credit
{
    public class CreateCreditDtoValidator : AbstractValidator<CreateCreditDto>
    {
        public CreateCreditDtoValidator()
        {
            RuleFor(c => c.CompanyId).NotEmpty();
            RuleFor(c => c.CreditApplicationId).NotEmpty();
            RuleFor(c => c.StartDate).NotEmpty();
            RuleFor(c => c.CreditLimit).NotEmpty();
        }
    }
}
