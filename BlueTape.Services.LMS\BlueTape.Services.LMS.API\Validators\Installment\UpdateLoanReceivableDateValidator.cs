﻿using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.Installment
{
    public class UpdateLoanReceivableDateValidator : AbstractValidator<UpdateLoanReceivableDateModel>
    {
        public UpdateLoanReceivableDateValidator()
        {
            RuleFor(x => x.Date).GreaterThanOrEqualTo(DateOnly.FromDateTime(DateTime.UtcNow));
            RuleFor(x => x.Note).NotEmpty().NotNull();
        }
    }
}
