﻿using BlueTape.LS.DTOs.LoanReceivable;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.Installment
{
    public class UpdateLoanReceivableValidator : AbstractValidator<UpdateLoanReceivableDto>
    {
        public UpdateLoanReceivableValidator()
        {
            RuleFor(x => x.ExpectedDate).NotEmpty().GreaterThanOrEqualTo(DateOnly.FromDateTime(DateTime.UtcNow));
            RuleFor(x => x.ExpectedAmount).NotEmpty().NotNull().GreaterThan(0);
        }
    }
}
