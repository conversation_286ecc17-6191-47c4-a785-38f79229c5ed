﻿using BlueTape.LS.DTOs.LoanReceivable;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.Installment
{
    public class UpdateLoanReceivablesValidator : AbstractValidator<UpdateLoanReceivablesDto>
    {
        public UpdateLoanReceivablesValidator()
        {
            RuleForEach(x => x.LoanReceivables).NotNull().SetValidator(new UpdateLoanReceivableValidator());
            RuleFor(x => x.Note).NotEmpty().NotNull();
        }
    }
}
