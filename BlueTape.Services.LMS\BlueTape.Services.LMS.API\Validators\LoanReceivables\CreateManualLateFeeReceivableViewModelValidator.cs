﻿using BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.LoanReceivables
{
    public class CreateManualLateFeeReceivableViewModelValidator : AbstractValidator<CreateFeeReceivableDto>
    {
        public CreateManualLateFeeReceivableViewModelValidator()
        {
            RuleFor(x => x.ExpectedAmount).GreaterThan(0);
            RuleFor(x => x.LoanId).NotEmpty();
            RuleFor(x => x.ExpectedDate).NotEmpty();
        }
    }
}
