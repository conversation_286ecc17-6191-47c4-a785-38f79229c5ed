﻿using BlueTape.LS.DTOs.LoanTemplates;
using BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.LoanTemplate
{
    public class ShortLoanTemplateViewModelValidator : AbstractValidator<ShortLoanTemplateDto>
    {
        public ShortLoanTemplateViewModelValidator()
        {
            RuleFor(x => x.InstallmentsNumber).GreaterThan(0)
                .WithMessage(LoanTemplateValidatorResources.InstallmentsNumberGreaterThanZero);
            RuleFor(x => x.PaymentIntervalInDays).GreaterThanOrEqualTo(0)
                .WithMessage(LoanTemplateValidatorResources.PaymentIntervalInDaysGreaterThanZero);
            RuleFor(x => x.LoanFeePercentage).InclusiveBetween(0, 1)
                .WithMessage(LoanTemplateValidatorResources.FeePercentBetweenZeroAndOne);
            RuleFor(x => x.GracePeriodInDays).GreaterThanOrEqualTo(0)
                .WithMessage(LoanTemplateValidatorResources.GracePeriodGreaterThanZero);
            RuleFor(x => x.TotalDurationInDays).GreaterThanOrEqualTo(0);
        }
    }
}
