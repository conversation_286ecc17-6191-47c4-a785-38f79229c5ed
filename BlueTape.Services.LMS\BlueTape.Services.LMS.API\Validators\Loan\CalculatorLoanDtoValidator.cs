﻿using BlueTape.LS.DTOs.Loan;
using BlueTape.Services.LMS.API.Resources.ViewModels.Loan;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.Loan
{
    public class CalculatorLoanDtoValidator : AbstractValidator<CalculatorLoanDto>
    {
        public CalculatorLoanDtoValidator()
        {
            RuleFor(x => x.Amount).GreaterThan(0).WithMessage(LoanValidatorResources.AmountGreaterThanZero);
            RuleFor(x => x.LoanTemplateId).NotEmpty().WithMessage(LoanValidatorResources.LoanTemplateIdEmpty);
        }
    }
}
