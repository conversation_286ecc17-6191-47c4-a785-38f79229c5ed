﻿using BlueTape.LS.DTOs.Loan;
using BlueTape.Services.LMS.API.Resources.ViewModels.Loan;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.Loan
{
    public class ShortLoanDtoValidator : AbstractValidator<CreateLoanDto>
    {
        public ShortLoanDtoValidator()
        {
            RuleFor(x => x.CompanyId).NotEmpty().WithMessage(LoanValidatorResources.CompanyIdEmpty);
            RuleFor(x => x.Amount).GreaterThan(0).WithMessage(LoanValidatorResources.AmountGreaterThanZero);
            RuleFor(x => x.EinHash)/*.NotEmpty().WithMessage(LoanValidatorResources.EinHashEmpty)*/;
            RuleFor(x => x.LoanTemplateId).NotEmpty().WithMessage(LoanValidatorResources.LoanTemplateIdEmpty);
        }
    }
}
