﻿using BlueTape.LS.DTOs.Payment;
using BlueTape.Services.LMS.API.Constants;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.Payment
{
    public class CreatePaymentDtoValidator : AbstractValidator<CreatePaymentDto>
    {
        public CreatePaymentDtoValidator()
        {
            RuleFor(x => x.Type);
            RuleFor(x => x.Amount)
                .NotNull()
                .NotEmpty()
                .GreaterThanOrEqualTo(0.01m)
                .PrecisionScale(
                    ValidatorConstants.BeforeTheCommaCountOfDigits,
                    ValidatorConstants.AfterTheCommaCountOfDigits,
                    ValidatorConstants.NotIgnoreTrailingZeros);
            RuleFor(x => x.LoanId).NotEmpty().NotNull();
        }
    }
}
