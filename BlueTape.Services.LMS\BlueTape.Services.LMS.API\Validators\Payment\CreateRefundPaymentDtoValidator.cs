﻿using BlueTape.LS.DTOs.Payment;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.Payment
{
    public class CreateRefundPaymentDtoValidator : AbstractValidator<CreateRefundPaymentDto>
    {
        public CreateRefundPaymentDtoValidator()
        {
            RuleFor(x => x.Amount).NotNull().NotEmpty().NotEqual(0.0m);
            RuleFor(x => x.LoanId).NotNull().NotEmpty();
            RuleFor(x => x.Note).NotNull().NotEmpty();
        }
    }
}
