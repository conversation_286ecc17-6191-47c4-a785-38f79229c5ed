﻿using BlueTape.LS.DTOs.Payment;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.Payment
{
    public class ManualPaymentDtoValidator : AbstractValidator<ManualPaymentDto>
    {
        public ManualPaymentDtoValidator()
        {
            RuleFor(x => x.Amount)
                .NotNull()
                .NotEmpty()
                .GreaterThanOrEqualTo(0.01m);
        }
    }
}
