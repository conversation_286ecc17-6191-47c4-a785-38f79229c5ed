﻿using BlueTape.LS.DTOs.Payment;
using FluentValidation;
using TinyHelpers.Extensions;

namespace BlueTape.Services.LMS.API.Validators.Payment
{
    public class UpdatePaymentDtoValidator : AbstractValidator<UpdatePaymentDto>
    {
        public UpdatePaymentDtoValidator()
        {
            RuleFor(x => x.Status).NotNull().NotEmpty().IsInEnum().When(x => x.TransactionNumber.IsNullOrEmpty());
            RuleFor(x => x.TransactionNumber).NotNull().NotEmpty().When(x => !x.Status.HasValue);
        }
    }
}
