﻿using BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees;
using FluentValidation;

namespace BlueTape.Services.LoanService.API.Validators.Admin;

public class CreateFeeReceivableDtoValidator : AbstractValidator<CreateFeeReceivableDto>
{
    public CreateFeeReceivableDtoValidator()
    {
        RuleFor(x => x.ExpectedAmount).GreaterThan(0);
        RuleFor(x => x.LoanId).NotEmpty();
        RuleFor(x => x.ExpectedDate).NotEmpty();
    }
}
