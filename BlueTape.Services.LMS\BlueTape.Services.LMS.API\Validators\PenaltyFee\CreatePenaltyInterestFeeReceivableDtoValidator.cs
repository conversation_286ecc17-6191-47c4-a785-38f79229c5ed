﻿using BlueTape.LS.DTOs.PenaltyInterest;
using FluentValidation;

namespace BlueTape.Services.LoanService.API.Validators.Admin
{
    public class CreatePenaltyInterestFeeReceivableDtoValidator : AbstractValidator<CreatePenaltyInterestFeeReceivableDto>
    {
        public CreatePenaltyInterestFeeReceivableDtoValidator()
        {
            RuleFor(x => x.ExpectedDate).NotEmpty();
            RuleFor(x => x.ExpectedAmount).GreaterThan(0);
            RuleFor(x => x.LoanId).NotEmpty();
            RuleFor(x => x.PenaltyStartDate).NotEmpty();
            RuleFor(x => x.PenaltyEndDate).NotEmpty();
            RuleFor(x => x.Note).NotEmpty();
        }
    }
}
