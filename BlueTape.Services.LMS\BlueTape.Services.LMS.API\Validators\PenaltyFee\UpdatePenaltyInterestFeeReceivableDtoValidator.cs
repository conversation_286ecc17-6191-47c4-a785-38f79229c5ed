﻿using BlueTape.LS.DTOs.LoanReceivable;
using BlueTape.Services.LMS.API.Resources.ViewModels.LatePaymentFee;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.PenaltyFee
{
    public class UpdatePenaltyInterestFeeReceivableDtoValidator : AbstractValidator<UpdatePenaltyInterestFeeDto>
    {
        public UpdatePenaltyInterestFeeReceivableDtoValidator()
        {
            RuleFor(x => x.ExpectedDate).NotEmpty().When(x => !x.Status.HasValue && x.ExpectedDate.HasValue).WithMessage(FeeValidatorResources.ExpectedAmountGreaterThenZero);
            RuleFor(x => x.ExpectedAmount).GreaterThan(0).When(x => !x.Status.HasValue && x.ExpectedAmount.HasValue);
            RuleFor(x => x.PenaltyStartDate).NotEmpty().When(x => !x.Status.HasValue && x.PenaltyStartDate.HasValue);
            RuleFor(x => x.PenaltyEndDate).NotEmpty().When(x => !x.Status.HasValue && x.PenaltyEndDate.HasValue);
            RuleFor(x => x.Status).NotEmpty().NotNull().IsInEnum().When(x => !x.ExpectedAmount.HasValue);
        }
    }
}
