﻿using BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees;
using BlueTape.Services.LMS.API.Resources.ViewModels.LatePaymentFee;
using FluentValidation;

namespace BlueTape.Services.LMS.API.Validators.ReceivableFee
{
    public class UpdateReceivableFeeDtoValidator : AbstractValidator<UpdateReceivableFeeDto>
    {
        public UpdateReceivableFeeDtoValidator()
        {
            RuleFor(x => x.Status).NotEmpty().NotNull().IsInEnum().When(x => !x.ExpectedAmount.HasValue);
            RuleFor(x => x.ExpectedAmount).GreaterThan(0).When(x => !x.Status.HasValue).WithMessage(FeeValidatorResources.ExpectedAmountGreaterThenZero);
        }
    }
}
