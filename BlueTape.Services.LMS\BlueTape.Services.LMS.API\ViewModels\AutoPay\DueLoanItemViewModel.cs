﻿namespace BlueTape.Services.LMS.API.ViewModels.AutoPay;

public class DueLoanItemViewModel
{
    public Guid LoanId { get; set; }
    public List<string> PayableIds { get; set; }
    public string CompanyId { get; set; } = string.Empty;
    public decimal NextPaymentAmount { get; set; }
    public decimal OverDueAmount { get; set; }
    public DateOnly NextPaymentDate { get; set; }
    public bool IsOverdue { get; set; }
    public string DueStatus { get; set; } = string.Empty;
}
