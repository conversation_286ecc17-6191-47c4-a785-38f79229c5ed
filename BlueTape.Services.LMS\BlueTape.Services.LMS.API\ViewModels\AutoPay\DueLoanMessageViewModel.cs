﻿using BlueTape.LS.Domain.Enums;

namespace BlueTape.Services.LMS.API.ViewModels.AutoPay;

public class DueLoanMessageViewModel
{
    public string Event { get; set; } = string.Empty;
    public string BlueTapeCorrelationId { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string CompanyId { get; set; } = string.Empty;
    public decimal DueSum { get; set; }
    public int DueCount { get; set; }
    public ProductType ProductType { get; set; }
    public List<DueLoanItemViewModel> Loans { get; set; } = [];
}
