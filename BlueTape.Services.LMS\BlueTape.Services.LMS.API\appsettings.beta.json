{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.DataProtection": "Error", "Microsoft.EntityFrameworkCore": "Warning", "System.Net.Http.HttpClient": "Information", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}}, "SlackNotification": {"MonitoringSnsTopicName": "monitoring-notifications-beta"}}