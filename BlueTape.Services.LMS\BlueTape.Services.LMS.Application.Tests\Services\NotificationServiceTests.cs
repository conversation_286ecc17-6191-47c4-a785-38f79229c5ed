﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.Notification.Sender.Abstractions;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Models.LoanPayables;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Application.Services;
using BlueTape.Services.LMS.DataAccess.Company.Abstractions.Services;
using BlueTape.Services.LMS.Domain.Enums;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace BlueTape.Services.LMS.Application.Tests.Services;

public class NotificationServiceTests
{
    private readonly NotificationService _notificationService;
    private readonly Mock<ICompanyService> _companyServiceMock = new();
    private readonly Mock<ILoanService> _loanServiceMock = new();
    private readonly Mock<IAzureNotificationSenderService> _azureNotificationSenderServiceMock = new();
    private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock = new();
    private readonly Mock<ILogger<NotificationService>> _loggerMock = new();

    public NotificationServiceTests()
    {
        _traceIdAccessorMock.Setup(x => x.TraceId).Returns("test-trace-id");

        _notificationService = new NotificationService(
            _companyServiceMock.Object,
            _loanServiceMock.Object,
            _azureNotificationSenderServiceMock.Object,
            _traceIdAccessorMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task NotifyUsersInvoiceDueIn3Days_WhenNoNotificationReceivers_LogsWarning()
    {
        // Arrange
        var companyId = "test-company-id";
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = Guid.NewGuid(),
                PayableIds = new List<string> { "invoice-1" }
            },
            BankAccount = new BankAccountModel
            {
                AccountNumber = new AccountNumberModel { Display = "1234" }
            }
        };

        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserDto>());

        // Act
        await _notificationService.NotifyUsersInvoiceDueIn3Days(autoPay, CancellationToken.None);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("No notification receivers found")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);

        _azureNotificationSenderServiceMock.Verify(
            x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task NotifyUsersInvoiceDueIn3Days_WhenHasReceivers_SendsNotifications()
    {
        // Arrange
        var companyId = "test-company-id";
        var loanId = Guid.NewGuid();
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = loanId,
                PayableIds = new List<string> { "invoice-1" },
                NextPaymentDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3))
            },
            BankAccount = new BankAccountModel
            {
                AccountNumber = new AccountNumberModel { Display = "1234" }
            }
        };

        var users = new List<UserDto>
        {
            new()
            {
                Email = "<EMAIL>",
                FirstName = "Test",
                Phone = "+**********",
                CreatedAt = DateTime.UtcNow
            }
        };

        var loan = new Loan
        {
            Id = loanId,
            Amount = 1000,
            Fee = 50,
            LoanPayables = new List<LoanPayables>
            {
                new() { InvoiceNumber = "invoice-1" }
            }
        };

        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(users);

        _loanServiceMock.Setup(x => x.GetById(
                loanId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);

        SystemNotificationDto capturedNotification = null;
        _azureNotificationSenderServiceMock
            .Setup(x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()))
            .Callback<SystemNotificationDto, CancellationToken>((n, ct) => capturedNotification = n)
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.NotifyUsersInvoiceDueIn3Days(autoPay, CancellationToken.None);

        // Assert
        _azureNotificationSenderServiceMock.Verify(
            x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
            Times.Exactly(2)); // Once for SMS and once for email

        Assert.NotNull(capturedNotification);
        Assert.Equal("InvoiceIsDueIn3DaysEmail", capturedNotification.NotificationName);
        Assert.Contains("invoice-1", capturedNotification.ReferenceIds);
        Assert.Equal(companyId, capturedNotification.CompanyId);
        Assert.NotEmpty(capturedNotification.EmailDelivery);
        Assert.Single(capturedNotification.EmailDelivery);
    }

    [Fact]
    public async Task NotifyUsersInvoiceDueIn3Days_WhenServiceThrowsException_LogsErrorAndRethrows()
    {
        // Arrange
        var companyId = "test-company-id";
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = Guid.NewGuid(),
                PayableIds = new List<string> { "invoice-1" }
            },
            BankAccount = new BankAccountModel
            {
                AccountNumber = new AccountNumberModel { Display = "1234" }
            }
        };

        var expectedException = new Exception("Test exception");
        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(
            () => _notificationService.NotifyUsersInvoiceDueIn3Days(autoPay, CancellationToken.None));

        Assert.Same(expectedException, exception);

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error sending")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task NotifyUsersInstallmentDueIn3Days_WhenNoNotificationReceivers_LogsWarning()
    {
        // Arrange
        var companyId = "test-company-id";
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = Guid.NewGuid(),
                PayableIds = new List<string> { "invoice-1" }
            },
            BankAccount = new BankAccountModel
            {
                AccountNumber = new AccountNumberModel { Display = "1234" }
            }
        };

        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserDto>());

        // Act
        await _notificationService.NotifyUsersInstallmentDueIn3Days(autoPay, CancellationToken.None);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("No notification receivers found")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);

        _azureNotificationSenderServiceMock.Verify(
            x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task NotifyUsersInstallmentDueIn3Days_WhenHasReceivers_SendsNotifications()
    {
        // Arrange
        var companyId = "test-company-id";
        var loanId = Guid.NewGuid();
        var nextPaymentDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = loanId,
                PayableIds = new List<string> { "invoice-1" },
                NextPaymentDate = nextPaymentDate
            },
            BankAccount = new BankAccountModel
            {
                AccountNumber = new AccountNumberModel { Display = "1234" }
            }
        };

        var users = new List<UserDto>
        {
            new()
            {
                Email = "<EMAIL>",
                FirstName = "Test",
                Phone = "+**********",
                CreatedAt = DateTime.UtcNow
            }
        };

        var loan = new Loan
        {
            Id = loanId,
            Amount = 1000,
            Fee = 50,
            LoanPayables = new List<LoanPayables>
            {
                new() { InvoiceNumber = "invoice-1" }
            },
            LoanReceivables = new List<LoanReceivable>
            {
                new() 
                { 
                    Type = ReceivableType.Installment,
                    ExpectedDate = nextPaymentDate,
                    ExpectedAmount = 200
                },
                new() 
                { 
                    Type = ReceivableType.Installment,
                    ExpectedDate = nextPaymentDate.AddMonths(1),
                    ExpectedAmount = 200
                }
            }
        };

        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(users);

        _loanServiceMock.Setup(x => x.GetById(
                loanId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);

        SystemNotificationDto capturedNotification = null;
        _azureNotificationSenderServiceMock
            .Setup(x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()))
            .Callback<SystemNotificationDto, CancellationToken>((n, ct) => capturedNotification = n)
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.NotifyUsersInstallmentDueIn3Days(autoPay, CancellationToken.None);

        // Assert
        _azureNotificationSenderServiceMock.Verify(
            x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
            Times.Exactly(2)); // Once for SMS and once for email

        Assert.NotNull(capturedNotification);
        Assert.Equal("InstallmentIsDueIn3DaysEmail", capturedNotification.NotificationName);
        Assert.Contains("invoice-1", capturedNotification.ReferenceIds);
        Assert.Equal(companyId, capturedNotification.CompanyId);
        Assert.Single(capturedNotification.EmailDelivery);
        Assert.Equal("d-2e7614aa26dd43299b0ca2779ef343a9", capturedNotification.EmailDelivery[0].Payload.TemplateId);
    }

    [Fact]
    public async Task NotifyUsersInstallmentDueIn3Days_WhenUserHasNoPhone_SkipsSmsNotification()
    {
        // Arrange
        var companyId = "test-company-id";
        var loanId = Guid.NewGuid();
        var nextPaymentDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = loanId,
                PayableIds = new List<string> { "invoice-1" },
                NextPaymentDate = nextPaymentDate
            },
            BankAccount = new BankAccountModel
            {
                AccountNumber = new AccountNumberModel { Display = "1234" }
            }
        };

        var users = new List<UserDto>
        {
            new()
            {
                Email = "<EMAIL>",
                FirstName = "Test",
                Phone = null, // No phone number
                CreatedAt = DateTime.UtcNow
            }
        };

        var loan = new Loan
        {
            Id = loanId,
            Amount = 1000,
            Fee = 50,
            LoanPayables = new List<LoanPayables>
            {
                new() { InvoiceNumber = "invoice-1" }
            },
            LoanReceivables = new List<LoanReceivable>
            {
                new() 
                { 
                    Type = ReceivableType.Installment,
                    ExpectedDate = nextPaymentDate,
                    ExpectedAmount = 200
                }
            }
        };

        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(users);

        _loanServiceMock.Setup(x => x.GetById(
                loanId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);

        // Act
        await _notificationService.NotifyUsersInstallmentDueIn3Days(autoPay, CancellationToken.None);

        // Assert
        _azureNotificationSenderServiceMock.Verify(
            x => x.Send(It.Is<SystemNotificationDto>(n => n.SmsDelivery != null && n.SmsDelivery.Count == 0), 
            It.IsAny<CancellationToken>()),
            Times.Once); // Only email notification should be sent
    }

    [Fact]
    public async Task NotifyUsersInstallmentDueIn3Days_WhenServiceThrowsException_LogsErrorAndRethrows()
    {
        // Arrange
        var companyId = "test-company-id";
        var loanId = Guid.NewGuid();
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = loanId,
                PayableIds = new List<string> { "invoice-1" }
            }
        };

        var expectedException = new Exception("Test exception");
        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(
            () => _notificationService.NotifyUsersInstallmentDueIn3Days(autoPay, CancellationToken.None));

        Assert.Same(expectedException, exception);
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error sending")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task SendSmsNotification_WhenSuccessful_SendsNotification()
    {
        // Arrange
        var user = new UserDto
        {
            Name = "Test User",
            Phone = "+**********"
        };
        var companyId = "test-company-id";
        var message = "Test message";

        SystemNotificationDto capturedNotification = null;
        _azureNotificationSenderServiceMock
            .Setup(x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()))
            .Callback<SystemNotificationDto, CancellationToken>((n, ct) => capturedNotification = n)
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.SendSmsNotification(user, companyId, message, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedNotification);
        Assert.Equal("SmsNotifications", capturedNotification.NotificationName);
        Assert.Equal(companyId, capturedNotification.CompanyId);
        Assert.Single(capturedNotification.SmsDelivery);
        Assert.Equal(message, capturedNotification.SmsDelivery[0].Payload.Message);
        Assert.Equal(user.Phone, capturedNotification.SmsDelivery[0].Payload.Receivers[0].PhoneNumber);
    }

    [Fact]
    public async Task SendSmsNotification_WhenServiceThrowsException_LogsErrorAndRethrows()
    {
        // Arrange
        var user = new UserDto
        {
            Name = "Test User",
            Phone = "+**********"
        };
        var companyId = "test-company-id";
        var message = "Test message";

        var expectedException = new Exception("Test exception");
        _azureNotificationSenderServiceMock
            .Setup(x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(
            () => _notificationService.SendSmsNotification(user, companyId, message, CancellationToken.None));

        Assert.Same(expectedException, exception);
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error sending down payment notification")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public void GetNotification_ReturnsCorrectNotificationObject()
    {
        // Arrange
        var notificationName = "TestNotification";
        var description = "Test description";
        var referenceIds = new List<string> { "ref1", "ref2" };
        var companyId = "test-company-id";

        // Act
        var notification = _notificationService.GetNotification(notificationName, description, referenceIds, companyId);

        // Assert
        Assert.NotNull(notification);
        Assert.Equal(notificationName, notification.NotificationName);
        Assert.Equal(description, notification.Description);
        Assert.Equal(referenceIds, notification.ReferenceIds);
        Assert.Equal(companyId, notification.CompanyId);
        Assert.Equal("test-trace-id", notification.TraceId);
        Assert.NotNull(notification.EmailDelivery);
        Assert.NotNull(notification.SmsDelivery);
        Assert.NotNull(notification.UserUiReviewDelivery);
        Assert.NotNull(notification.BlueTapeBackOfficeDelivery);
        Assert.Empty(notification.EmailDelivery);
        Assert.Empty(notification.SmsDelivery);
        Assert.Empty(notification.UserUiReviewDelivery);
        Assert.Empty(notification.BlueTapeBackOfficeDelivery);
    }

    [Fact]
    public async Task NotifyInvoiceApprovedSinglePaymentDue_WhenNoNotificationReceivers_LogsWarning()
    {
        // Arrange
        var companyId = "test-company-id";
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = Guid.NewGuid(),
                PayableIds = new List<string> { "invoice-1" }
            }
        };

        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserDto>());

        // Act
        await _notificationService.NotifyInvoiceApprovedSinglePaymentDue(autoPay, CancellationToken.None);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("No notification receivers found")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);

        _azureNotificationSenderServiceMock.Verify(
            x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task NotifyInvoiceApprovedSinglePaymentDue_WhenHasReceivers_SendsNotifications()
    {
        // Arrange
        var companyId = "test-company-id";
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = Guid.NewGuid(),
                PayableIds = new List<string> { "invoice-1" }
            }
        };

        var users = new List<UserDto>
        {
            new()
            {
                Email = "<EMAIL>",
                FirstName = "Test",
                CreatedAt = DateTime.UtcNow
            }
        };

        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(users);

        SystemNotificationDto capturedNotification = null;
        _azureNotificationSenderServiceMock
            .Setup(x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()))
            .Callback<SystemNotificationDto, CancellationToken>((n, ct) => capturedNotification = n)
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.NotifyInvoiceApprovedSinglePaymentDue(autoPay, CancellationToken.None);

        // Assert
        _azureNotificationSenderServiceMock.Verify(
            x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
            Times.Once);

        Assert.NotNull(capturedNotification);
        Assert.Equal("InvoiceApprovedSinglePaymentDue", capturedNotification.NotificationName);
        Assert.Contains("invoice-1", capturedNotification.ReferenceIds);
        Assert.Equal(companyId, capturedNotification.CompanyId);
        Assert.Single(capturedNotification.EmailDelivery);
        Assert.Equal("d-acb33d2f4ca94f5c80097f728475ec39", capturedNotification.EmailDelivery[0].Payload.TemplateId);
    }

    [Fact]
    public async Task NotifyInvoiceApprovedSinglePaymentDue_WhenServiceThrowsException_LogsErrorAndRethrows()
    {
        // Arrange
        var companyId = "test-company-id";
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = Guid.NewGuid(),
                PayableIds = new List<string> { "invoice-1" }
            }
        };

        var expectedException = new Exception("Test exception");
        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(
            () => _notificationService.NotifyInvoiceApprovedSinglePaymentDue(autoPay, CancellationToken.None));

        Assert.Same(expectedException, exception);
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error sending")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task NotifyInvoiceApprovedInstallmentPlan_WhenNoNotificationReceivers_LogsWarning()
    {
        // Arrange
        var companyId = "test-company-id";
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = Guid.NewGuid(),
                PayableIds = new List<string> { "invoice-1" }
            }
        };

        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserDto>());

        // Act
        await _notificationService.NotifyInvoiceApprovedInstallmentPlan(autoPay, CancellationToken.None);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("No notification receivers found")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);

        _azureNotificationSenderServiceMock.Verify(
            x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task NotifyInvoiceApprovedInstallmentPlan_WhenHasReceivers_SendsNotifications()
    {
        // Arrange
        var companyId = "test-company-id";
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = Guid.NewGuid(),
                PayableIds = new List<string> { "invoice-1" }
            }
        };

        var users = new List<UserDto>
        {
            new()
            {
                Email = "<EMAIL>",
                FirstName = "Test",
                CreatedAt = DateTime.UtcNow
            }
        };

        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(users);

        SystemNotificationDto capturedNotification = null;
        _azureNotificationSenderServiceMock
            .Setup(x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()))
            .Callback<SystemNotificationDto, CancellationToken>((n, ct) => capturedNotification = n)
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.NotifyInvoiceApprovedInstallmentPlan(autoPay, CancellationToken.None);

        // Assert
        _azureNotificationSenderServiceMock.Verify(
            x => x.Send(It.IsAny<SystemNotificationDto>(), It.IsAny<CancellationToken>()),
            Times.Once);

        Assert.NotNull(capturedNotification);
        Assert.Equal("InvoiceApprovedInstallmentPlan", capturedNotification.NotificationName);
        Assert.Contains("invoice-1", capturedNotification.ReferenceIds);
        Assert.Equal(companyId, capturedNotification.CompanyId);
        Assert.Single(capturedNotification.EmailDelivery);
        Assert.Equal("d-4d10d0de54914d088632b5150b8b7454", capturedNotification.EmailDelivery[0].Payload.TemplateId);
    }

    [Fact]
    public async Task NotifyInvoiceApprovedInstallmentPlan_WhenServiceThrowsException_LogsErrorAndRethrows()
    {
        // Arrange
        var companyId = "test-company-id";
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = Guid.NewGuid(),
                PayableIds = new List<string> { "invoice-1" }
            }
        };

        var expectedException = new Exception("Test exception");
        _companyServiceMock.Setup(x => x.GetCompanyNotificationReceivers(
                companyId,
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(
            () => _notificationService.NotifyInvoiceApprovedInstallmentPlan(autoPay, CancellationToken.None));

        Assert.Same(expectedException, exception);
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error sending")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }
}
