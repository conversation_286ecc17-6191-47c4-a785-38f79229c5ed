﻿using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.Companies;
using BlueTape.LinqpalClient.Abstractions;
using BlueTape.LinqpalClient.DTOs;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Messages.DrawRepayment;
using BlueTape.PaymentService.Messages.IhcRepayment;
using BlueTape.PaymentService.Senders;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Services;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace BlueTape.Services.LMS.Application.Tests.Services;

public class PaymentExternalServiceTests
{
    private readonly Mock<IDrawRepaymentMessageSender> _drawRepaymentMessageSenderMock;
    private readonly Mock<IIhcRepaymentMessageSender> _ihcRepaymentMessageSenderMock;
    private readonly Mock<ICompanyHttpClient> _companyClientMock;
    private readonly Mock<ILinqpalHttpClient> _linqpalHttpClientMock;
    private readonly Mock<ILogger<PaymentExternalService>> _loggerMock;
    private readonly PaymentExternalService _service;

    public PaymentExternalServiceTests()
    {
        _drawRepaymentMessageSenderMock = new Mock<IDrawRepaymentMessageSender>();
        _ihcRepaymentMessageSenderMock = new Mock<IIhcRepaymentMessageSender>();
        _companyClientMock = new Mock<ICompanyHttpClient>();
        _linqpalHttpClientMock = new Mock<ILinqpalHttpClient>();
        _loggerMock = new Mock<ILogger<PaymentExternalService>>();

        _service = new PaymentExternalService(
            _drawRepaymentMessageSenderMock.Object,
            _ihcRepaymentMessageSenderMock.Object,
            _companyClientMock.Object,
            _linqpalHttpClientMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task CreateDrawRepaymentAch_SendsCorrectMessage()
    {
        // Arrange
        var companyId = "test-company";
        var loanId = Guid.NewGuid();
        var bankAccountId = "test-bank-account";
        var paymentAmount = 1000m;

        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = loanId,
                PayableIds = new List<string> { "invoice-1" }
            },
            BankAccount = new BankAccountModel
            {
                Id = bankAccountId
            },
            PaymentAmount = paymentAmount
        };

        var company = new CompanyModel
        {
            LegalName = "Test Company Legal Name",
            Name = "Test Company"
        };

        _companyClientMock.Setup(x => x.GetCompanyByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(company);

        ServiceBusMessageBt<DrawRepaymentRequestMessage> capturedMessage = null;
        _drawRepaymentMessageSenderMock
            .Setup(x => x.SendMessage(It.IsAny<ServiceBusMessageBt<DrawRepaymentRequestMessage>>(), It.IsAny<CancellationToken>()))
            .Callback<ServiceBusMessageBt<DrawRepaymentRequestMessage>, CancellationToken>((m, ct) => capturedMessage = m)
            .Returns(Task.CompletedTask);

        // Act
        await _service.CreateDrawRepaymentAch(autoPay, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedMessage);
        var message = capturedMessage.MessageBody;
        Assert.Equal(DomainConstants.DrawRepayment, message.FlowTemplateCode);
        Assert.Equal("LMS autopay", message.CreatedBy);
        Assert.NotNull(message.BlueTapeCorrelationId);

        var details = message.PaymentRequestDetails;
        Assert.Equal("USD", details.Currency);
        Assert.Equal(paymentAmount, details.RequestedAmount);
        Assert.Equal("ach", details.PaymentMethod);
        Assert.Equal(loanId, details.DrawRepaymentDrawDetails.Id);
        Assert.Equal(companyId, details.CustomerDetails.Id);
        Assert.Equal(company.LegalName, details.CustomerDetails.Name);
        Assert.Equal(bankAccountId, details.CustomerDetails.AccountId);
    }

    [Fact]
    public async Task CreateIhcRepaymentAch_SendsCorrectMessage()
    {
        // Arrange
        var companyId = "test-company";
        var loanId = Guid.NewGuid();
        var bankAccountId = "test-bank-account";
        var paymentAmount = 1000m;
        var payableIds = new List<string> { "invoice-1", "invoice-2" };

        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                LoanId = loanId,
                PayableIds = payableIds
            },
            BankAccount = new BankAccountModel
            {
                Id = bankAccountId
            },
            PaymentAmount = paymentAmount
        };

        var company = new CompanyModel
        {
            LegalName = "Test Company Legal Name",
            Name = "Test Company"
        };

        _companyClientMock.Setup(x => x.GetCompanyByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(company);

        ServiceBusMessageBt<IhcRepaymentRequestMessage> capturedMessage = null;
        _ihcRepaymentMessageSenderMock
            .Setup(x => x.SendMessage(It.IsAny<ServiceBusMessageBt<IhcRepaymentRequestMessage>>(), It.IsAny<CancellationToken>()))
            .Callback<ServiceBusMessageBt<IhcRepaymentRequestMessage>, CancellationToken>((m, ct) => capturedMessage = m)
            .Returns(Task.CompletedTask);

        // Act
        await _service.CreateIhcRepaymentAch(autoPay, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedMessage);
        var message = capturedMessage.MessageBody;
        Assert.Equal(DomainConstants.IhcRepayment, message.FlowTemplateCode);
        Assert.Equal("LMS autopay", message.CreatedBy);
        Assert.NotNull(message.BlueTapeCorrelationId);

        var details = message.PaymentRequestDetails;
        Assert.Equal("USD", details.Currency);
        Assert.Equal(paymentAmount, details.RequestedAmount);
        Assert.Equal("ach", details.PaymentMethod);
        Assert.Equal(loanId, details.DrawDetails.Id);
        Assert.Equal(companyId, details.CustomerDetails.Id);
        Assert.Equal(company.LegalName, details.CustomerDetails.Name);
        Assert.Equal(bankAccountId, details.CustomerDetails.AccountId);
        Assert.Equal(payableIds.Count, details.PayablesDetails.Count);
        Assert.All(details.PayablesDetails, pd => Assert.Contains(pd.Id, payableIds));
    }

    [Fact]
    public async Task CreateDrawRepaymentCard_WhenSuccessful_ProcessesPayment()
    {
        // Arrange
        var loanId = Guid.NewGuid();
        var bankAccountId = "test-bank-account";
        var paymentAmount = 1000m;

        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                LoanId = loanId
            },
            BankAccount = new BankAccountModel
            {
                Id = bankAccountId
            },
            PaymentAmount = paymentAmount
        };

        _linqpalHttpClientMock.Setup(x => x.ProcessCardPayment(
                It.IsAny<CardPaymentRequestDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CardPaymentResponseDto { Success = true });

        // Act
        await _service.CreateDrawRepaymentCard(autoPay, CancellationToken.None);

        // Assert
        _linqpalHttpClientMock.Verify(x => x.ProcessCardPayment(
            It.Is<CardPaymentRequestDto>(r =>
                r.DrawId == loanId.ToString() &&
                r.Method == "bankAccount" &&
                r.Amount == paymentAmount &&
                r.BankAccountId == bankAccountId &&
                r.AutoDebit.GetValueOrDefault()),
            It.IsAny<CancellationToken>()));
    }

    [Fact]
    public async Task CreateDrawRepaymentCard_WhenFailed_ThrowsException()
    {
        // Arrange
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                LoanId = Guid.NewGuid()
            },
            BankAccount = new BankAccountModel
            {
                Id = "test-bank-account"
            },
            PaymentAmount = 1000m
        };

        _linqpalHttpClientMock.Setup(x => x.ProcessCardPayment(
                It.IsAny<CardPaymentRequestDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CardPaymentResponseDto { Success = false, Result = "Payment failed" });

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _service.CreateDrawRepaymentCard(autoPay, CancellationToken.None));

        Assert.Equal("Payment failed", exception.Message);
    }

    [Fact]
    public async Task CreateIhcRepaymentCard_WhenSuccessful_ProcessesPayment()
    {
        // Arrange
        var companyId = "test-company";
        var bankAccountId = "test-bank-account";
        var paymentAmount = 1000m;
        var payableIds = new List<string> { "invoice-1", "invoice-2" };

        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = companyId,
                PayableIds = payableIds
            },
            BankAccount = new BankAccountModel
            {
                Id = bankAccountId
            },
            PaymentAmount = paymentAmount
        };

        _linqpalHttpClientMock.Setup(x => x.ProcessIhcCardPayment(
                It.IsAny<IhcCardPaymentRequestDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CardPaymentResponseDto { Success = true });

        // Act
        await _service.CreateIhcRepaymentCard(autoPay, CancellationToken.None);

        // Assert
        _linqpalHttpClientMock.Verify(x => x.ProcessIhcCardPayment(
            It.Is<IhcCardPaymentRequestDto>(r =>
                r.CompanyId == companyId &&
                r.PaymentMethod == "card" &&
                r.RequestedAmount == paymentAmount &&
                r.AccountId == bankAccountId &&
                r.AutoDebit.GetValueOrDefault() &&
                r.InvoiceIds.Count == payableIds.Count &&
                !r.InvoiceIds.Except(payableIds).Any() &&
                !payableIds.Except(r.InvoiceIds).Any()),
            It.IsAny<CancellationToken>()));
    }

    [Fact]
    public async Task CreateIhcRepaymentCard_WhenFailed_ThrowsException()
    {
        // Arrange
        var autoPay = new AutoPayModel
        {
            DueLoanItem = new DueLoanItem
            {
                CompanyId = "test-company",
                PayableIds = new List<string> { "invoice-1" }
            },
            BankAccount = new BankAccountModel
            {
                Id = "test-bank-account"
            },
            PaymentAmount = 1000m
        };

        _linqpalHttpClientMock.Setup(x => x.ProcessIhcCardPayment(
                It.IsAny<IhcCardPaymentRequestDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CardPaymentResponseDto { Success = false, Result = "IHC payment failed" });

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _service.CreateIhcRepaymentCard(autoPay, CancellationToken.None));

        Assert.Equal("IHC payment failed", exception.Message);
    }
}
