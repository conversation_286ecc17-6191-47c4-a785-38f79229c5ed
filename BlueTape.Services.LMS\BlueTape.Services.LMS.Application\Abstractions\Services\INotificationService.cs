﻿using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Services.LMS.Application.Abstractions.Services;
public interface INotificationService
{
    Task NotifyUsersInvoiceDueIn3Days(AutoPayModel autoPay, CancellationToken ct);

    Task NotifyUsersInstallmentDueIn3Days(AutoPayModel autoPay, CancellationToken ct);

    Task SendSmsNotification(UserDto user, string companyId, string message, CancellationToken ct);

    SystemNotificationDto GetNotification(
        string notificationName, string notificationDescription, List<string> referenceIds,
        string? notificationReceiverCompanyId);
}
