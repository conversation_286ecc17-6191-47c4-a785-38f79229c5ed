﻿using BlueTape.Services.LMS.Application.Models.AutoPay;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Services.LMS.Application.Abstractions.Services;
public interface IPaymentExternalService
{
    Task CreateDrawRepaymentAch(AutoPayModel request, CancellationToken ct);
    Task CreateIhcRepaymentAch(AutoPayModel request, CancellationToken ct);
    Task CreateDrawRepaymentCard(AutoPayModel request, CancellationToken ct);
    Task CreateIhcRepaymentCard(AutoPayModel request, CancellationToken ct);
}
