﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace BlueTape.Services.LMS.Application.Models.AutoPay;

public class DueLoanItem
{
    [JsonPropertyName("LoanId")]
    public Guid LoanId { get; set; }
    [JsonPropertyName("PayableIds")]
    public List<string> PayableIds { get; set; }
    [JsonPropertyName("CompanyId")]
    public string CompanyId { get; set; } = string.Empty;
    [JsonPropertyName("NextPaymentAmount")]
    public decimal NextPaymentAmount { get; set; }
    [JsonPropertyName("OverDueAmount")]
    public decimal OverDueAmount { get; set; }
    [JsonPropertyName("NextPaymentDate")]
    public DateOnly NextPaymentDate { get; set; }
    [JsonPropertyName("IsOverdue")]
    public bool IsOverdue { get; set; }
    [JsonPropertyName("DueStatus")]
    public string DueStatus { get; set; } = string.Empty;
}