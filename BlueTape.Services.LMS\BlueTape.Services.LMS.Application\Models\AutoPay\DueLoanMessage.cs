using BlueTape.Services.LMS.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace BlueTape.Services.LMS.Application.Models.AutoPay;

public class DueLoanMessage
{
    [JsonPropertyName("Event")]
    public string Event { get; set; } = string.Empty;
    [JsonPropertyName("BlueTapeCorrelationId")]
    public string BlueTapeCorrelationId { get; set; } = string.Empty;
    [JsonPropertyName("CreatedAt")]
    public DateTime CreatedAt { get; set; }
    [JsonPropertyName("CreatedBy")]
    public string CreatedBy { get; set; } = string.Empty;
    [JsonPropertyName("CompanyId")]
    public string CompanyId { get; set; } = string.Empty;
    [JsonPropertyName("DueSum")]
    public decimal DueSum { get; set; }
    [JsonPropertyName("DueCount")]
    public int DueCount { get; set; }
    [JsonPropertyName("ProductType")]
    public ProductType ProductType { get; set; }
    [JsonPropertyName("Loans")]
    public List<DueLoanItem> Loans { get; set; } = [];
}