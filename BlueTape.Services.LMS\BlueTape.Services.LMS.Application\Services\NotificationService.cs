﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.Notification.Sender.Abstractions;
using BlueTape.Notification.Sender.Enums;
using BlueTape.Notification.Sender.SystemNotifications;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.DataAccess.Company.Abstractions.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Services.LMS.Application.Services;
public class NotificationService(ICompanyService companyService,
    ILoanService loanService,
    IAzureNotificationSenderService azureNotificationSenderService,
    ITraceIdAccessor traceIdAccessor,
    ILogger<NotificationService> logger) : INotificationService
{
    private const string EmailSender = "<EMAIL>";
    private const string EmailSenderName = "BlueTape Inc";
    private const string InvoiceIsDueIn3DaysEmail = "d-9770e71a7e9c47c78ef6829fb16faec4";
    private const string InstallmentIsDueIn3DaysEmail = "d-2e7614aa26dd43299b0ca2779ef343a9";
    private const string InvoiceApprovedSinglePaymentDue = "d-acb33d2f4ca94f5c80097f728475ec39";
    private const string InvoiceApprovedInstallmentPlan = "d-4d10d0de54914d088632b5150b8b7454";

    public async Task NotifyUsersInvoiceDueIn3Days(AutoPayModel autoPay, CancellationToken ct)
    {
        try
        {
            var companyId = autoPay.DueLoanItem.CompanyId;
            var users = await companyService.GetCompanyNotificationReceivers(companyId, ct);
            var loan = await loanService.GetById(autoPay.DueLoanItem.LoanId, ct);
            var firstOwner = users.MaxBy(x => x.CreatedAt);

            if (firstOwner == null)
            {
                logger.LogWarning("No notification receivers found for company {CompanyId}", companyId);
                return;
            }

            if (firstOwner.Phone is not null)
            {
                var message = BuildPaymentReminderMessage(firstOwner);
                await SendSmsNotification(firstOwner, companyId, message, ct);
            }

            var dynamicEmailData = new
            {
                customerName = firstOwner.FirstName,
                invoiceNumber = loan.LoanPayables.FirstOrDefault()?.InvoiceNumber,
                dueDate = autoPay.DueLoanItem.NextPaymentDate,
                totalAmount = loan.Amount,
                processingFee = loan.Fee,
                totalCharged = loan.Amount + loan.Fee,
                paymentMethod = autoPay.BankAccount.AccountNumber?.Display
            };

            var downPaymentIsExpiredNotification = GetNotification(
                nameof(InvoiceIsDueIn3DaysEmail),
                $"{nameof(InvoiceIsDueIn3DaysEmail)} for company id: {companyId}",
                autoPay.DueLoanItem.PayableIds,
                companyId);

            downPaymentIsExpiredNotification.EmailDelivery = [new NotificationChannelDto<EmailPayloadDto>()
            {
                Payload = new EmailPayloadDto
                {
                    Html = string.Empty,
                    TemplateId = InvoiceIsDueIn3DaysEmail,
                    TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                    Subject = nameof(InvoiceIsDueIn3DaysEmail),
                    From = new EmailReceiverDataDto()
                    {
                        Email = EmailSender,
                        Name = EmailSenderName
                    },
                    Receivers = users.Select(x => new EmailReceiverDataDto()
                    {
                        Email = x.Email,
                        Name = string.Empty
                    }).ToList(),
                }
            }];

            await azureNotificationSenderService.Send(downPaymentIsExpiredNotification, ct);

            logger.LogInformation("Successfully sent notification for company {CompanyId}", companyId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending {1} notification for company {CompanyId}", nameof(InvoiceIsDueIn3DaysEmail), autoPay.DueLoanItem.CompanyId);
            throw;
        }
    }

    public async Task NotifyUsersInstallmentDueIn3Days(AutoPayModel autoPay, CancellationToken ct)
    {
        try
        {
            var companyId = autoPay.DueLoanItem.CompanyId;
            var users = await companyService.GetCompanyNotificationReceivers(companyId, ct);
            var loan = await loanService.GetById(autoPay.DueLoanItem.LoanId, ct);
            var firstOwner = users.MaxBy(x => x.CreatedAt);

            if (firstOwner == null)
            {
                logger.LogWarning("No notification receivers found for company {CompanyId}", companyId);
                return;
            }

            if (firstOwner.Phone is not null)
            {
                var message = BuildPaymentReminderMessage(firstOwner);
                await SendSmsNotification(firstOwner, companyId, message, ct);
            }

            var installments = loan.LoanReceivables.Where(x => x.Type == Domain.Enums.ReceivableType.Installment).ToList();
            var installment = installments.FirstOrDefault(x => x.ExpectedDate == autoPay.DueLoanItem.NextPaymentDate);
            var installmentIndex = installments.IndexOf(installment) + 1;

            var dynamicEmailData = new
            {
                customerName = firstOwner.FirstName,
                invoiceNumber = loan.LoanPayables.FirstOrDefault()?.InvoiceNumber,
                installmentNumber = $"{installmentIndex} of {installments.Count}",
                installmentAmount = installment?.ExpectedAmount,
                dueDate = autoPay.DueLoanItem.NextPaymentDate,
                processingFee = loan.Fee,
                totalAmount = loan.Amount,
                paymentMethod = autoPay.BankAccount.AccountNumber?.Display
            };

            var downPaymentIsExpiredNotification = GetNotification(
                nameof(InstallmentIsDueIn3DaysEmail),
                $"{nameof(InstallmentIsDueIn3DaysEmail)} for company id: {companyId}",
                autoPay.DueLoanItem.PayableIds,
                companyId);

            downPaymentIsExpiredNotification.EmailDelivery = [new NotificationChannelDto<EmailPayloadDto>()
            {
                Payload = new EmailPayloadDto
                {
                    Html = string.Empty,
                    TemplateId = InstallmentIsDueIn3DaysEmail,
                    TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                    Subject = nameof(InstallmentIsDueIn3DaysEmail),
                    From = new EmailReceiverDataDto()
                    {
                        Email = EmailSender,
                        Name = EmailSenderName
                    },
                    Receivers = users.Select(x => new EmailReceiverDataDto()
                    {
                        Email = x.Email,
                        Name = string.Empty
                    }).ToList(),
                }
            }];

            await azureNotificationSenderService.Send(downPaymentIsExpiredNotification, ct);

            logger.LogInformation("Successfully sent notification for company {CompanyId}", companyId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending {1} notification for company {CompanyId}", nameof(InstallmentIsDueIn3DaysEmail), autoPay.DueLoanItem.CompanyId);
            throw;
        }
    }

    public async Task NotifyInvoiceApprovedSinglePaymentDue(AutoPayModel autoPay, CancellationToken ct)
    {
        try
        {
            var companyId = autoPay.DueLoanItem.CompanyId;
            var users = await companyService.GetCompanyNotificationReceivers(companyId, ct);
            var firstOwner = users.MaxBy(x => x.CreatedAt);

            if (firstOwner == null)
            {
                logger.LogWarning("No notification receivers found for company {CompanyId}", companyId);
                return;
            }

            var dynamicEmailData = new
            {
                customerName = "John",
                supplierName = "Contoso Inc.",
                invoiceNumber = "INV-12345",
                invoiceAmount = "$1,200.00",
                processingFee = "$36.00",
                totalAmount = "$1,236.00",
                dueDate = "06/25/2025",
                paymentMethod = "Auto Pay: Yes"
            };

            var downPaymentIsExpiredNotification = GetNotification(
                nameof(InvoiceApprovedSinglePaymentDue),
                $"{nameof(InvoiceApprovedSinglePaymentDue)} for company id: {companyId}",
                autoPay.DueLoanItem.PayableIds,
                companyId);

            downPaymentIsExpiredNotification.EmailDelivery = [new NotificationChannelDto<EmailPayloadDto>()
            {
                Payload = new EmailPayloadDto
                {
                    Html = string.Empty,
                    TemplateId = InvoiceApprovedSinglePaymentDue,
                    TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                    Subject = nameof(InvoiceApprovedSinglePaymentDue),
                    From = new EmailReceiverDataDto()
                    {
                        Email = EmailSender,
                        Name = EmailSenderName
                    },
                    Receivers = users.Select(x => new EmailReceiverDataDto()
                    {
                        Email = x.Email,
                        Name = string.Empty
                    }).ToList(),
                }
            }];

            await azureNotificationSenderService.Send(downPaymentIsExpiredNotification, ct);

            logger.LogInformation("Successfully sent notification for company {CompanyId}", companyId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending {1} notification for company {CompanyId}", nameof(InvoiceApprovedSinglePaymentDue), autoPay.DueLoanItem.CompanyId);
            throw;
        }
    }

    public async Task NotifyInvoiceApprovedInstallmentPlan(AutoPayModel autoPay, CancellationToken ct)
    {
        try
        {
            var companyId = autoPay.DueLoanItem.CompanyId;
            var users = await companyService.GetCompanyNotificationReceivers(companyId, ct);
            var firstOwner = users.MaxBy(x => x.CreatedAt);

            if (firstOwner == null)
            {
                logger.LogWarning("No notification receivers found for company {CompanyId}", companyId);
                return;
            }

            var dynamicEmailData = new
            {
                customerName = "John",
                supplierName = "Contoso Inc.",
                invoiceNumber = "INV-12345",
                invoiceAmount = "$1,200.00",
                processingFee = "$36.00",
                totalAmount = "$1,236.00",
                installmentsCount = 5,
                installmentAmount = "$247.20",
                firstPaymentDate = "06/25/2025",
                paymentMethod = "Auto Pay: Yes"
            };

            var downPaymentIsExpiredNotification = GetNotification(
                nameof(InvoiceApprovedInstallmentPlan),
                $"{nameof(InvoiceApprovedInstallmentPlan)} for company id: {companyId}",
                autoPay.DueLoanItem.PayableIds,
                companyId);

            downPaymentIsExpiredNotification.EmailDelivery = [new NotificationChannelDto<EmailPayloadDto>()
            {
                Payload = new EmailPayloadDto
                {
                    Html = string.Empty,
                    TemplateId = InvoiceApprovedInstallmentPlan,
                    TemplatePayload = JsonSerializer.Serialize(dynamicEmailData),
                    Subject = nameof(InvoiceApprovedInstallmentPlan),
                    From = new EmailReceiverDataDto()
                    {
                        Email = EmailSender,
                        Name = EmailSenderName
                    },
                    Receivers = users.Select(x => new EmailReceiverDataDto()
                    {
                        Email = x.Email,
                        Name = string.Empty
                    }).ToList(),
                }
            }];

            await azureNotificationSenderService.Send(downPaymentIsExpiredNotification, ct);

            logger.LogInformation("Successfully sent notification for company {CompanyId}", companyId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending {1} notification for company {CompanyId}", nameof(InvoiceApprovedInstallmentPlan), autoPay.DueLoanItem.CompanyId);
            throw;
        }
    }

    private static string BuildPaymentReminderMessage(UserDto firstOwner)
    {
        return $"BlueTape: Hello {firstOwner.FirstName}, this is a friendly reminder that a payment to your loan is due in 3 days.";
    }

    public async Task SendSmsNotification(UserDto user, string companyId, string message, CancellationToken ct)
    {
        try
        {
            var smsNotification = GetNotification(
                "SmsNotifications",
                $"some sms notification for company id: {companyId}",
                ["someInvoiceId"],
                companyId);

            smsNotification.SmsDelivery = [new NotificationChannelDto<SmsPayloadDto>()
            {
                Payload = new SmsPayloadDto
                {
                    Message = message,
                    Receivers = [new SmsReceiverDataDto() {Name = user.Name!, PhoneNumber = user.Phone!}],
                }
            }];

            await azureNotificationSenderService.Send(smsNotification, ct);

            logger.LogInformation("Successfully sent notification for company {CompanyId}", companyId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending down payment notification for company {CompanyId}", companyId);
            throw;
        }
    }

    public SystemNotificationDto GetNotification(
        string notificationName, string notificationDescription, List<string> referenceIds, string? notificationReceiverCompanyId)
    {
        var systemNotification = new SystemNotificationDto
        {
            Source = NotificationSource.LMS,
            TraceId = traceIdAccessor.TraceId,
            CompanyId = notificationReceiverCompanyId,
            NotificationName = notificationName,
            Description = notificationDescription,
            ReferenceIds = referenceIds,
            EmailDelivery = [],
            SmsDelivery = [],
            UserUiReviewDelivery = [],
            BlueTapeBackOfficeDelivery = []
        };

        return systemNotification;
    }
}
