﻿using BlueTape.CompanyClient.Abstractions;
using BlueTape.LinqpalClient.Abstractions;
using BlueTape.LinqpalClient.DTOs;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Messages;
using BlueTape.PaymentService.Messages.DrawRepayment;
using BlueTape.PaymentService.Messages.IhcRepayment;
using BlueTape.PaymentService.Senders;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Services.LMS.Application.Services;
public class PaymentExternalService(
    IDrawRepaymentMessageSender drawRepaymentMessageSender,
    IIhcRepaymentMessageSender ihcRepaymentMessageSender,
    ICompanyHttpClient companyClient,
    ILinqpalHttpClient linqpalHttpClient,
    ILogger<PaymentExternalService> logger) : IPaymentExternalService
{
    public async Task CreateDrawRepaymentAch(AutoPayModel request, CancellationToken ct)
    {
        var company = await companyClient.GetCompanyByIdAsync(request.DueLoanItem.CompanyId, ct);
        var message = new DrawRepaymentRequestMessage()
        {
            FlowTemplateCode = DomainConstants.DrawRepayment,
            CreatedBy = "LMS autopay",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new DrawRepaymentRequestDetails()
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = request.PaymentAmount,
                PaymentMethod = "ach",
                DrawRepaymentDrawDetails = new DrawRepaymentDrawDetails()
                {
                    Id = request.DueLoanItem.LoanId,
                    Amount = request.PaymentAmount,
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = request.DueLoanItem.CompanyId,
                    Name = !string.IsNullOrEmpty(company?.LegalName) ? company.LegalName : company?.Name!,
                    AccountId = request.BankAccount.Id,
                },
                CompatibilityDetails = new CompatibilityDetails
                {
                    LmsPaymentId = Guid.NewGuid().ToString(),
                },
            }
        };

        await drawRepaymentMessageSender.SendMessage(new ServiceBusMessageBt<DrawRepaymentRequestMessage>(message), ct);
    }

    public async Task CreateIhcRepaymentAch(AutoPayModel request, CancellationToken ct)
    {
        var company = await companyClient.GetCompanyByIdAsync(request.DueLoanItem.CompanyId, ct);
        var payablesDetails = request.DueLoanItem.PayableIds.Select(x => new PayablesDetail()
        {
            Id = x,
        }).ToList();

        var message = new IhcRepaymentRequestMessage()
        {
            FlowTemplateCode = DomainConstants.IhcRepayment,
            CreatedBy = "LMS autopay",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new()
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = request.PaymentAmount,
                PaymentMethod = "ach",
                DrawDetails = new()
                {
                    Id = request.DueLoanItem.LoanId
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = request.DueLoanItem.CompanyId,
                    Name = !string.IsNullOrEmpty(company?.LegalName) ? company.LegalName : company?.Name!,
                    AccountId = request.BankAccount.Id
                },
                PayablesDetails = payablesDetails
            }
        };

        await ihcRepaymentMessageSender.SendMessage(new ServiceBusMessageBt<IhcRepaymentRequestMessage>(message), ct);
    }

    public async Task CreateDrawRepaymentCard(AutoPayModel request, CancellationToken ct)
    {
        var message = new CardPaymentRequestDto
        {
            DrawId = request.DueLoanItem.LoanId.ToString(),
            Method = "bankAccount",
            Amount = request.PaymentAmount,
            BankAccountId = request.BankAccount.Id,
            AutoDebit = true,
        };

        var response = await linqpalHttpClient.ProcessCardPayment(message, ct);
        if (response is null)
            throw new InvalidOperationException("Failed to process card payment");
        if (!response.Success)
            throw new InvalidOperationException(response.Result);
    }

    public async Task CreateIhcRepaymentCard(AutoPayModel request, CancellationToken ct)
    {
        var message = new IhcCardPaymentRequestDto
        {
            InvoiceIds = request.DueLoanItem.PayableIds,
            PaymentMethod = "card",
            AccountId = request.BankAccount.Id,
            CompanyId = request.DueLoanItem.CompanyId,
            RequestedAmount = request.PaymentAmount,
            AutoDebit = true,
        };

        var response = await linqpalHttpClient.ProcessIhcCardPayment(message, ct);
        if (response is null)
            throw new InvalidOperationException("Failed to process card IHC payment");
        if (!response.Success)
            throw new InvalidOperationException(response.Result);
    }
}
