﻿using BlueTape.Services.LMS.Application.Models.AutoPay;
using FluentValidation;

namespace BlueTape.Services.LMS.Application.Validators;

public class DueLoanItemValidator: AbstractValidator<DueLoanItem>
{
    public DueLoanItemValidator()
    {
        RuleFor(x => x.NextPaymentAmount).GreaterThan(0);
        RuleFor(x => x.OverDueAmount).GreaterThanOrEqualTo(0);
        RuleFor(x => x.CompanyId).NotEmpty();
        RuleFor(x => x.LoanId).NotEmpty();
        RuleFor(x => x.CompanyId).NotEmpty();
    }
}