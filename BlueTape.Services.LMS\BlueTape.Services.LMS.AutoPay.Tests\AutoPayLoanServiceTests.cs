﻿using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Models.LoanReceivables;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Application.Models.Payments;
using BlueTape.Services.LMS.Domain.Enums;
using Moq;
using Shouldly;
using BlueTape.Services.LMS.AutoPay.Services;
using Xunit;

namespace BlueTape.Services.LMS.Application.Tests.Services
{
    public class AutoPayLoanServiceTests
    {
        private readonly AutoPayLoanService _autoPayLoanService;
        private readonly Mock<ILoanService> _loanExternalServiceMock = new();

        public AutoPayLoanServiceTests()
        {
            _autoPayLoanService = new AutoPayLoanService(_loanExternalServiceMock.Object);
        }

        [Fact]
        public async Task GetUpcoming_ValidData_ReturnsAutoPayLoansList()
        {
            var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            const int loanAmount = 10000;
            const string loanCompanyId = "test_company";
            var loanId = Guid.NewGuid();

            var loan = new Loan
            {
                Id = loanId,
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId,
                IsDeleted = false,
                IsOverdue = false,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var receivableId = Guid.NewGuid();
            const int receivablePaidAmount = 0;
            const int receivableExpectedAmount = 2000;
            const int receivableAdjustAmount = -200;
            var receivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(2));

            var receivable = new LoanReceivable()
            {
                Id = receivableId,
                Status = LoanReceivableStatus.Pending,
                Loan = loan,
                PaidAmount = receivablePaidAmount,
                ExpectedAmount = receivableExpectedAmount,
                AdjustAmount = receivableAdjustAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
            };

            var secondReceivableId = Guid.NewGuid();
            const int secondReceivablePaidAmount = 200;
            const int secondReceivableExpectedAmount = 2000;
            var secondReceivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(9));

            var secondReceivable = new LoanReceivable()
            {
                Id = secondReceivableId,
                Status = LoanReceivableStatus.Pending,
                Loan = loan,
                PaidAmount = secondReceivablePaidAmount,
                ExpectedAmount = secondReceivableExpectedAmount,
                PaidDate = null,
                ExpectedDate = secondReceivableExpectedDate,
                LoanId = loan.Id,
            };
            loan.LoanReceivables.Add(receivable);
            loan.LoanReceivables.Add(secondReceivable);

            IEnumerable<Loan> loans = new List<Loan>() { loan };

            _loanExternalServiceMock.Setup(x => x.GetUpcomingAndOverdueLoans(It.IsAny<DateOnly>(), null, default, null, null)).ReturnsAsync(loans);

            var result = (await _autoPayLoanService.GetUpcoming(date, 3, default)).ToList();

            result.First().Id.ShouldBe(loanId);
            result.First().IsOverdue.ShouldBe(false);
            result.First().NextPaymentAmount.ShouldBe(receivableExpectedAmount + receivableAdjustAmount);
            result.First().NextPaymentDate.ShouldBe(receivableExpectedDate);
            result.First().OverDueAmount.ShouldBe(0);
        }

        [Fact]
        public async Task GetUpcoming_LateReceivable_ReturnsAutoPayLoansList()
        {
            DateOnly date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            const int loanAmount = 10000;
            const string loanCompanyId = "test_company";
            var loanId = Guid.NewGuid();

            var loan = new Loan
            {
                Id = loanId,
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId,
                IsDeleted = false,
                IsOverdue = true,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var receivableId = Guid.NewGuid();
            const int receivablePaidAmount = 1000;
            const int receivableExpectedAmount = 2000;
            var receivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-3));

            var receivable = new LoanReceivable()
            {
                Id = receivableId,
                Status = LoanReceivableStatus.Late,
                Loan = loan,
                PaidAmount = receivablePaidAmount,
                ExpectedAmount = receivableExpectedAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
            };

            var secondReceivableId = Guid.NewGuid();
            const int secondReceivablePaidAmount = 0;
            const int secondReceivableExpectedAmount = 2000;
            var secondReceivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(1));

            var secondReceivable = new LoanReceivable()
            {
                Id = secondReceivableId,
                Status = LoanReceivableStatus.Pending,
                Loan = loan,
                PaidAmount = secondReceivablePaidAmount,
                ExpectedAmount = secondReceivableExpectedAmount,
                PaidDate = null,
                ExpectedDate = secondReceivableExpectedDate,
                LoanId = loan.Id,
            };
            loan.LoanReceivables.Add(receivable);
            loan.LoanReceivables.Add(secondReceivable);

            IEnumerable<Loan> loans = new List<Loan>() { loan };

            _loanExternalServiceMock.Setup(x => x.GetUpcomingAndOverdueLoans(It.IsAny<DateOnly>(), null, default, null, null)).ReturnsAsync(loans);

            var result = (await _autoPayLoanService.GetUpcoming(date, 3, default))!.ToList();

            result.First().Id.ShouldBe(loanId);
            result.First().IsOverdue.ShouldBe(true);
            result.First().NextPaymentAmount.ShouldBe(receivableExpectedAmount + secondReceivableExpectedAmount - receivablePaidAmount - secondReceivablePaidAmount);
            result.First().NextPaymentDate.ShouldBe(secondReceivableExpectedDate);
            result.First().OverDueAmount.ShouldBe(receivableExpectedAmount - receivablePaidAmount);
        }

        [Fact]
        public async Task GetUpcoming_NullLoans_ReturnsAutoPayLoansList()
        {
            var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            IEnumerable<Loan> loans = null;

            _loanExternalServiceMock.Setup(x => x.GetUpcomingAndOverdueLoans(It.IsAny<DateOnly>(), false, default, null, null)).ReturnsAsync(loans);

            var result = (await _autoPayLoanService.GetUpcoming(date, 3, default))!.ToList();

            result.Count.ShouldBe(0);
        }

        [Fact]
        public async Task GetUpcoming_AllReceivablesAreLate_ReturnsAutoPayLoansList()
        {
            var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            const int loanAmount = 10000;
            const string loanCompanyId = "test_company";
            var loanId = Guid.NewGuid();

            var loan = new Loan
            {
                Id = loanId,
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId,
                IsDeleted = false,
                IsOverdue = true,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var receivableId = Guid.NewGuid();
            const int receivablePaidAmount = 1000;
            const int receivableExpectedAmount = 2000;
            const int receivableAdjustAmount = -350;
            var receivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-10));

            var receivable = new LoanReceivable()
            {
                Id = receivableId,
                Status = LoanReceivableStatus.Late,
                Loan = loan,
                PaidAmount = receivablePaidAmount,
                ExpectedAmount = receivableExpectedAmount,
                AdjustAmount = receivableAdjustAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
            };

            var secondReceivableId = Guid.NewGuid();
            const int secondReceivablePaidAmount = 0;
            const int secondReceivableExpectedAmount = 2000;
            const int secondReceivableAdjustAmount = -350;
            var secondReceivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-5));

            var secondReceivable = new LoanReceivable()
            {
                Id = secondReceivableId,
                Status = LoanReceivableStatus.Pending,
                Loan = loan,
                PaidAmount = secondReceivablePaidAmount,
                ExpectedAmount = secondReceivableExpectedAmount,
                AdjustAmount = secondReceivableAdjustAmount,
                PaidDate = null,
                ExpectedDate = secondReceivableExpectedDate,
                LoanId = loan.Id,
            };
            loan.LoanReceivables.Add(receivable);
            loan.LoanReceivables.Add(secondReceivable);

            IEnumerable<Loan> loans = new List<Loan>() { loan };
            const int nextPaymentExpectedAmount = receivableExpectedAmount - receivablePaidAmount + receivableAdjustAmount
                + secondReceivableExpectedAmount - secondReceivablePaidAmount + secondReceivableAdjustAmount;

            _loanExternalServiceMock
                .Setup(x => x.GetUpcomingAndOverdueLoans(It.IsAny<DateOnly>(), null, default, null, null))
                .ReturnsAsync(loans);

            var result = (await _autoPayLoanService.GetUpcoming(date, 3, default))?.ToList();

            result.ShouldNotBeNull();
            result.Count.ShouldBeGreaterThan(0);

            var firstLoan = result.First();
            firstLoan.Id.ShouldBe(loanId);
            firstLoan.IsOverdue.ShouldBe(true);
            firstLoan.NextPaymentAmount.ShouldBe(nextPaymentExpectedAmount);
            firstLoan.NextPaymentDate.ShouldBe(secondReceivableExpectedDate);
            firstLoan.OverDueAmount.ShouldBe(nextPaymentExpectedAmount);
        }

        [Fact]
        public async Task GetUpcoming_HasProcessingPayment_ShouldConsiderProcessingPaymentAmount()
        {
            var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            const int loanAmount = 10000;
            const string loanCompanyId = "test_company";
            var loanId = Guid.NewGuid();

            var loan = new Loan
            {
                Id = loanId,
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId,
                IsDeleted = false,
                IsOverdue = true,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var receivableId = Guid.NewGuid();
            const int receivablePaidAmount = 1000;
            const int receivableExpectedAmount = 2000;
            var receivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-10));

            var receivable = new LoanReceivable()
            {
                Id = receivableId,
                Status = LoanReceivableStatus.Late,
                Loan = loan,
                PaidAmount = receivablePaidAmount,
                ExpectedAmount = receivableExpectedAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
            };

            loan.LoanReceivables.Add(receivable);

            var paymentAmount = 500;

            var payment = new Payment()
            {
                LoanId = loan.Id,
                Status = PaymentStatus.Processing,
                Amount = paymentAmount
            };

            loan.Payments = new List<Payment>() { payment };

            IEnumerable<Loan> loans = new List<Loan>() { loan };

            _loanExternalServiceMock.Setup(x => x.GetUpcomingAndOverdueLoans(It.IsAny<DateOnly>(), null, default, null, null)).ReturnsAsync(loans);

            var result = (await _autoPayLoanService.GetUpcoming(date, 3, default))!.ToList();

            result.First().Id.ShouldBe(loanId);
            result.First().IsOverdue.ShouldBe(true);
            result.First().NextPaymentAmount.ShouldBe(receivableExpectedAmount - receivablePaidAmount - paymentAmount);
            result.First().NextPaymentDate.ShouldBe(receivableExpectedDate);
            result.First().OverDueAmount.ShouldBe(receivableExpectedAmount - receivablePaidAmount - paymentAmount);
        }

        [Fact]
        public async Task GetUpcoming_HasProcessingAndRejectedPayments_ShouldConsiderOnlyProcessingPaymentAmount()
        {
            var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            const int loanAmount = 10000;
            const string loanCompanyId = "test_company";
            var loanId = Guid.NewGuid();

            var loan = new Loan
            {
                Id = loanId,
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId,
                IsDeleted = false,
                IsOverdue = true,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var receivableId = Guid.NewGuid();
            const int receivablePaidAmount = 1000;
            const int receivableExpectedAmount = 2000;
            var receivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-10));

            var receivable = new LoanReceivable()
            {
                Id = receivableId,
                Status = LoanReceivableStatus.Late,
                Loan = loan,
                PaidAmount = receivablePaidAmount,
                ExpectedAmount = receivableExpectedAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
            };

            loan.LoanReceivables.Add(receivable);

            var paymentAmount = 500;

            var processingPayment = new Payment()
            {
                LoanId = loan.Id,
                Status = PaymentStatus.Processing,
                Amount = paymentAmount
            };

            var rejectedPayment = new Payment()
            {
                LoanId = loan.Id,
                Status = PaymentStatus.Rejected,
                Amount = paymentAmount
            };

            var refundPayment = new Payment()
            {
                LoanId = loan.Id,
                Status = PaymentStatus.Processing,
                Amount = paymentAmount * -1,
                SubType = PaymentSubType.Refund
            };

            loan.Payments = [processingPayment, rejectedPayment, refundPayment];

            IEnumerable<Loan> loans = new List<Loan>() { loan };

            _loanExternalServiceMock.Setup(x => x.GetUpcomingAndOverdueLoans(It.IsAny<DateOnly>(), null, default, null, null)).ReturnsAsync(loans);

            var result = (await _autoPayLoanService.GetUpcoming(date, 3, default))!.ToList();

            result.First().Id.ShouldBe(loanId);
            result.First().IsOverdue.ShouldBe(true);
            result.First().NextPaymentAmount.ShouldBe(receivableExpectedAmount - receivablePaidAmount - paymentAmount);
            result.First().NextPaymentDate.ShouldBe(receivableExpectedDate);
            result.First().OverDueAmount.ShouldBe(receivableExpectedAmount - receivablePaidAmount - paymentAmount);
        }

        [Fact]
        public async Task GetUpcoming_NextPaymentAmountEqualsZero_ShouldIgnoreThisAutoPayLoan()
        {
            var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            const int loanAmount = 10000;
            const string loanCompanyId = "test_company";
            var loanId = Guid.NewGuid();

            var loan = new Loan
            {
                Id = loanId,
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId,
                IsDeleted = false,
                IsOverdue = true,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var receivableId = Guid.NewGuid();
            const int receivablePaidAmount = 10;
            const int receivableExpectedAmount = 500;
            var receivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-10));

            var receivable = new LoanReceivable()
            {
                Id = receivableId,
                Status = LoanReceivableStatus.Late,
                Loan = loan,
                PaidAmount = receivablePaidAmount,
                ExpectedAmount = receivableExpectedAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
            };

            loan.LoanReceivables.Add(receivable);

            var paymentAmount = 500;

            var processingPayment = new Payment()
            {
                LoanId = loan.Id,
                Status = PaymentStatus.Processing,
                Amount = paymentAmount
            };

            loan.Payments = new List<Payment>() { processingPayment };

            IEnumerable<Loan> loans = new List<Loan>() { loan };

            _loanExternalServiceMock.Setup(x => x.GetUpcomingAndOverdueLoans(It.IsAny<DateOnly>(), null, default, null, null)).ReturnsAsync(loans);

            var result = (await _autoPayLoanService.GetUpcoming(date, 3, default))!.ToList();

            result.Count.ShouldBe(0);
        }

        [Fact]
        public async Task GetUpcoming_NextPaymentDetailsHasValidData_ShouldReturnValidData()
        {
            var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            const int loanAmount = 1000;
            const string loanCompanyId = "test_company";
            var loanId = Guid.NewGuid();

            var loan = new Loan
            {
                Id = loanId,
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId,
                IsDeleted = false,
                IsOverdue = false,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var loanFeeId = Guid.NewGuid();
            const int loanFeePaidAmount = 0;
            const int loanFeeExpectedAmount = 200;
            const int loanFeeAdjustAmount = 0;
            var receivableExpectedDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(2));

            var loanFee = new LoanReceivable()
            {
                Id = loanFeeId,
                Status = LoanReceivableStatus.Pending,
                Loan = loan,
                PaidAmount = loanFeePaidAmount,
                ExpectedAmount = loanFeeExpectedAmount,
                AdjustAmount = loanFeeAdjustAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
                Type = ReceivableType.LoanFee
            };

            var firstExtensionFeeId = Guid.NewGuid();
            const int firstExtensionFeePaidAmount = 0;
            const int firstExtensionFeeExpectedAmount = 100;
            const int firstExtensionFeeAdjustAmount = 0;
            var firstExtensionFee = new LoanReceivable()
            {
                Id = firstExtensionFeeId,
                Status = LoanReceivableStatus.Pending,
                Loan = loan,
                PaidAmount = firstExtensionFeePaidAmount,
                ExpectedAmount = firstExtensionFeeExpectedAmount,
                AdjustAmount = firstExtensionFeeAdjustAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
                Type = ReceivableType.ExtensionFee
            };

            var secondExtensionFeeId = Guid.NewGuid();
            const int secondExtensionFeePaidAmount = 0;
            const int secondExtensionFeeExpectedAmount = 100;
            const int secondExtensionFeeAdjustAmount = 0;
            var secondExtensionFee = new LoanReceivable()
            {
                Id = secondExtensionFeeId,
                Status = LoanReceivableStatus.Pending,
                Loan = loan,
                PaidAmount = secondExtensionFeePaidAmount,
                ExpectedAmount = secondExtensionFeeExpectedAmount,
                AdjustAmount = secondExtensionFeeAdjustAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
                Type = ReceivableType.ExtensionFee
            };

            var installmentId = Guid.NewGuid();
            const int installmentPaidAmount = 0;
            const int installmentExpectedAmount = 1000;
            const int installmentAdjustAmount = 0;
            var installment = new LoanReceivable()
            {
                Id = installmentId,
                Status = LoanReceivableStatus.Pending,
                Loan = loan,
                PaidAmount = installmentPaidAmount,
                ExpectedAmount = installmentExpectedAmount,
                AdjustAmount = installmentAdjustAmount,
                PaidDate = null,
                ExpectedDate = receivableExpectedDate,
                LoanId = loan.Id,
                Type = ReceivableType.Installment
            };

            loan.LoanReceivables.Add(loanFee);
            loan.LoanReceivables.Add(firstExtensionFee);
            loan.LoanReceivables.Add(secondExtensionFee);
            loan.LoanReceivables.Add(installment);

            var expectedNextPaymentAmount = loanFeeExpectedAmount + firstExtensionFeeExpectedAmount
                                                                  + secondExtensionFeeExpectedAmount + installmentExpectedAmount;

            IEnumerable<Loan> loans = new List<Loan>() { loan };

            _loanExternalServiceMock.Setup(x => x.GetUpcomingAndOverdueLoans(It.IsAny<DateOnly>(), null, default, null, null)).ReturnsAsync(loans);

            var result = (await _autoPayLoanService.GetUpcoming(date, 3, default))!.ToList();

            result.First().Id.ShouldBe(loanId);
            result.First().IsOverdue.ShouldBe(false);
            result.First().NextPaymentAmount.ShouldBe(expectedNextPaymentAmount);
            result.First().NextPaymentDate.ShouldBe(receivableExpectedDate);
            result.First().OverDueAmount.ShouldBe(0);
            result.First().NextPaymentDetails?.Count.ShouldBe(3);
            result.First().NextPaymentDetails?.First(x => x.ReceivableType == ReceivableType.LoanFee)
                .Count.ShouldBe(1);
            result.First().NextPaymentDetails?.First(x => x.ReceivableType == ReceivableType.Installment)
                .Count.ShouldBe(1);
            result.First().NextPaymentDetails?.First(x => x.ReceivableType == ReceivableType.ExtensionFee)
                .Count.ShouldBe(2);
            result.First().NextPaymentDetails?.First(x => x.ReceivableType == ReceivableType.ExtensionFee)
                .Amount.ShouldBe(firstExtensionFeeExpectedAmount + secondExtensionFeeExpectedAmount);
            result.First().DueStatus.ShouldBe(LoanDueStatus.Pending);
        }

        [Fact]
        public async Task GetUpcoming_LoansWithDueAndPastDueReceivables_ShouldReturnDifferentDueStatuses()
        {
            var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            const int loanAmount = 10000;
            const string loanCompanyId1 = "test_company_1";
            const string loanCompanyId2 = "test_company_2";
            const string loanCompanyId3 = "test_company_3";

            var loanPending = new Loan
            {
                Id = Guid.NewGuid(),
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId3,
                IsDeleted = false,
                IsOverdue = false,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var loanDue = new Loan
            {
                Id = Guid.NewGuid(),
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId1,
                IsDeleted = false,
                IsOverdue = false,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var loanPastDuePaymentProcessing = new Loan
            {
                Id = Guid.NewGuid(),
                Status = LoanStatus.Pending,
                Amount = loanAmount,
                CompanyId = loanCompanyId2,
                IsDeleted = false,
                IsOverdue = false,
                LoanReceivables = new List<LoanReceivable>(),
                Credit = new()
            };

            var receivableExpectedDateDue = DateOnly.FromDateTime(DateTime.UtcNow);
            var receivableExpectedDatePastDuePaymentProcessing = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-3));
            var receivableExpectedDatePending = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(3));

            var receivablePending = new LoanReceivable()
            {
                Id = Guid.NewGuid(),
                Status = LoanReceivableStatus.Pending,
                Loan = loanPastDuePaymentProcessing,
                PaidAmount = 0,
                ExpectedAmount = 1000,
                PaidDate = null,
                ExpectedDate = receivableExpectedDatePending,
                LoanId = loanPending.Id,
            };

            var receivablePastDueProcessingAmount = new LoanReceivable()
            {
                Id = Guid.NewGuid(),
                Status = LoanReceivableStatus.Pending,
                Loan = loanPastDuePaymentProcessing,
                PaidAmount = 0,
                ExpectedAmount = 1000,
                PaidDate = null,
                ExpectedDate = receivableExpectedDatePastDuePaymentProcessing,
                LoanId = loanPastDuePaymentProcessing.Id,
            };

            var receivableDue = new LoanReceivable()
            {
                Id = Guid.NewGuid(),
                Status = LoanReceivableStatus.Pending,
                Loan = loanDue,
                PaidAmount = 10,
                ExpectedAmount = 500,
                PaidDate = null,
                ExpectedDate = receivableExpectedDateDue,
                LoanId = loanDue.Id,
            };

            loanDue.LoanReceivables.Add(receivableDue);
            loanPastDuePaymentProcessing.LoanReceivables.Add(receivablePastDueProcessingAmount);
            loanPending.LoanReceivables.Add(receivablePending);

            var paymentAmount = 500;

            var processingPayment = new Payment()
            {
                LoanId = loanPastDuePaymentProcessing.Id,
                Status = PaymentStatus.Processing,
                Amount = paymentAmount
            };

            loanPastDuePaymentProcessing.Payments = new List<Payment>() { processingPayment };

            IEnumerable<Loan> loans = new List<Loan>() { loanDue, loanPastDuePaymentProcessing, loanPending };

            _loanExternalServiceMock.Setup(x => x.GetUpcomingAndOverdueLoans(It.IsAny<DateOnly>(), null, default, null, null)).ReturnsAsync(loans);

            var result = (await _autoPayLoanService.GetUpcoming(date, 3, default))!.ToList();
            var dueStatuses = result.Select(x => x.DueStatus);

            result.Count.ShouldBe(3);
            dueStatuses.ShouldBe(new List<LoanDueStatus>
            {
                LoanDueStatus.Due, LoanDueStatus.PastDuePaymentProcessing, LoanDueStatus.Pending
            });
        }
    }
}
