using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.AutoPay.Abstractions.Senders;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System.Text.Json;
using Xunit;

namespace BlueTape.Services.LMS.AutoPay.Tests.GetDueStrategies;

public class GetDueBaseStrategyTests
{
    private readonly Mock<ILogger<GetDueBaseStrategy>> _loggerMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IAutoPayLoanService> _autoPayLoanServiceMock;
    private readonly Mock<IOptions<ProccessDueMessagingOptions>> _optionsMock;
    private readonly Mock<IProcessDueMessageSender> _processDueMessageSenderMock;
    private readonly Mock<IDueLoanMessagesGenerator> _dueLoanMessagesGeneratorMock;
    private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock;
    private readonly TestGetDueStrategy _strategy;

    public GetDueBaseStrategyTests()
    {
        _loggerMock = new Mock<ILogger<GetDueBaseStrategy>>();
        _dateProviderMock = new Mock<IDateProvider>();
        _autoPayLoanServiceMock = new Mock<IAutoPayLoanService>();
        _optionsMock = new Mock<IOptions<ProccessDueMessagingOptions>>();
        _processDueMessageSenderMock = new Mock<IProcessDueMessageSender>();
        _dueLoanMessagesGeneratorMock = new Mock<IDueLoanMessagesGenerator>();
        _traceIdAccessorMock = new Mock<ITraceIdAccessor>();

        _optionsMock.Setup(x => x.Value).Returns(new ProccessDueMessagingOptions
        {
            MaxMessagesInBatchCount = 10,
            MaxSimultaneouslySentMessagesCount = 5,
            ScheduledPeriodDurationBetweenMessagesInMinutes = 1
        });

        _strategy = new TestGetDueStrategy(
            _loggerMock.Object,
            _dateProviderMock.Object,
            _autoPayLoanServiceMock.Object,
            _optionsMock.Object,
            _processDueMessageSenderMock.Object,
            _dueLoanMessagesGeneratorMock.Object,
            _traceIdAccessorMock.Object,
            ProductType.LineOfCredit);
    }

    [Fact]
    public async Task GetDueLoans_WhenNoUpcomingLoans_ReturnsEmptyList()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);
        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.LineOfCredit, null))
            .ReturnsAsync(new List<AutoPayLoan>());

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Empty(result);
        _processDueMessageSenderMock.Verify(
            x => x.SendMessages(It.IsAny<List<ServiceBusMessageBt<DueLoanMessage>>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task GetDueLoans_WhenHasUpcomingLoans_ProcessesAndSendsMessages()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        var companyId = "test-company";
        var loanId = Guid.NewGuid();
        var traceId = "test-trace-id";

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);
        _traceIdAccessorMock.Setup(x => x.TraceId).Returns(traceId);

        var upcomingLoan = new AutoPayLoan
        {
            Id = loanId,
            CompanyId = companyId,
            CreditId = Guid.NewGuid(),
            CreditStatus = CreditStatus.Active,
            IsAutoCollectionPaused = false
        };

        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.LineOfCredit, null))
            .ReturnsAsync(new List<AutoPayLoan> { upcomingLoan });

        var expectedMessage = new DueLoanMessage
        {
            CompanyId = companyId,
            ProductType = ProductType.LineOfCredit,
            Loans = new List<DueLoanItem> { new() { LoanId = loanId } }
        };

        _dueLoanMessagesGeneratorMock
            .Setup(x => x.GenerateDueLoansMessage(It.IsAny<List<AutoPayLoan>>(), ProductType.LineOfCredit, companyId))
            .Returns(expectedMessage);

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Single(result);
        Assert.Equal(companyId, result[0].CompanyId);
        Assert.Equal(ProductType.LineOfCredit, result[0].ProductType);

        _processDueMessageSenderMock.Verify(
            x => x.SendMessages(It.IsAny<List<ServiceBusMessageBt<DueLoanMessage>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetDueLoans_WhenNoEligibleLoans_ReturnsEmptyList()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        var companyId = "test-company";
        var loanId = Guid.NewGuid();

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        var upcomingLoan = new AutoPayLoan
        {
            Id = loanId,
            CompanyId = companyId,
            CreditId = Guid.NewGuid(),
            CreditStatus = CreditStatus.Closed, // Not eligible
            IsAutoCollectionPaused = false
        };

        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.LineOfCredit, null))
            .ReturnsAsync(new List<AutoPayLoan> { upcomingLoan });

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Empty(result);
        _processDueMessageSenderMock.Verify(
            x => x.SendMessages(It.IsAny<List<ServiceBusMessageBt<DueLoanMessage>>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    private class TestGetDueStrategy : GetDueBaseStrategy
    {
        public TestGetDueStrategy(
            ILogger<GetDueBaseStrategy> logger,
            IDateProvider dateProvider,
            IAutoPayLoanService autoPayLoanService,
            IOptions<ProccessDueMessagingOptions> options,
            IProcessDueMessageSender processDueMessageSender,
            IDueLoanMessagesGenerator dueLoanMessagesGenerator,
            ITraceIdAccessor traceIdAccessor,
            ProductType productType)
            : base(logger, dateProvider, autoPayLoanService, options, processDueMessageSender, dueLoanMessagesGenerator, traceIdAccessor, productType)
        {
        }

        public override bool IsApplicable(ProductType productType) => true;

        protected override Task<List<AutoPayLoan>> GetEligibleLoans(IGrouping<string, AutoPayLoan> loans, CancellationToken cancellationToken)
        {
            return Task.FromResult(FilterLoans(loans));
        }
    }
}