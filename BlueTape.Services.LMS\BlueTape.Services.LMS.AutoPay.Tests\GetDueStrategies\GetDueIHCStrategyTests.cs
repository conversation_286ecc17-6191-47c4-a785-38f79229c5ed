using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.AutoPay.Abstractions.Senders;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace BlueTape.Services.LMS.AutoPay.Tests.GetDueStrategies;

public class GetDueIHCStrategyTests
{
    private readonly Mock<ILogger<GetDueIHCStrategy>> _loggerMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IAutoPayLoanService> _autoPayLoanServiceMock;
    private readonly Mock<IOptions<ProccessDueMessagingOptions>> _optionsMock;
    private readonly Mock<IProcessDueMessageSender> _processDueMessageSenderMock;
    private readonly Mock<IDueLoanMessagesGenerator> _dueLoanMessagesGeneratorMock;
    private readonly Mock<ICompanyHttpClient> _companyHttpClientMock;
    private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock;
    private readonly GetDueIHCStrategy _strategy;

    public GetDueIHCStrategyTests()
    {
        _loggerMock = new Mock<ILogger<GetDueIHCStrategy>>();
        _dateProviderMock = new Mock<IDateProvider>();
        _autoPayLoanServiceMock = new Mock<IAutoPayLoanService>();
        _optionsMock = new Mock<IOptions<ProccessDueMessagingOptions>>();
        _processDueMessageSenderMock = new Mock<IProcessDueMessageSender>();
        _dueLoanMessagesGeneratorMock = new Mock<IDueLoanMessagesGenerator>();
        _companyHttpClientMock = new Mock<ICompanyHttpClient>();
        _traceIdAccessorMock = new Mock<ITraceIdAccessor>();

        _optionsMock.Setup(x => x.Value).Returns(new ProccessDueMessagingOptions
        {
            MaxMessagesInBatchCount = 10,
            MaxSimultaneouslySentMessagesCount = 5,
            ScheduledPeriodDurationBetweenMessagesInMinutes = 1
        });

        _strategy = new GetDueIHCStrategy(
            _loggerMock.Object,
            _dateProviderMock.Object,
            _autoPayLoanServiceMock.Object,
            _optionsMock.Object,
            _processDueMessageSenderMock.Object,
            _dueLoanMessagesGeneratorMock.Object,
            _companyHttpClientMock.Object,
            _traceIdAccessorMock.Object);
    }

    [Fact]
    public void IsApplicable_ReturnsTrue_ForInHouseCredit()
    {
        // Act
        var result = _strategy.IsApplicable(ProductType.InHouseCredit);

        // Assert
        Assert.True(result);
    }

    [Theory]
    [InlineData(ProductType.LineOfCredit)]
    [InlineData(ProductType.ARAdvance)]
    public void IsApplicable_ReturnsFalse_ForNonInHouseCredit(ProductType productType)
    {
        // Act
        var result = _strategy.IsApplicable(productType);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetDueLoans_WhenCompanyNotFound_ReturnsEmptyList()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        var companyId = "test-company";
        var loanId = Guid.NewGuid();

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        var upcomingLoan = new AutoPayLoan
        {
            Id = loanId,
            CompanyId = companyId,
            CreditId = Guid.NewGuid(),
            CreditStatus = CreditStatus.Active,
            IsAutoCollectionPaused = false
        };

        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.InHouseCredit, null))
            .ReturnsAsync(new List<AutoPayLoan> { upcomingLoan });

        _companyHttpClientMock
            .Setup(x => x.GetCompanyByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((CompanyModel)null);

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDueLoans_WhenCompanyInCollection_ReturnsEmptyList()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        var companyId = "test-company";
        var loanId = Guid.NewGuid();

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        var upcomingLoan = new AutoPayLoan
        {
            Id = loanId,
            CompanyId = companyId,
            CreditId = Guid.NewGuid(),
            CreditStatus = CreditStatus.Active,
            IsAutoCollectionPaused = false
        };

        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.InHouseCredit, null))
            .ReturnsAsync(new List<AutoPayLoan> { upcomingLoan });

        _companyHttpClientMock
            .Setup(x => x.GetCompanyByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel
            {
                Id = companyId,
                AccountStatus = AccountStatusEnum.InCollection
            });

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDueLoans_WithValidCompanyAndLoans_ProcessesAndSendsMessages()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        var companyId = "test-company";
        var loanId = Guid.NewGuid();
        var traceId = "test-trace-id";

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);
        _traceIdAccessorMock.Setup(x => x.TraceId).Returns(traceId);

        var upcomingLoan = new AutoPayLoan
        {
            Id = loanId,
            CompanyId = companyId,
            CreditId = Guid.NewGuid(),
            CreditStatus = CreditStatus.Active,
            IsAutoCollectionPaused = false
        };

        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.InHouseCredit, null))
            .ReturnsAsync(new List<AutoPayLoan> { upcomingLoan });

        _companyHttpClientMock
            .Setup(x => x.GetCompanyByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel
            {
                Id = companyId,
                AccountStatus = AccountStatusEnum.Active,
                Settings = new CompanySettingsModel
                {
                    InHouseCredit = new CompanyInHouseCreditModel
                    {
                        IsAutoPayEnabledByCompanyUser = true,
                        IsAutoPayRequired = false
                    }
                }
            });

        var expectedMessage = new DueLoanMessage
        {
            CompanyId = companyId,
            ProductType = ProductType.InHouseCredit,
            Loans = new List<DueLoanItem> { new() { LoanId = loanId } }
        };

        _dueLoanMessagesGeneratorMock
            .Setup(x => x.GenerateDueLoansMessage(It.IsAny<List<AutoPayLoan>>(), ProductType.InHouseCredit, companyId))
            .Returns(expectedMessage);

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Single(result);
        Assert.Equal(companyId, result[0].CompanyId);
        Assert.Equal(ProductType.InHouseCredit, result[0].ProductType);

        _processDueMessageSenderMock.Verify(
            x => x.SendMessages(It.IsAny<List<ServiceBusMessageBt<DueLoanMessage>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}