using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.AutoPay.Abstractions.Senders;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace BlueTape.Services.LMS.AutoPay.Tests.GetDueStrategies;

public class GetDueLOCStrategyTests
{
    private readonly Mock<ILogger<GetDueLOCStrategy>> _loggerMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IAutoPayLoanService> _autoPayLoanServiceMock;
    private readonly Mock<IOptions<ProccessDueMessagingOptions>> _optionsMock;
    private readonly Mock<IProcessDueMessageSender> _processDueMessageSenderMock;
    private readonly Mock<IDueLoanMessagesGenerator> _dueLoanMessagesGeneratorMock;
    private readonly Mock<ILoanApplicationRepository> _loanApplicationRepositoryMock;
    private readonly Mock<ICompanyHttpClient> _companyHttpClientMock;
    private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock;
    private readonly GetDueLOCStrategy _strategy;

    public GetDueLOCStrategyTests()
    {
        _loggerMock = new Mock<ILogger<GetDueLOCStrategy>>();
        _dateProviderMock = new Mock<IDateProvider>();
        _autoPayLoanServiceMock = new Mock<IAutoPayLoanService>();
        _optionsMock = new Mock<IOptions<ProccessDueMessagingOptions>>();
        _processDueMessageSenderMock = new Mock<IProcessDueMessageSender>();
        _dueLoanMessagesGeneratorMock = new Mock<IDueLoanMessagesGenerator>();
        _loanApplicationRepositoryMock = new Mock<ILoanApplicationRepository>();
        _companyHttpClientMock = new Mock<ICompanyHttpClient>();
        _traceIdAccessorMock = new Mock<ITraceIdAccessor>();

        _optionsMock.Setup(x => x.Value).Returns(new ProccessDueMessagingOptions
        {
            MaxMessagesInBatchCount = 10,
            MaxSimultaneouslySentMessagesCount = 5,
            ScheduledPeriodDurationBetweenMessagesInMinutes = 1
        });

        _strategy = new GetDueLOCStrategy(
            _loggerMock.Object,
            _dateProviderMock.Object,
            _autoPayLoanServiceMock.Object,
            _optionsMock.Object,
            _processDueMessageSenderMock.Object,
            _dueLoanMessagesGeneratorMock.Object,
            _loanApplicationRepositoryMock.Object,
            _companyHttpClientMock.Object,
            _traceIdAccessorMock.Object);
    }

    [Fact]
    public void IsApplicable_ReturnsTrue_ForLineOfCredit()
    {
        // Act
        var result = _strategy.IsApplicable(ProductType.LineOfCredit);

        // Assert
        Assert.True(result);
    }

    [Theory]
    [InlineData(ProductType.InHouseCredit)]
    [InlineData(ProductType.ARAdvance)]
    public void IsApplicable_ReturnsFalse_ForNonLineOfCredit(ProductType productType)
    {
        // Act
        var result = _strategy.IsApplicable(productType);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetDueLoans_WhenCompanyNotFound_ReturnsEmptyList()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        var companyId = "test-company";
        var loanId = Guid.NewGuid();

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        var upcomingLoan = new AutoPayLoan
        {
            Id = loanId,
            CompanyId = companyId,
            CreditId = Guid.NewGuid(),
            CreditStatus = CreditStatus.Active,
            IsAutoCollectionPaused = false
        };

        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.LineOfCredit, null))
            .ReturnsAsync(new List<AutoPayLoan> { upcomingLoan });

        _companyHttpClientMock
            .Setup(x => x.GetCompanyByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((CompanyModel)null);

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDueLoans_WhenCompanyInCollection_ReturnsEmptyList()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        var companyId = "test-company";
        var loanId = Guid.NewGuid();

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        var upcomingLoan = new AutoPayLoan
        {
            Id = loanId,
            CompanyId = companyId,
            CreditId = Guid.NewGuid(),
            CreditStatus = CreditStatus.Active,
            IsAutoCollectionPaused = false
        };

        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.LineOfCredit, null))
            .ReturnsAsync(new List<AutoPayLoan> { upcomingLoan });

        _companyHttpClientMock
            .Setup(x => x.GetCompanyByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel
            {
                Id = companyId,
                AccountStatus = AccountStatusEnum.InCollection
            });

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDueLoans_WithValidCompanyAndLoans_ProcessesAndSendsMessages()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        var companyId = "test-company";
        var loanId = Guid.NewGuid();
        var traceId = "test-trace-id";

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);
        _traceIdAccessorMock.Setup(x => x.TraceId).Returns(traceId);

        var upcomingLoan = new AutoPayLoan
        {
            Id = loanId,
            CompanyId = companyId,
            CreditId = Guid.NewGuid(),
            CreditStatus = CreditStatus.Active,
            IsAutoCollectionPaused = false
        };

        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.LineOfCredit, null))
            .ReturnsAsync(new List<AutoPayLoan> { upcomingLoan });

        _companyHttpClientMock
            .Setup(x => x.GetCompanyByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel
            {
                Id = companyId,
                AccountStatus = AccountStatusEnum.Active
            });

        _loanApplicationRepositoryMock
            .Setup(x => x.GetLightByLmsIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<LightLoanApplicationDocument>
            {
                new()
                {
                    LmsId = loanId.ToString(),
                    Status = "approved",
                    Metadata = new LoanApplicationMetadataDocument
                    {
                        PaymentCancelled = false
                    }
                }
            });

        var expectedMessage = new DueLoanMessage
        {
            CompanyId = companyId,
            ProductType = ProductType.LineOfCredit,
            Loans = new List<DueLoanItem> { new() { LoanId = loanId } }
        };

        _dueLoanMessagesGeneratorMock
            .Setup(x => x.GenerateDueLoansMessage(It.IsAny<List<AutoPayLoan>>(), ProductType.LineOfCredit, companyId))
            .Returns(expectedMessage);

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Single(result);
        Assert.Equal(companyId, result[0].CompanyId);
        Assert.Equal(ProductType.LineOfCredit, result[0].ProductType);

        _processDueMessageSenderMock.Verify(
            x => x.SendMessages(It.IsAny<List<ServiceBusMessageBt<DueLoanMessage>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetDueLoans_WithAutoCollectionPaused_FiltersOutPausedLoans()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        var companyId = "test-company";
        var loanId1 = Guid.NewGuid();
        var loanId2 = Guid.NewGuid();

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        var upcomingLoans = new List<AutoPayLoan>
        {
            new()
            {
                Id = loanId1,
                CompanyId = companyId,
                CreditId = Guid.NewGuid(),
                CreditStatus = CreditStatus.Active,
                IsAutoCollectionPaused = true // This loan should be filtered out
            },
            new()
            {
                Id = loanId2,
                CompanyId = companyId,
                CreditId = Guid.NewGuid(),
                CreditStatus = CreditStatus.Active,
                IsAutoCollectionPaused = false
            }
        };

        _autoPayLoanServiceMock
            .Setup(x => x.GetUpcoming(currentDate, 3, It.IsAny<CancellationToken>(), ProductType.LineOfCredit, null))
            .ReturnsAsync(upcomingLoans);

        _companyHttpClientMock
            .Setup(x => x.GetCompanyByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new CompanyModel
            {
                Id = companyId,
                AccountStatus = AccountStatusEnum.Active
            });

        _loanApplicationRepositoryMock
            .Setup(x => x.GetLightByLmsIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<LightLoanApplicationDocument>
            {
                new()
                {
                    LmsId = loanId2.ToString(),
                    Status = "approved",
                    Metadata = new LoanApplicationMetadataDocument
                    {
                        PaymentCancelled = false
                    }
                }
            });

        var expectedMessage = new DueLoanMessage
        {
            CompanyId = companyId,
            ProductType = ProductType.LineOfCredit,
            Loans = new List<DueLoanItem> { new() { LoanId = loanId2 } }
        };

        _dueLoanMessagesGeneratorMock
            .Setup(x => x.GenerateDueLoansMessage(It.Is<List<AutoPayLoan>>(l => l.Count == 1 && l[0].Id == loanId2), ProductType.LineOfCredit, companyId))
            .Returns(expectedMessage);

        // Act
        var result = await _strategy.GetDueLoans(CancellationToken.None);

        // Assert
        Assert.Single(result);
        Assert.Equal(companyId, result[0].CompanyId);
        Assert.Single(result[0].Loans);
        Assert.Equal(loanId2, result[0].Loans[0].LoanId);
    }
}