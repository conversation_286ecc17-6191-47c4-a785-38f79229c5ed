using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Utilities.Providers;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.AutoPay.Tests.ProcessDueStrategies;

public class ProcessDueBaseStrategyTests
{
    private readonly Mock<ICompanyHttpClient> _companyHttpClientMock;
    private readonly Mock<ILoanRepository> _loanRepositoryMock;
    private readonly Mock<IValidator<DueLoanItem>> _dueLoanItemValidatorMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IPaymentExternalService> _paymentExternalServiceMock;
    private readonly Mock<INotificationService> _notificationServiceMock;
    private readonly Mock<ILogger<ProcessDueBaseStrategy>> _loggerMock;
    private readonly TestProcessDueStrategy _strategy;

    public ProcessDueBaseStrategyTests()
    {
        _companyHttpClientMock = new Mock<ICompanyHttpClient>();
        _loanRepositoryMock = new Mock<ILoanRepository>();
        _dueLoanItemValidatorMock = new Mock<IValidator<DueLoanItem>>();
        _dateProviderMock = new Mock<IDateProvider>();
        _paymentExternalServiceMock = new Mock<IPaymentExternalService>();
        _notificationServiceMock = new Mock<INotificationService>();
        _loggerMock = new Mock<ILogger<ProcessDueBaseStrategy>>();

        _strategy = new TestProcessDueStrategy(
            _companyHttpClientMock.Object,
            _loanRepositoryMock.Object,
            _dueLoanItemValidatorMock.Object,
            _dateProviderMock.Object,
            _notificationServiceMock.Object,
            _loggerMock.Object
        );
    }

    [Fact]
    public async Task ProcessDue_NoBankAccounts_ShouldThrowException()
    {
        // Arrange
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = Guid.NewGuid() }
            }
        };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<BankAccountModel>());

        // Act & Assert
        await Should.ThrowAsync<InvalidOperationException>(async () =>
            await _strategy.ProcessDue(message, CancellationToken.None));
    }

    [Fact]
    public async Task ProcessDue_NoApplicableAccount_ShouldUsePrimaryAccount()
    {
        // Arrange
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = DateOnly.FromDateTime(DateTime.UtcNow) }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimary = false, PaymentMethodType = "bank" },
            new() { IsPrimary = true, PaymentMethodType = "bank" },
            new() { IsPrimary = false, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dueLoanItemValidatorMock.Setup(x => x.ValidateAsync(It.IsAny<DueLoanItem>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new FluentValidation.Results.ValidationResult());
        _dateProviderMock.Setup(x => x.CurrentDate)
            .Returns(DateOnly.FromDateTime(DateTime.UtcNow));

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _strategy.UsedBankAccount.IsPrimary.GetValueOrDefault().ShouldBeTrue();
    }

    [Fact]
    public async Task ProcessDue_NoApplicableAndNoPrimaryAccount_ShouldUseFirstAccount()
    {
        // Arrange
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = DateOnly.FromDateTime(DateTime.UtcNow) }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimary = false, PaymentMethodType = "bank", Id = "first" },
            new() { IsPrimary = false, PaymentMethodType = "bank", Id = "second" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dueLoanItemValidatorMock.Setup(x => x.ValidateAsync(It.IsAny<DueLoanItem>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new FluentValidation.Results.ValidationResult());
        _dateProviderMock.Setup(x => x.CurrentDate)
            .Returns(DateOnly.FromDateTime(DateTime.UtcNow));

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _strategy.UsedBankAccount.Id.ShouldBe("first");
    }

    [Fact]
    public async Task ProcessDue_AutoCollectionPaused_ShouldSkipPayment()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = today, NextPaymentAmount = 1000 }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimary = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = true };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _strategy.PaymentInitialized.ShouldBeFalse();
    }

    [Fact]
    public async Task ProcessDue_DeactivatedBankAccounts_ShouldBeFiltered()
    {
        // Arrange
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = DateOnly.FromDateTime(DateTime.UtcNow) }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsDeactivated = true, PaymentMethodType = "bank", Id = "deactivated" },
            new() { IsDeactivated = false, PaymentMethodType = "bank", Id = "active" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dueLoanItemValidatorMock.Setup(x => x.ValidateAsync(It.IsAny<DueLoanItem>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new FluentValidation.Results.ValidationResult());
        _dateProviderMock.Setup(x => x.CurrentDate)
            .Returns(DateOnly.FromDateTime(DateTime.UtcNow));

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _strategy.UsedBankAccount.Id.ShouldBe("active");
    }

    private class TestProcessDueStrategy : ProcessDueBaseStrategy
    {
        public BankAccountModel UsedBankAccount { get; private set; }
        public bool PaymentInitialized { get; private set; }

        public TestProcessDueStrategy(
            ICompanyHttpClient companyHttpClient,
            ILoanRepository loanRepository,
            IValidator<DueLoanItem> dueLoanItemValidator,
            IDateProvider dateProvider,
            INotificationService notificationService,
            ILogger<ProcessDueBaseStrategy> logger)
            : base(companyHttpClient, loanRepository, dueLoanItemValidator, dateProvider, notificationService, logger)
        {
        }

        protected override Task InitializeAchPayment(AutoPayModel autoPay, CancellationToken cancellationToken)
        {
            UsedBankAccount = autoPay.BankAccount;
            PaymentInitialized = true;
            return Task.CompletedTask;
        }

        protected override Task InitializeCardPayment(AutoPayModel autoPay, CancellationToken cancellationToken)
        {
            UsedBankAccount = autoPay.BankAccount;
            PaymentInitialized = true;
            return Task.CompletedTask;
        }

        protected override Task NotifyDueInThreeDays(AutoPayModel autoPay, CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }

        protected override BankAccountModel GetApplicableBankAccountByProduct(List<BankAccountModel> bankAccounts)
        {
            return null; // Force fallback to primary/first account
        }
    }
} 