using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Utilities.Providers;
using FluentValidation;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.AutoPay.Tests.ProcessDueStrategies;

public class ProcessDueIHCStrategyTests
{
    private readonly Mock<ICompanyHttpClient> _companyHttpClientMock;
    private readonly Mock<ILoanRepository> _loanRepositoryMock;
    private readonly Mock<IValidator<DueLoanItem>> _dueLoanItemValidatorMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IPaymentExternalService> _paymentExternalServiceMock;
    private readonly Mock<INotificationService> _notificationServiceMock;
    private readonly Mock<ILogger<ProcessDueBaseStrategy>> _loggerMock;
    private readonly ProcessDueIHCStrategy _strategy;

    public ProcessDueIHCStrategyTests()
    {
        _companyHttpClientMock = new Mock<ICompanyHttpClient>();
        _loanRepositoryMock = new Mock<ILoanRepository>();
        _dueLoanItemValidatorMock = new Mock<IValidator<DueLoanItem>>();
        _dateProviderMock = new Mock<IDateProvider>();
        _paymentExternalServiceMock = new Mock<IPaymentExternalService>();
        _notificationServiceMock = new Mock<INotificationService>();
        _loggerMock = new Mock<ILogger<ProcessDueBaseStrategy>>();

        _strategy = new ProcessDueIHCStrategy(
            _companyHttpClientMock.Object,
            _loanRepositoryMock.Object,
            _dueLoanItemValidatorMock.Object,
            _dateProviderMock.Object,
            _paymentExternalServiceMock.Object,
            _notificationServiceMock.Object,
            _loggerMock.Object
        );
    }

    [Fact]
    public async Task ProcessDue_NoLoans_ShouldLogAndReturn()
    {
        // Arrange
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>()
        };

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _companyHttpClientMock.Verify(x => x.GetBankAccountsByCompanyIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => true),
                null,
                It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)),
            Times.Once);
    }

    [Fact]
    public async Task ProcessDue_WithLoans_ShouldProcessEachLoan()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = today, NextPaymentAmount = 1000 }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForIHCAutoPay = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateIhcRepaymentAch(
            It.Is<AutoPayModel>(m => m.PaymentAmount == 1000), 
            It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    [Fact]
    public async Task GetApplicableBankAccount_WithIHCPrimaryAccount_ShouldReturnIHCPrimaryAccount()
    {
        // Arrange
        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForIHCAutoPay = false },
            new() { IsPrimaryForIHCAutoPay = true },
            new() { IsPrimaryForIHCAutoPay = false }
        };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync("test-company", It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);

        // Act
        var result = await _companyHttpClientMock.Object.GetBankAccountsByCompanyIdAsync("test-company", CancellationToken.None);
        var ihcAccount = result.FirstOrDefault(x => x.IsPrimaryForIHCAutoPay == true);

        // Assert
        ihcAccount.ShouldNotBeNull();
        ihcAccount.IsPrimaryForIHCAutoPay.GetValueOrDefault().ShouldBeTrue();
    }

    [Fact]
    public async Task ProcessDue_WithOverdueAmount_ShouldProcessOverduePayment()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() 
                { 
                    LoanId = Guid.NewGuid(), 
                    NextPaymentDate = today.AddDays(5), 
                    IsOverdue = true,
                    OverDueAmount = 500
                }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForIHCAutoPay = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateIhcRepaymentAch(
            It.Is<AutoPayModel>(m => m.PaymentAmount == 500), 
            It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    [Fact]
    public async Task ProcessDue_DueInThreeDays_ShouldSendNotification()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() 
                { 
                    LoanId = Guid.NewGuid(), 
                    NextPaymentDate = today.AddDays(3), 
                    NextPaymentAmount = 1000
                }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForIHCAutoPay = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _notificationServiceMock.Verify(x => x.NotifyUsersInvoiceDueIn3Days(
            It.IsAny<AutoPayModel>(), 
            It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    [Fact]
    public async Task ProcessDue_WithCardPayment_ShouldProcessCardPayment()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() 
                { 
                    LoanId = Guid.NewGuid(), 
                    NextPaymentDate = today,
                    NextPaymentAmount = 1000
                }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForIHCAutoPay = true, PaymentMethodType = "card" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateIhcRepaymentCard(
            It.Is<AutoPayModel>(m => m.PaymentAmount == 1000), 
            It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    [Fact]
    public async Task ProcessDue_WithMultipleLoans_ShouldProcessAllLoans()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = today, NextPaymentAmount = 1000 },
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = today, NextPaymentAmount = 2000 }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForIHCAutoPay = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateIhcRepaymentAch(
            It.IsAny<AutoPayModel>(), 
            It.IsAny<CancellationToken>()), 
            Times.Exactly(2));
    }

    [Fact]
    public async Task ProcessDue_WithValidationError_ShouldContinueProcessingOtherLoans()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var firstLoanId = Guid.NewGuid();
        var secondLoanId = Guid.NewGuid();
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = firstLoanId, NextPaymentDate = today, NextPaymentAmount = 1000 },
                new() { LoanId = secondLoanId, NextPaymentDate = today, NextPaymentAmount = 2000 }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForIHCAutoPay = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x => x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Setup validation to fail for the first loan
        _dueLoanItemValidatorMock.Setup(x => x.ValidateAsync(It.Is<DueLoanItem>(l => l.LoanId == firstLoanId), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ValidationException("Validation failed"));

        // Setup validation to pass for the second loan
        _dueLoanItemValidatorMock.Setup(x => x.ValidateAsync(It.Is<DueLoanItem>(l => l.LoanId == secondLoanId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new FluentValidation.Results.ValidationResult());

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateIhcRepaymentAch(
            It.Is<AutoPayModel>(m => m.PaymentAmount == 2000), 
            It.IsAny<CancellationToken>()), 
            Times.Once);
    }
} 