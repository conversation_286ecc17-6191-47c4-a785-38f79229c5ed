﻿using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Domain.Enums;

namespace BlueTape.Services.LMS.AutoPay.Abstractions.Services
{
    public interface IDueLoanMessagesGenerator
    {
        public DueLoanMessage GenerateDueLoansMessage(List<AutoPayLoan> loans,
            ProductType productType, string companyId);
    }
}
