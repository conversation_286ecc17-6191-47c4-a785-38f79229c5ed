﻿using BlueTape.Services.LMS.AutoPay.Abstractions.Senders;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy;
using BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy.Abstractions;
using BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy;
using BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy.Abstractions;
using BlueTape.Services.LMS.AutoPay.Senders;
using BlueTape.Services.LMS.AutoPay.Services;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Services.LMS.AutoPay.DI;

public static class DependencyRegistrar
{
    public static void AddAutoPayDependencies(this IServiceCollection services)
    {
        services.AddTransient<IAutoPayLoanService, AutoPayLoanService>();
        services.AddTransient<IDueLoanMessagesGenerator, DueLoanMessagesGenerator>();
        services.AddTransient<IProcessDueMessageSender, ProcessDueMessageSender>();
            
        services.AddTransient<IGetDueStrategy, GetDueLOCStrategy>();
        services.AddTransient<IGetDueStrategy, GetDueIHCStrategy>();
            
        services.AddTransient<IProcessDueStrategy, ProcessDueLOCStrategy>();
        services.AddTransient<IProcessDueStrategy, ProcessDueIHCStrategy>();
    }
}