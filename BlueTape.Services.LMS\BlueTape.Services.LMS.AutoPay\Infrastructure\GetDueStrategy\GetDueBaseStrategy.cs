﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.AutoPay.Abstractions.Senders;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy.Abstractions;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy;

public abstract class GetDueBaseStrategy(
    ILogger<GetDueBaseStrategy> logger,
    IDateProvider dateProvider,
    IAutoPayLoanService autoPayLoanService,
    IOptions<ProccessDueMessagingOptions> options,
    IProcessDueMessageSender processDueMessageSender,
    IDueLoanMessagesGenerator dueLoanMessagesGenerator,
    ITraceIdAccessor traceIdAccessor,
    ProductType productType)
    : IGetDueStrategy
{
    public virtual bool IsApplicable(ProductType productType) => false;
    public async Task<List<DueLoanMessage>> GetDueLoans(CancellationToken cancellationToken, string? companyId = null)
    {
        var todayDate = dateProvider.CurrentDate;

        logger.LogInformation("Starting LOC draws processing for date: {DueDate}", todayDate);

        try
        {
            var upcomingLoans =
                (await autoPayLoanService.GetUpcoming(todayDate, 3, cancellationToken, productType, companyId))
                .ToList();

            if (upcomingLoans.Count == 0)
            {
                logger.LogInformation("No upcoming {Product} draws found for date: {DueDate}", productType, todayDate);
                return [];
            }

            var groupedLoansByCompanyId = upcomingLoans.AsParallel().GroupBy(loan => loan.CompanyId).ToList();

            List<DueLoanMessage> messages = [];

            foreach (var group in groupedLoansByCompanyId)
            {
                var eligibleLoans = await GetEligibleLoans(group, cancellationToken);

                if (eligibleLoans.Count == 0) continue;

                var message = dueLoanMessagesGenerator.GenerateDueLoansMessage(eligibleLoans, productType, group.Key);

                messages.Add(message);
            }

            logger.LogInformation("Formed {Count} {Product} batches for processing", messages.Count, productType);

            await SendBatchesToServiceBus(messages.AsParallel().Select(GetServiceBusMessage).ToList(), cancellationToken);

            logger.LogInformation("Successfully sent {Count} {Product} batches to Service Bus", messages.Count, productType);

            return messages;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing {Product} draws for date: {DueDate}", productType, todayDate);
            throw;
        }
    }

    protected abstract Task<List<AutoPayLoan>> GetEligibleLoans(IGrouping<string, AutoPayLoan> loans, CancellationToken cancellationToken);

    private async Task SendBatchesToServiceBus(IReadOnlyList<ServiceBusMessageBt<DueLoanMessage>> events, CancellationToken cancellationToken)
    {
        if (!events.Any()) return;
        var scheduledPeriodInMinutes = options.Value.ScheduledPeriodDurationBetweenMessagesInMinutes;
        var maxSimultaneouslySentMessagesCount = options.Value.MaxSimultaneouslySentMessagesCount;
        var currentDateTimeOffset = DateTimeOffset.UtcNow;
        for (var messagesSentCount = 0; messagesSentCount < events.Count; messagesSentCount++)
        {
            var currentEvent = events[messagesSentCount];
            var offsetInSeconds = (60 * (messagesSentCount / maxSimultaneouslySentMessagesCount) * scheduledPeriodInMinutes) + (messagesSentCount % 2) * 10;
            currentEvent.MessageAttributes!.ScheduledEnqueueTimeUtc = currentDateTimeOffset.AddSeconds(offsetInSeconds);
        }
        var eventsBatches = events.Select((x, i) => new { Index = i, Value = x })
            .GroupBy(x => x.Index / options.Value.MaxMessagesInBatchCount)
            .Select(x => x.Select(v => v.Value).ToList())
            .ToList();

        foreach (var eventsBatch in eventsBatches)
        {
            await processDueMessageSender.SendMessages(eventsBatch, cancellationToken);
        }
    }

    private static bool IsCreditNotEligible(CreditStatus creditStatus) =>
        creditStatus is (CreditStatus.InCollection or CreditStatus.Closed);

    private static bool IsAccountNotEligible(AccountStatusEnum? accountStatus) =>
        accountStatus is (AccountStatusEnum.Closed or AccountStatusEnum.InCollection);

    protected bool IsCompanyNotEligible(CompanyModel? company, string loanIdsJson)
    {
        if (company is null) return true;

        if (IsAccountNotEligible(company.AccountStatus))
        {
            logger.LogInformation(
                "{ProductType} loans for company {CompanyId} have not eligible company account status {CompanyStatus}, loan ids: {ids}",
                productType, company.Id, company.AccountStatus, loanIdsJson);
            return true;
        }

        return false;
    }

    private bool IsAutoCollectionPaused(AutoPayLoan loan)
    {
        if (loan.IsAutoCollectionPaused)
        {
            logger.LogInformation(
                "{ProductType} loan {LoanId} for company {CompanyId} has auto-collection paused at {PausedAt} by {PausedBy}",
                productType, loan.Id, loan.CompanyId, loan.AutoCollectionPausedAt, loan.AutoCollectionPausedBy);
            return true;
        }
        return false;
    }

    protected List<AutoPayLoan> FilterLoans(IGrouping<string, AutoPayLoan> loans)
    {
        var filteredLoans = new List<AutoPayLoan>();

        loans.AsParallel().GroupBy(loan => loan.CreditId).ForAll(groupByCredit =>
        {
            var loansInGroup = groupByCredit.ToList();
            var firstLoan = loansInGroup.First();
            if (IsCreditNotEligible(firstLoan.CreditStatus))
            {
                logger.LogInformation(
                    "{ProductType} loans for company {CompanyId} for credit {CreditId} have not eligible credit status {CreditStatus}",
                    productType, firstLoan.CompanyId, firstLoan.CreditId, firstLoan.CreditId);
                return;
            }

            filteredLoans.AddRange(loansInGroup.AsParallel().Where(loan => !IsAutoCollectionPaused(loan)).ToList());

        });

        return filteredLoans;
    }

    private ServiceBusMessageBt<DueLoanMessage> GetServiceBusMessage(DueLoanMessage message) =>
        new ServiceBusMessageBt<DueLoanMessage>(message, new ServiceBusMessageAttributes()
        {
            CorrelationId = traceIdAccessor.TraceId
        });
}