﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.AutoPay.Abstractions.Senders;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy;

public class GetDueIHCStrategy(
    ILogger<GetDueIHCStrategy> logger,
    IDateProvider dateProvider,
    IAutoPayLoanService autoPayLoanService,
    IOptions<ProccessDueMessagingOptions> options,
    IProcessDueMessageSender processDueMessageSender,
    IDueLoanMessagesGenerator dueLoanMessagesGenerator,
    ICompanyHttpClient companyHttpClient,
    ITraceIdAccessor traceIdAccessor) : GetDueBaseStrategy(logger, dateProvider, autoPayLoanService, options,
    processDueMessageSender, dueLoanMessagesGenerator, traceIdAccessor, ProductType.InHouseCredit)
{
    public override bool IsApplicable(ProductType productType) => productType is ProductType.InHouseCredit;
    protected override async Task<List<AutoPayLoan>> GetEligibleLoans(IGrouping<string, AutoPayLoan> loans, CancellationToken cancellationToken)
    {
        if (await IsCompanyNotEligible(loans, cancellationToken)) return [];

        return FilterLoans(loans);
    }

    private async Task<bool> IsCompanyNotEligible(IGrouping<string, AutoPayLoan> loans, CancellationToken cancellationToken)
    {
        var companyId = loans.First().CompanyId!;
        var loanIds = JsonSerializer.Serialize(loans.Select(x => x.Id));
        var company = await companyHttpClient.GetCompanyByIdAsync(companyId, cancellationToken);

        if (IsCompanyNotEligible(company, loanIds)) return true;

        var ihcSettings = company!.Settings?.InHouseCredit;
        if (ihcSettings == null)
        {
            logger.LogInformation("Company {CompanyId} has no IHC settings configured", company.Id);
            return true;
        }

        var isAutoPayEnabled = ihcSettings.IsAutoPayEnabledByCompanyUser.GetValueOrDefault() ||
                               ihcSettings.IsAutoPayRequired.GetValueOrDefault();
        if (!isAutoPayEnabled)
        {
            logger.LogInformation("Company {CompanyId} has no auto-pay enabled (not required and not enabled by user), loan ids: {ids}",
                company.Id, loanIds);
            return true;
        }

        return false;
    }
}