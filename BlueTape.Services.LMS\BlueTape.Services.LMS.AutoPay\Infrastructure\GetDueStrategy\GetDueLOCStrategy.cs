﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.AutoPay.Abstractions.Senders;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy;

public class GetDueLOCStrategy(
    ILogger<GetDueLOCStrategy> logger,
    IDateProvider dateProvider,
    IAutoPayLoanService autoPayLoanService,
    IOptions<ProccessDueMessagingOptions> options,
    IProcessDueMessageSender processDueMessageSender,
    IDueLoanMessagesGenerator dueLoanMessagesGenerator,
    ILoanApplicationRepository loanApplicationRepository,
    ICompanyHttpClient companyHttpClient,
    ITraceIdAccessor traceIdAccessor) : GetDueBaseStrategy(logger, dateProvider, autoPayLoanService, options,
    processDueMessageSender, dueLoanMessagesGenerator, traceIdAccessor, ProductType.LineOfCredit)
{
    public override bool IsApplicable(ProductType productType) => productType is ProductType.LineOfCredit;

    protected override async Task<List<AutoPayLoan>> GetEligibleLoans(IGrouping<string, AutoPayLoan> loans, CancellationToken cancellationToken)
    {
        if (await IsCompanyNotEligible(loans, cancellationToken)) return [];

        var filteredLoans = FilterLoans(loans);

        filteredLoans = await FilterLoansByLoanApps(filteredLoans, cancellationToken);

        return filteredLoans;
    }

    private async Task<List<AutoPayLoan>> FilterLoansByLoanApps(List<AutoPayLoan> loans,
        CancellationToken cancellationToken)
    {
        var loansForAutoPay = new List<AutoPayLoan>();

        var loanApplications = await loanApplicationRepository.GetLightByLmsIds(loans.Select(l => l.Id.ToString()).ToList(), cancellationToken);
        var approvedApps = loanApplications
            .AsParallel()
            .Where(app => app.Status == "approved")
            .ToDictionary(app => Guid.Parse(app.LmsId!));

        loans
            .AsParallel()
            .Where(loan => approvedApps.ContainsKey(loan.Id))
            .ToDictionary(loan => loan, loan => approvedApps[loan.Id])
            .AsParallel()
            .ForAll(loan =>
            {
                if (loan.Value?.Metadata?.PaymentCancelled.GetValueOrDefault() == true)
                {
                    logger.LogInformation("LOC loan {LoanId} has auto-pay disabled via metadata.payment_cancelled", loan.Key.Id);
                    return;
                }

                loansForAutoPay.Add(loan.Key);
            });

        return loansForAutoPay;
    }

    private async Task<bool> IsCompanyNotEligible(IGrouping<string, AutoPayLoan> loans, CancellationToken cancellationToken)
    {
        var companyId = loans.First().CompanyId!;
        var loanIds = JsonSerializer.Serialize(loans.Select(x => x.Id));
        var company = await companyHttpClient.GetCompanyByIdAsync(companyId, cancellationToken);

        return IsCompanyNotEligible(company, loanIds);
    }
}