﻿using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy.Abstractions;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Utilities.Providers;
using FluentValidation;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy;

public abstract class ProcessDueBaseStrategy(
    ICompanyHttpClient companyHttpClient,
    ILoanRepository loanRepository,
    IValidator<DueLoanItem> dueLoanItemValidator,
    IDateProvider dateProvider,
    INotificationService notificationService,
    ILogger<ProcessDueBaseStrategy> logger
    ) : IProcessDueStrategy
{
    public virtual bool IsApplicable(ProductType productType) => false;

    public async Task ProcessDue(DueLoanMessage message, CancellationToken cancellationToken)
    {
        if (message.Loans.Count == 0)
        {
            logger.LogInformation("No loans to process in message: {Message}", JsonSerializer.Serialize(message));
            return;
        }
        logger.LogInformation("Processing due loans: {Message}", JsonSerializer.Serialize(message));

        var bankAccount = await GetBankAccount(message.CompanyId, cancellationToken);
        //add Slack notification here

        //Move to separate method
        foreach (var loan in message.Loans)
        {
            try
            {
                await dueLoanItemValidator.ValidateAndThrowAsync(loan, cancellationToken);
                logger.LogInformation("Processed loan: {Loan}", JsonSerializer.Serialize(loan));
                await ProcessDueLoan(loan, bankAccount, cancellationToken);
            }
            catch (ValidationException ex)
            {
                logger.LogError(ex, "Validation failed for loan: {Loan}", JsonSerializer.Serialize(loan));
                // Handle validation failure, e.g., log or notify
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while processing loan: {Loan}", JsonSerializer.Serialize(loan));
                // Notify ops team
            }
        }
    }

    private async Task ProcessDueLoan(DueLoanItem dueLoanItem, BankAccountModel bankAccount, CancellationToken cancellationToken)
    {
        // calculate amount to pay
        var loan = (await loanRepository.GetById(dueLoanItem.LoanId, cancellationToken))!;
        var autoPay = new AutoPayModel()
        {
            BankAccount = bankAccount,
            DueLoanItem = dueLoanItem
        };

        if (!loan.IsAutoCollectionPaused && IsDueTodayOrMissed(dueLoanItem.NextPaymentDate)) // loan due today
        {
            autoPay.PaymentAmount = dueLoanItem.NextPaymentAmount;
            await InitializeRepaymentByPaymentMethod(autoPay, cancellationToken);
        }
        else if (!loan.IsAutoCollectionPaused && dueLoanItem is { IsOverdue: true, OverDueAmount: > 0 }) //loan have overdue amount
        {
            autoPay.PaymentAmount = dueLoanItem.OverDueAmount;
            await InitializeRepaymentByPaymentMethod(autoPay, cancellationToken);
        }
        else if (IsDueInThreeDays(dueLoanItem.NextPaymentDate))
        {
            await NotifyDueInThreeDays(autoPay, cancellationToken);
        }
    }

    private async Task<BankAccountModel> GetBankAccount(string companyId, CancellationToken cancellationToken)
    {
        var bankAccounts =
            await companyHttpClient.GetBankAccountsByCompanyIdAsync(companyId, cancellationToken);

        bankAccounts = bankAccounts.Where(ba => !ba.IsDeactivated.GetValueOrDefault()).ToList();

        if (bankAccounts.Count == 0)
        {
            throw new InvalidOperationException(
                $"No bank accounts found for company with ID: {companyId}");
        }

        var applicableBank = GetApplicableBankAccountByProduct(bankAccounts);
        if (applicableBank is null)
        {
            applicableBank = bankAccounts.FirstOrDefault(ba => ba.IsPrimary.GetValueOrDefault()) ??
                             bankAccounts.First();
        }

        return applicableBank;
    }

    private async Task InitializeRepaymentByPaymentMethod(AutoPayModel autoPay,
        CancellationToken cancellationToken)
    {
        var paymentMethod = autoPay.BankAccount.PaymentMethodType;
        switch (paymentMethod)
        {
            case "bank":
                // Initialize ACH payment
                await InitializeAchPayment(autoPay, cancellationToken);
                break;
            case "card":
                // Initialize Card payment
                await InitializeCardPayment(autoPay, cancellationToken);
                break;
            default:
                throw new InvalidOperationException("Unsupported payment method type: " +
                                                    paymentMethod);
        }
    }

    protected abstract Task InitializeAchPayment(AutoPayModel autoPay, CancellationToken cancellationToken);

    protected abstract Task InitializeCardPayment(AutoPayModel autoPay, CancellationToken cancellationToken);

    protected abstract Task NotifyDueInThreeDays(AutoPayModel autoPay, CancellationToken cancellationToken);

    private bool IsDueInThreeDays(DateOnly nextPaymentDate)
    {
        var today = dateProvider.CurrentDate;
        return nextPaymentDate == today.AddDays(3);
    }

    private bool IsDueTodayOrMissed(DateOnly nextPaymentDate)
    {
        var today = dateProvider.CurrentDate;
        return nextPaymentDate == today || nextPaymentDate < today;
    }

    protected abstract BankAccountModel? GetApplicableBankAccountByProduct(List<BankAccountModel> bankAccounts);
}