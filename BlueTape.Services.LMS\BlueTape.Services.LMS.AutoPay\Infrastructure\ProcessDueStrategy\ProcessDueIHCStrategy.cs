﻿using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Utilities.Providers;
using FluentValidation;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy;

public class ProcessDueIHCStrategy(
    ICompanyHttpClient companyHttpClient,
    ILoanRepository loanRepository,
    IValidator<DueLoanItem> dueLoanItemValidator,
    IDateProvider dateProvider,
    IPaymentExternalService paymentExternalService,
    INotificationService notificationService,
    ILogger<ProcessDueBaseStrategy> logger) : ProcessDueBaseStrategy(companyHttpClient, loanRepository,
    dueLoanItemValidator, dateProvider, notificationService, logger)
{
    public override bool IsApplicable(ProductType productType) => productType is ProductType.InHouseCredit;
    protected override Task InitializeAchPayment(AutoPayModel autoPay, CancellationToken cancellationToken)
    {
        return paymentExternalService.CreateIhcRepaymentAch(autoPay, cancellationToken);
    }

    protected override Task InitializeCardPayment(AutoPayModel autoPay, CancellationToken cancellationToken)
    {
        return paymentExternalService.CreateIhcRepaymentCard(autoPay, cancellationToken);
    }

    protected override BankAccountModel? GetApplicableBankAccountByProduct(List<BankAccountModel> bankAccounts)
    {
        return bankAccounts.FirstOrDefault(x => x.IsPrimaryForIHCAutoPay.GetValueOrDefault());
    }

    protected override Task NotifyDueInThreeDays(AutoPayModel autoPay, CancellationToken cancellationToken)
    {
        return notificationService.NotifyUsersInvoiceDueIn3Days(autoPay, cancellationToken);
    }
}