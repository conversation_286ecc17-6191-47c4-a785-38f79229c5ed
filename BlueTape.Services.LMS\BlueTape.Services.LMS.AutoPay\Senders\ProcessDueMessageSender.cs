﻿using BlueTape.ServiceBusMessaging;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.AutoPay.Abstractions.Senders;
using BlueTape.Services.LMS.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.LMS.AutoPay.Senders;

public class ProcessDueMessageSender(IConfiguration configuration,
    ILogger<ProcessDueMessageSender> logger)
    : ServiceBusMessageSender<DueLoanMessage>(configuration, logger,
            InfrastructureConstants.ProcessDueQueueName, InfrastructureConstants.ProcessDueQueueConnectionString),
        IProcessDueMessageSender;