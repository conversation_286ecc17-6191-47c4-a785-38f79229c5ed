﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace BlueTape.Services.LMS.AutoPay.Services;

public class DueLoanMessagesGenerator(ITraceIdAccessor traceIdAccessor,
    IDateProvider dateProvider,
    ILogger<DueLoanMessagesGenerator> logger) : IDueLoanMessagesGenerator
{
    public DueLoanMessage GenerateDueLoansMessage(List<AutoPayLoan> loans, ProductType productType, string companyId)
    {
        var correlationId = traceIdAccessor.TraceId;

        logger.LogInformation("Generating due loan messages for {Count} companies with product type {ProductType}", loans.Count, productType);

        var eventName = productType switch
        {
            ProductType.LineOfCredit => "CREATE.DRAW.REPAYMENT",
            ProductType.InHouseCredit => "CREATE.IHC.REPAYMENT",
            _ => throw new ArgumentOutOfRangeException(nameof(productType), productType, null)
        };

        var message = new DueLoanMessage
        {
            Event = eventName,
            BlueTapeCorrelationId = correlationId,
            CreatedAt = dateProvider.CurrentDateTime,
            CreatedBy = "BlueTape.LMS.GetDue",
            CompanyId = companyId,
            DueSum = loans.Sum(l => l.NextPaymentAmount),
            DueCount = loans.Count,
            ProductType = productType,
            Loans = loans.Select(l => new DueLoanItem
            {
                LoanId = l.Id,
                PayableIds = l.PayablesIds,
                CompanyId = companyId,
                NextPaymentAmount = l.NextPaymentAmount,
                OverDueAmount = l.OverDueAmount,
                NextPaymentDate = l.NextPaymentDate,
                IsOverdue = l.IsOverdue,
                DueStatus = l.DueStatus.ToString()
            }).ToList()
        };
                
        logger.LogInformation("Generated ProcessDue message {message}", JsonSerializer.Serialize(message));
        
        return message;
    }
}