﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BlueTape.Services.LoanManagementService.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class AddAutoCollectionFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AutoCollectionPausedAt",
                table: "Loans",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AutoCollectionPausedBy",
                table: "Loans",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsAutoCollectionPaused",
                table: "Loans",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AutoCollectionPausedAt",
                table: "Loans");

            migrationBuilder.DropColumn(
                name: "AutoCollectionPausedBy",
                table: "Loans");

            migrationBuilder.DropColumn(
                name: "IsAutoCollectionPaused",
                table: "Loans");
        }
    }
}
