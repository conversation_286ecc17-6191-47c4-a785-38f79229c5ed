﻿using BlueTape.Services.LMS.MonitoringService.Abstractions;
using BlueTape.Services.LMS.MonitoringService.BackgroundServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Quartz;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.MonitoringService.Tests.BackgroundServices
{
    public class MissingTabapayReportFilesJobTests
    {
        private readonly MissingTabapayReportFilesJob _missingTabapayReportFilesJob;
        private readonly Mock<ILogger<MissingTabapayReportFilesJob>> _loggerMock = new();
        private readonly Mock<IServiceScope> _serviceScopeMock = new();
        private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock = new();
        private readonly Mock<IMissingTabapayReportFilesService> _missingTabapayReportFilesServiceMock = new();

        public MissingTabapayReportFilesJobTests()
        {
            _missingTabapayReportFilesJob = new MissingTabapayReportFilesJob(_loggerMock.Object, _serviceScopeFactoryMock.Object);
        }

        [Fact]
        public Task Execute_Valid_ShouldNotThrows()
        {
            Mock<IJobExecutionContext> context = new();

            _missingTabapayReportFilesServiceMock.Setup(x => x.MissingTabapayReportFilesChecking(CancellationToken.None, null)).Throws(new ApplicationException());

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton(_missingTabapayReportFilesServiceMock.Object);

            var serviceProvider = serviceCollection.BuildServiceProvider();

            _serviceScopeMock.SetupGet<IServiceProvider>(s => s.ServiceProvider)
                .Returns(serviceProvider);

            _serviceScopeFactoryMock.Setup(s => s.CreateScope())
                .Returns(_serviceScopeMock.Object);

            return _missingTabapayReportFilesJob.Execute(context.Object).ShouldNotThrowAsync();
        }

        [Fact]
        public Task Execute_Invalid_ShouldNotThrows()
        {
            Mock<IJobExecutionContext> context = new();

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton(_missingTabapayReportFilesServiceMock.Object);

            var serviceProvider = serviceCollection.BuildServiceProvider();

            _serviceScopeMock.SetupGet<IServiceProvider>(s => s.ServiceProvider)
                .Returns(serviceProvider);

            _serviceScopeFactoryMock.Setup(s => s.CreateScope())
                .Returns(_serviceScopeMock.Object);

            return _missingTabapayReportFilesJob.Execute(context.Object).ShouldNotThrowAsync();
        }
    }
}
