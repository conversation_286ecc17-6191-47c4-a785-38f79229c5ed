﻿using BlueTape.Services.LMS.MonitoringService.Abstractions;
using BlueTape.Services.LMS.MonitoringService.BackgroundServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Quartz;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.MonitoringService.Tests.BackgroundServices
{
    public class ProcessingPaymentsForALongTimeJobTests
    {
        private readonly ProcessingPaymentsForALongTimeJob _processingPaymentsForALongTimeJob;
        private readonly Mock<ILogger<ProcessingPaymentsForALongTimeJob>> _loggerMock = new();
        private readonly Mock<IServiceScope> _serviceScopeMock = new();
        private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock = new();
        private readonly Mock<IProcessingPaymentsService> _processingPaymentsService = new();

        public ProcessingPaymentsForALongTimeJobTests()
        {
            _processingPaymentsForALongTimeJob = new ProcessingPaymentsForALongTimeJob(_loggerMock.Object, _serviceScopeFactoryMock.Object);
        }

        [Fact]
        public Task Execute_Valid_ShouldNotThrows()
        {
            Mock<IJobExecutionContext> context = new();

            _processingPaymentsService.Setup(x => x.ProcessingPaymentsChecking(CancellationToken.None, null)).Throws(new ApplicationException());

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton(_processingPaymentsService.Object);

            var serviceProvider = serviceCollection.BuildServiceProvider();

            _serviceScopeMock.SetupGet<IServiceProvider>(s => s.ServiceProvider)
                .Returns(serviceProvider);

            _serviceScopeFactoryMock.Setup(s => s.CreateScope())
                .Returns(_serviceScopeMock.Object);

            return _processingPaymentsForALongTimeJob.Execute(context.Object).ShouldNotThrowAsync();
        }

        [Fact]
        public Task Execute_Invalid_ShouldNotThrows()
        {
            Mock<IJobExecutionContext> context = new();

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton(_processingPaymentsService.Object);

            var serviceProvider = serviceCollection.BuildServiceProvider();

            _serviceScopeMock.SetupGet<IServiceProvider>(s => s.ServiceProvider)
                .Returns(serviceProvider);

            _serviceScopeFactoryMock.Setup(s => s.CreateScope())
                .Returns(_serviceScopeMock.Object);

            return _processingPaymentsForALongTimeJob.Execute(context.Object).ShouldNotThrowAsync();
        }
    }
}
