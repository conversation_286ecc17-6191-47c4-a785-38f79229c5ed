﻿using BlueTape.Services.LMS.MonitoringService.Abstractions;
using BlueTape.Services.LMS.MonitoringService.BackgroundServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Quartz;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.MonitoringService.Tests.BackgroundServices
{
    public class TabapayProcessingCardPaymentForALongTimeJobTests
    {
        private readonly TabapayProcessingCardPaymentForALongTimeJob _tabapayProcessingCardPaymentForALongTimeJob;
        private readonly Mock<ILogger<TabapayProcessingCardPaymentForALongTimeJob>> _loggerMock = new();
        private readonly Mock<IServiceScope> _serviceScopeMock = new();
        private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock = new();
        private readonly Mock<ITabapayProcessingCardPaymentService> _processingPaymentsService = new();

        public TabapayProcessingCardPaymentForALongTimeJobTests()
        {
            _tabapayProcessingCardPaymentForALongTimeJob = new TabapayProcessingCardPaymentForALongTimeJob(_loggerMock.Object, _serviceScopeFactoryMock.Object);
        }

        [Fact]
        public Task Execute_Valid_ShouldNotThrows()
        {
            Mock<IJobExecutionContext> context = new();

            _processingPaymentsService.Setup(x => x.TabapayProcessingCardPaymentChecking(CancellationToken.None, null)).Throws(new ApplicationException());

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton(_processingPaymentsService.Object);

            var serviceProvider = serviceCollection.BuildServiceProvider();

            _serviceScopeMock.SetupGet<IServiceProvider>(s => s.ServiceProvider)
                .Returns(serviceProvider);

            _serviceScopeFactoryMock.Setup(s => s.CreateScope())
                .Returns(_serviceScopeMock.Object);

            return _tabapayProcessingCardPaymentForALongTimeJob.Execute(context.Object).ShouldNotThrowAsync();
        }

        [Fact]
        public Task Execute_Invalid_ShouldNotThrows()
        {
            Mock<IJobExecutionContext> context = new();

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton(_processingPaymentsService.Object);

            var serviceProvider = serviceCollection.BuildServiceProvider();

            _serviceScopeMock.SetupGet<IServiceProvider>(s => s.ServiceProvider)
                .Returns(serviceProvider);

            _serviceScopeFactoryMock.Setup(s => s.CreateScope())
                .Returns(_serviceScopeMock.Object);

            return _tabapayProcessingCardPaymentForALongTimeJob.Execute(context.Object).ShouldNotThrowAsync();
        }
    }
}
