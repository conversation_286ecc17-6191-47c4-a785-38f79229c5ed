﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Moq" Version="4.20.70" />
    <PackageReference Include="Shouldly" Version="4.2.1" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="AWSSDK.S3" Version="3.7.307.23" />
    <PackageReference Include="MongoDB.Bson" Version="2.25.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlueTape.Services.LMS.MonitoringService\BlueTape.Services.LMS.MonitoringService.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.DataAccess\BlueTape.Services.LMS.DataAccess.csproj" />
    <ProjectReference Include="..\BlueTape.Services.LMS.Domain\BlueTape.Services.LMS.Domain.csproj" />
    <ProjectReference Include="..\BlueTape.DataAccess.Mongo\BlueTape.DataAccess.Mongo.csproj" />
    <ProjectReference Include="..\BlueTape.Services.Reporting\BlueTape.Services.Reporting.csproj" />
  </ItemGroup>

</Project>
