﻿using BlueTape.AWSS3;
using BlueTape.AWSS3.Abstractions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Services.LMS.MonitoringService.Services;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace BlueTape.Services.LMS.MonitoringService.Tests.Services;

public class MissingTabapayReportFilesServiceTests
{
    private readonly Mock<IDynamicS3Client> _s3ClientMock;
    private readonly Mock<IOptions<S3ConfigurationOptions>> _optionsMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<ILogger<MissingTabapayReportFilesService>> _loggerMock;
    private readonly Mock<ISlackNotificationService> _slackNotificationServiceMock;
    private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock;
    private readonly MissingTabapayReportFilesService _service;
    private readonly DateTime _currentDate;
    private readonly DateOnly _currentDateOnly;

    public MissingTabapayReportFilesServiceTests()
    {
        _s3ClientMock = new Mock<IDynamicS3Client>();
        _optionsMock = new Mock<IOptions<S3ConfigurationOptions>>();
        _dateProviderMock = new Mock<IDateProvider>();
        _loggerMock = new Mock<ILogger<MissingTabapayReportFilesService>>();
        _slackNotificationServiceMock = new Mock<ISlackNotificationService>();
        _traceIdAccessorMock = new Mock<ITraceIdAccessor>();

        _currentDate = DateTime.UtcNow;
        _currentDateOnly = DateOnly.FromDateTime(_currentDate);
        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(_currentDate);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(_currentDateOnly);
        _traceIdAccessorMock.Setup(x => x.TraceId).Returns("test-trace-id");

        _optionsMock.Setup(x => x.Value).Returns(new S3ConfigurationOptions
        {
            BlueTapeDefaultAwsRegion = "us-east-1"
        });

        Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "dev");

        _service = new MissingTabapayReportFilesService(
            _s3ClientMock.Object,
            _optionsMock.Object,
            _dateProviderMock.Object,
            _loggerMock.Object,
            _slackNotificationServiceMock.Object,
            _traceIdAccessorMock.Object);
    }

    [Fact]
    public async Task MissingTabapayReportFilesChecking_WhenFileExists_ShouldNotSendNotification()
    {
        // Arrange
        var expectedFileName = $"3471_{_currentDateOnly:yyyyMMdd}_transactions_v2-5.csv";
        var expectedBucketName = "dev.uw1.linqpal-tabapay-reports";
        
        _s3ClientMock
            .Setup(x => x.IsFileExistInS3BucketAsync(
                expectedBucketName,
                expectedFileName,
                It.IsAny<S3ConnectionOptions>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await _service.MissingTabapayReportFilesChecking(CancellationToken.None);

        // Assert
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(It.IsAny<EventMessageBody>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task MissingTabapayReportFilesChecking_WhenFileDoesNotExist_ShouldSendNotification()
    {
        // Arrange
        var expectedFileName = $"3471_{_currentDateOnly:yyyyMMdd}_transactions_v2-5.csv";
        var expectedBucketName = "dev.uw1.linqpal-tabapay-reports";
        
        _s3ClientMock
            .Setup(x => x.IsFileExistInS3BucketAsync(
                expectedBucketName,
                expectedFileName,
                It.IsAny<S3ConnectionOptions>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        await _service.MissingTabapayReportFilesChecking(CancellationToken.None);

        // Assert
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(
                It.Is<EventMessageBody>(m => 
                    m.EventLevel == EventLevel.Warning &&
                    m.Message.Contains(expectedFileName)),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task MissingTabapayReportFilesChecking_WhenExceptionOccurs_ShouldSendNotificationAndRethrow()
    {
        // Arrange
        var expectedException = new Exception("Test exception");
        var expectedFileName = $"3471_{_currentDateOnly:yyyyMMdd}_transactions_v2-5.csv";
        var expectedBucketName = "dev.uw1.linqpal-tabapay-reports";
        
        _s3ClientMock
            .Setup(x => x.IsFileExistInS3BucketAsync(
                expectedBucketName,
                expectedFileName,
                It.IsAny<S3ConnectionOptions>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(() => 
            _service.MissingTabapayReportFilesChecking(CancellationToken.None));

        Assert.Same(expectedException, exception);
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(
                It.Is<EventMessageBody>(m => 
                    m.EventLevel == EventLevel.Error &&
                    m.Message.Contains(expectedException.Message)),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [InlineData("dev", "dev.uw1.linqpal-tabapay-reports")]
    [InlineData("qa", "qa.uw1.linqpal-tabapay-reports")]
    [InlineData("prod", "prod.uw1.linqpal-tabapay-reports")]
    public async Task MissingTabapayReportFilesChecking_ShouldUseCorrectBucketName(string environment, string expectedBucketName)
    {
        // Arrange
        Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", environment);
        var expectedFileName = $"3471_{_currentDateOnly:yyyyMMdd}_transactions_v2-5.csv";
        
        var service = new MissingTabapayReportFilesService(
            _s3ClientMock.Object,
            _optionsMock.Object,
            _dateProviderMock.Object,
            _loggerMock.Object,
            _slackNotificationServiceMock.Object,
            _traceIdAccessorMock.Object);

        _s3ClientMock
            .Setup(x => x.IsFileExistInS3BucketAsync(
                expectedBucketName,
                expectedFileName,
                It.IsAny<S3ConnectionOptions>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await service.MissingTabapayReportFilesChecking(CancellationToken.None);

        // Assert
        _s3ClientMock.Verify(
            x => x.IsFileExistInS3BucketAsync(
                expectedBucketName,
                expectedFileName,
                It.IsAny<S3ConnectionOptions>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}