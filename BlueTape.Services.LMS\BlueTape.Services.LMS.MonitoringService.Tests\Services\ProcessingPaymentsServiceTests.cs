﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.MonitoringService.Configuration;
using BlueTape.Services.LMS.MonitoringService.Services;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace BlueTape.Services.LMS.MonitoringService.Tests.Services;

public class ProcessingPaymentsServiceTests
{
    private readonly Mock<IPaymentRepository> _paymentRepositoryMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IOptions<MonitoringServiceOptions>> _optionsMock;
    private readonly Mock<ILogger<ProcessingPaymentsService>> _loggerMock;
    private readonly Mock<ISlackNotificationService> _slackNotificationServiceMock;
    private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock;
    private readonly ProcessingPaymentsService _service;
    private readonly DateTime _currentDate;
    private readonly DateOnly _currentDateOnly;
    private readonly int _daysThreshold;

    public ProcessingPaymentsServiceTests()
    {
        _paymentRepositoryMock = new Mock<IPaymentRepository>();
        _dateProviderMock = new Mock<IDateProvider>();
        _optionsMock = new Mock<IOptions<MonitoringServiceOptions>>();
        _loggerMock = new Mock<ILogger<ProcessingPaymentsService>>();
        _slackNotificationServiceMock = new Mock<ISlackNotificationService>();
        _traceIdAccessorMock = new Mock<ITraceIdAccessor>();

        _currentDate = DateTime.UtcNow;
        _currentDateOnly = DateOnly.FromDateTime(_currentDate);
        _daysThreshold = 3;

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(_currentDate);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(_currentDateOnly);
        _optionsMock.Setup(x => x.Value).Returns(new MonitoringServiceOptions
        {
            ProcessingPaymentsDaysThreshold = _daysThreshold
        });
        _traceIdAccessorMock.Setup(x => x.TraceId).Returns("test-trace-id");

        _service = new ProcessingPaymentsService(
            _paymentRepositoryMock.Object,
            _dateProviderMock.Object,
            _optionsMock.Object,
            _loggerMock.Object,
            _slackNotificationServiceMock.Object,
            _traceIdAccessorMock.Object);
    }

    [Fact]
    public async Task ProcessingPaymentsChecking_WhenNoPaymentsFound_ShouldNotSendNotification()
    {
        // Arrange
        var thresholdDate = _currentDateOnly.AddDays(-_daysThreshold);
        _paymentRepositoryMock
            .Setup(x => x.GetProcessingPayments(thresholdDate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PaymentEntity>());

        // Act
        await _service.ProcessingPaymentsChecking(CancellationToken.None);

        // Assert
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(It.IsAny<EventMessageBody>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task ProcessingPaymentsChecking_WhenPaymentsFound_ShouldSendNotification()
    {
        // Arrange
        var thresholdDate = _currentDateOnly.AddDays(-_daysThreshold);
        var payment = new PaymentEntity
        {
            Id = Guid.NewGuid(),
            Amount = 100.0m,
            Status = PaymentStatus.Processing,
            Date = _currentDateOnly.AddDays(-(_daysThreshold + 1)),
            Loan = new LoanEntity
            {
                Id = Guid.NewGuid(),
                Status = LoanStatus.Started
            }
        };

        _paymentRepositoryMock
            .Setup(x => x.GetProcessingPayments(thresholdDate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PaymentEntity> { payment });

        // Act
        await _service.ProcessingPaymentsChecking(CancellationToken.None);

        // Assert
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(
                It.IsAny<EventMessageBody>(), 
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ProcessingPaymentsChecking_WhenSpecificPaymentIdProvided_ShouldOnlyCheckThatPayment()
    {
        // Arrange
        var paymentId = Guid.NewGuid();
        var payment = new PaymentEntity
        {
            Id = paymentId,
            Amount = 100.0m,
            Status = PaymentStatus.Processing,
            Date = _currentDateOnly.AddDays(-(_daysThreshold + 1)),
            Loan = new LoanEntity
            {
                Id = Guid.NewGuid(),
                Status = LoanStatus.Started
            }
        };

        _paymentRepositoryMock
            .Setup(x => x.GetByIdWithLoan(paymentId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(payment);

        // Act
        await _service.ProcessingPaymentsChecking(CancellationToken.None, paymentId);

        // Assert
        _paymentRepositoryMock.Verify(
            x => x.GetByIdWithLoan(paymentId, It.IsAny<CancellationToken>()),
            Times.Once);
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(
                It.IsAny<EventMessageBody>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ProcessingPaymentsChecking_WhenExceptionOccurs_ShouldSendErrorNotificationAndRethrow()
    {
        // Arrange
        var expectedException = new Exception("Test exception");
        var thresholdDate = _currentDateOnly.AddDays(-_daysThreshold);
        _paymentRepositoryMock
            .Setup(x => x.GetProcessingPayments(thresholdDate, It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(() =>
            _service.ProcessingPaymentsChecking(CancellationToken.None));

        Assert.Same(expectedException, exception);
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(
                It.Is<EventMessageBody>(m =>
                    m.EventLevel == EventLevel.Error &&
                    m.Message.Contains(expectedException.Message)),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}