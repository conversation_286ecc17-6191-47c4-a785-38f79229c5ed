﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.Services.LMS.MonitoringService.Configuration;
using BlueTape.Services.LMS.MonitoringService.Services;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Bson;
using Moq;
using Xunit;

namespace BlueTape.Services.LMS.MonitoringService.Tests.Services;

public class TabapayProcessingCardPaymentServiceTests
{
    private readonly Mock<ITransactionRepository> _transactionRepositoryMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IOptions<MonitoringServiceOptions>> _optionsMock;
    private readonly Mock<ILogger<TabapayProcessingCardPaymentService>> _loggerMock;
    private readonly Mock<ISlackNotificationService> _slackNotificationServiceMock;
    private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock;
    private readonly TabapayProcessingCardPaymentService _service;
    private readonly DateTime _currentDate;
    private readonly int _daysThreshold;

    public TabapayProcessingCardPaymentServiceTests()
    {
        _transactionRepositoryMock = new Mock<ITransactionRepository>();
        _dateProviderMock = new Mock<IDateProvider>();
        _optionsMock = new Mock<IOptions<MonitoringServiceOptions>>();
        _loggerMock = new Mock<ILogger<TabapayProcessingCardPaymentService>>();
        _slackNotificationServiceMock = new Mock<ISlackNotificationService>();
        _traceIdAccessorMock = new Mock<ITraceIdAccessor>();

        _currentDate = DateTime.UtcNow;
        _daysThreshold = 3;

        _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(_currentDate);
        _optionsMock.Setup(x => x.Value).Returns(new MonitoringServiceOptions
        {
            TabapayProcessingCardPaymentDaysThreshold = _daysThreshold
        });
        _traceIdAccessorMock.Setup(x => x.TraceId).Returns("test-trace-id");

        _service = new TabapayProcessingCardPaymentService(
            _transactionRepositoryMock.Object,
            _dateProviderMock.Object,
            _optionsMock.Object,
            _loggerMock.Object,
            _slackNotificationServiceMock.Object,
            _traceIdAccessorMock.Object);
    }

    [Fact]
    public async Task TabapayProcessingCardPaymentChecking_WhenNoTransactionsFound_ShouldNotSendNotification()
    {
        // Arrange
        _transactionRepositoryMock
            .Setup(x => x.GetCardTransactionsWithProcessingOperations(
                It.IsAny<DateTime>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<TransactionDocument>());

        // Act
        await _service.TabapayProcessingCardPaymentChecking(CancellationToken.None);

        // Assert
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(It.IsAny<EventMessageBody>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task TabapayProcessingCardPaymentChecking_WhenTransactionsFound_ShouldSendNotification()
    {
        // Arrange
        var transaction = new TransactionDocument
        {
            Id = ObjectId.GenerateNewId().ToString(),
            Status = "SUCCESS",
            PaymentMethod = "card",
            Amount = 100.0m,
            PayerId = "test-payer-id",
            Date = _currentDate.AddDays(-(_daysThreshold + 1)),
            Operation = new OperationDocument
            {
                Id = ObjectId.GenerateNewId().ToString(),
                Status = "PROCESSING",
                Amount = 100.0m,
                Date = _currentDate.AddDays(-(_daysThreshold + 1))
            }
        };

        _transactionRepositoryMock
            .Setup(x => x.GetCardTransactionsWithProcessingOperations(
                It.IsAny<DateTime>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<TransactionDocument> { transaction });

        // Act
        await _service.TabapayProcessingCardPaymentChecking(CancellationToken.None);

        // Assert
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(
                It.IsAny<EventMessageBody>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task TabapayProcessingCardPaymentChecking_WhenSpecificTransactionIdProvided_ShouldOnlyCheckThatTransaction()
    {
        // Arrange
        var transactionId = ObjectId.GenerateNewId().ToString();
        var transaction = new TransactionDocument
        {
            Id = transactionId,
            Status = "SUCCESS",
            PaymentMethod = "card",
            Amount = 100.0m,
            PayerId = "test-payer-id",
            Date = _currentDate.AddDays(-(_daysThreshold + 1)),
            Operation = new OperationDocument
            {
                Id = ObjectId.GenerateNewId().ToString(),
                Status = "PROCESSING",
                Amount = 100.0m,
                Date = _currentDate.AddDays(-(_daysThreshold + 1))
            }
        };

        _transactionRepositoryMock
            .Setup(x => x.GetCardTransactionWithProcessingOperationByTransactionId(
                transactionId,
                It.IsAny<DateTime>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(transaction);

        // Act
        await _service.TabapayProcessingCardPaymentChecking(CancellationToken.None, transactionId);

        // Assert
        _transactionRepositoryMock.Verify(
            x => x.GetCardTransactionWithProcessingOperationByTransactionId(
                transactionId,
                It.IsAny<DateTime>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(
                It.IsAny<EventMessageBody>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task TabapayProcessingCardPaymentChecking_WhenExceptionOccurs_ShouldSendErrorNotificationAndRethrow()
    {
        // Arrange
        var expectedException = new Exception("Test exception");
        _transactionRepositoryMock
            .Setup(x => x.GetCardTransactionsWithProcessingOperations(
                It.IsAny<DateTime>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(() =>
            _service.TabapayProcessingCardPaymentChecking(CancellationToken.None));

        Assert.Same(expectedException, exception);
        _slackNotificationServiceMock.Verify(
            x => x.NotifyFromMonitoring(
                It.Is<EventMessageBody>(m =>
                    m.EventLevel == EventLevel.Error &&
                    m.Message.Contains(expectedException.Message)),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}