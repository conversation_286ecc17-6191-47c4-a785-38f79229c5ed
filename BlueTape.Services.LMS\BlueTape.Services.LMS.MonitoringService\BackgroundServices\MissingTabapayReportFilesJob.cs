﻿using BlueTape.Services.LMS.MonitoringService.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;

namespace BlueTape.Services.LMS.MonitoringService.BackgroundServices
{
    [DisallowConcurrentExecution]
    public class MissingTabapayReportFilesJob : IJob
    {
        private const string MessageTemplate = $"Execution of {nameof(MissingTabapayReportFilesJob)}";

        private readonly ILogger<MissingTabapayReportFilesJob> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public MissingTabapayReportFilesJob(ILogger<MissingTabapayReportFilesJob> logger, IServiceScopeFactory serviceScopeFactory)
        {
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            _logger.LogInformation("{template} started", MessageTemplate);

            try
            {
                await <PERSON>le();
            }
            catch (Exception e)
            {
                _logger.LogCritical(e, "{template} failed with an error", MessageTemplate);
            }

            _logger.LogInformation("{template} successfully finished.", MessageTemplate);
        }

        private async Task Handle()
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var missingTabapayReportFilesService = scope.ServiceProvider.GetRequiredService<IMissingTabapayReportFilesService>();
            await missingTabapayReportFilesService.MissingTabapayReportFilesChecking(CancellationToken.None);
        }
    }
}
