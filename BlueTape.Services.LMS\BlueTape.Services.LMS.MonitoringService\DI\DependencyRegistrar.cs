﻿using BlueTape.Services.LMS.MonitoringService.Abstractions;
using BlueTape.Services.LMS.MonitoringService.BackgroundServices;
using BlueTape.Services.LMS.MonitoringService.Configuration;
using BlueTape.Services.LMS.MonitoringService.Extensions;
using BlueTape.Services.LMS.MonitoringService.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Quartz;

namespace BlueTape.Services.LMS.MonitoringService.DI
{
    public static class DependencyRegistrar
    {
        public static void AddMonitoringServiceDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddQuartz(q =>
            {
                q.AddJobAndTrigger<ProcessingPaymentsForALongTimeJob>(configuration);
                q.AddJobAndTrigger<TabapayProcessingCardPaymentForALongTimeJob>(configuration);
                q.AddJobAndTrigger<MissingTabapayReportFilesJob>(configuration);
            });

            services.AddTransient<IProcessingPaymentsService, ProcessingPaymentsService>();
            services.AddTransient<ITabapayProcessingCardPaymentService, TabapayProcessingCardPaymentService>();
            services.AddTransient<IMissingTabapayReportFilesService, MissingTabapayReportFilesService>();
            services.Configure<MonitoringServiceOptions>(configuration.GetSection(nameof(MonitoringServiceOptions)));
        }
    }
}
