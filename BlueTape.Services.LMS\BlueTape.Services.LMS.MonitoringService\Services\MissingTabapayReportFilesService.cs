﻿using Amazon;
using BlueTape.AWSS3;
using BlueTape.AWSS3.Abstractions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.LMS.Infrastructure;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Services.LMS.MonitoringService.Abstractions;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.Services.LMS.MonitoringService.Services
{
    public class MissingTabapayReportFilesService : IMissingTabapayReportFilesService
    {
        private readonly IDynamicS3Client _dynamicS3Client;
        private readonly IOptions<S3ConfigurationOptions> _options;
        private readonly IDateProvider _dateProvider;
        private readonly ILogger<MissingTabapayReportFilesService> _logger;
        private readonly ISlackNotificationService _slackNotificationService;
        private readonly ITraceIdAccessor _traceIdAccessor;

        public MissingTabapayReportFilesService(IDynamicS3Client customS3Client,
            IOptions<S3ConfigurationOptions> options,
            IDateProvider dateProvider,
            ILogger<MissingTabapayReportFilesService> logger,
            ISlackNotificationService slackNotificationService,
            ITraceIdAccessor traceIdAccessor)
        {
            _dynamicS3Client = customS3Client;
            _dateProvider = dateProvider;
            _options = options;
            _logger = logger;
            _slackNotificationService = slackNotificationService;
            _traceIdAccessor = traceIdAccessor;
        }

        public async Task MissingTabapayReportFilesChecking(CancellationToken ct, DateOnly? date = null)
        {
            try
            {
                _logger.LogInformation("Start check for missing Tabapay report files");

                await PerformMissingTabapayReportFilesCheck(ct, date);

                _logger.LogInformation("Missing Tabapay report files check completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while checking for missing Tabapay report files");

                var message = CreateEventMessage(
                   $"Error in MissingTabapayReportFilesChecking: {ex.Message}\n{ex.StackTrace}",
                   EventLevel.Error);

                try
                {
                    await _slackNotificationService.NotifyFromMonitoring(
                        message,
                        _traceIdAccessor.TraceId,
                        ct);
                }
                catch (Exception notifyEx)
                {
                    _logger.LogError(notifyEx, "Failed to send Slack notification for missing Tabapay report files check error");
                }

                throw;
            }
        }

        private async Task PerformMissingTabapayReportFilesCheck(CancellationToken ct, DateOnly? date = null)
        {
            var bucketName = GetBucketName();
            var currentDate = date is null ? _dateProvider.CurrentDate : date;
            var fileName = $"3471_{currentDate:yyyyMMdd}_transactions_v2-5.csv";
            var region = _options.Value.BlueTapeDefaultAwsRegion;
            var s3ConnectionOptions = new S3ConnectionOptions
            {
                RegionEndpoint = RegionEndpoint.GetBySystemName(region)
            };

            var isFileExist = await _dynamicS3Client.IsFileExistInS3BucketAsync(bucketName, fileName, s3ConnectionOptions, ct);

            if (isFileExist)
            {
                _logger.LogInformation("Tabapay report file {FileName} exists in bucket {BucketName}", fileName, bucketName);
                return;
            }

            _logger.LogWarning("Tabapay report file {FileName} is missing in bucket {BucketName}", fileName, bucketName);

            try
            {
                var message = CreateEventMessage(
                    $"Tabapay report file {fileName} is missing in bucket {bucketName} at {currentDate}",
                    EventLevel.Warning);

                await _slackNotificationService.NotifyFromMonitoring(
                  message,
                  _traceIdAccessor.TraceId,
                  CancellationToken.None);

                _logger.LogInformation("Notification sent for missing Tabapay report file {FileName}", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification for missing Tabapay report file {FileName}", fileName);
                throw;
            }
        }

        private EventMessageBody CreateEventMessage(string message, EventLevel eventLevel)
        {
            return new()
            {
                Message = message,
                EventLevel = eventLevel,
                EventName = "MissingTabapayReportFilesChecking",
                EventSource = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                ServiceName = "LMS",
                TimeStamp = _dateProvider.CurrentDateTime.ToString()
            };
        }

        private string GetBucketName()
        {
            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
            return $"{env}.uw1.linqpal-tabapay-reports";
        }
    }
}
