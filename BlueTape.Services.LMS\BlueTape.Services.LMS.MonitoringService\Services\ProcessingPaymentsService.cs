﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.MonitoringService.Abstractions;
using BlueTape.Services.LMS.MonitoringService.Configuration;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Constants;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.Services.LMS.MonitoringService.Services
{
    public class ProcessingPaymentsService : IProcessingPaymentsService
    {
        private readonly IPaymentRepository _paymentRepository;
        private readonly IDateProvider _dateProvider;
        private readonly IOptions<MonitoringServiceOptions> _options;
        private readonly ILogger<ProcessingPaymentsService> _logger;
        private readonly ISlackNotificationService _slackNotificationService;
        private readonly ITraceIdAccessor _traceIdAccessor;

        public ProcessingPaymentsService(IPaymentRepository paymentRepository,
            IDateProvider dateProvider,
            IOptions<MonitoringServiceOptions> options,
            ILogger<ProcessingPaymentsService> logger,
            ISlackNotificationService slackNotificationService,
            ITraceIdAccessor traceIdAccessor)
        {
            _paymentRepository = paymentRepository;
            _dateProvider = dateProvider;
            _options = options;
            _logger = logger;
            _slackNotificationService = slackNotificationService;
            _traceIdAccessor = traceIdAccessor;
        }

        public async Task ProcessingPaymentsChecking(CancellationToken ct, Guid? paymentId = null)
        {
            try
            {
                _logger.LogInformation("Start processing payments check");

                await PerformProcessingPaymentsCheck(ct, paymentId);

                _logger.LogInformation("Processing payments check completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing payments check");

                var message = CreateEventMessage(
                   $"Error in ProcessingPaymentsChecking: {ex.Message}\n{ex.StackTrace}",
                   EventLevel.Error);

                try
                {
                    await _slackNotificationService.NotifyFromMonitoring(
                        message,
                        _traceIdAccessor.TraceId,
                        ct);
                }
                catch (Exception notifyEx)
                {
                    _logger.LogError(notifyEx, "Failed to send Slack notification about processing payments check error");
                }

                throw;
            }
        }

        private async Task PerformProcessingPaymentsCheck(CancellationToken ct, Guid? paymentId = null)
        {
            var processingPayments = await GetProcessingPayments(paymentId, ct);

            if (processingPayments.Any())
            {
                _logger.LogInformation("Found {Count} processing payments older than {DaysThreshold} days",
                    processingPayments.Count, _options.Value.ProcessingPaymentsDaysThreshold);
                foreach (var payment in processingPayments)
                {
                    try
                    {
                        var message = CreatePaymentProcessingMessage(payment);

                        await _slackNotificationService.NotifyFromMonitoring(
                          message,
                          _traceIdAccessor.TraceId,
                          CancellationToken.None);

                        _logger.LogInformation("Sent notification for payment {paymentId}", payment.Id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send notification for payment {paymentId}, but continuing with others", payment.Id);
                    }
                }
            }
            else
            {
                _logger.LogInformation("No processing payments found older than {DaysThreshold} days", _options.Value.ProcessingPaymentsDaysThreshold);
            }
        }

        private EventMessageBody CreateEventMessage(string message, EventLevel eventLevel)
        {
            return new()
            {
                Message = message,
                EventLevel = eventLevel,
                EventName = "ProcessingPaymentsChecking",
                EventSource = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                ServiceName = "LMS",
                TimeStamp = _dateProvider.CurrentDateTime.ToString()
            };
        }

        private async Task<List<PaymentEntity>> GetProcessingPayments(Guid? paymentId, CancellationToken ct)
        {
            var thresholdDate = _dateProvider.CurrentDate.AddDays(-_options.Value.ProcessingPaymentsDaysThreshold);

            List<PaymentEntity> payments;
            if (paymentId == null)
            {
                payments = await _paymentRepository.GetProcessingPayments(thresholdDate, ct);
            }
            else
            {
                var payment = await _paymentRepository.GetByIdWithLoan(paymentId.GetValueOrDefault(), ct);
                payments = payment is not null ? [payment] : [];
            }

            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
            if (env is EnvironmentConstants.Beta or EnvironmentConstants.Qa or EnvironmentConstants.Dev)
                return payments.Take(20).ToList();

            return payments;
        }

        private EventMessageBody CreatePaymentProcessingMessage(PaymentEntity payment)
        {
            var loan = payment.Loan;
            var message = $"Payment with PaymentId {payment.Id}, LoanId {loan?.Id}, CompanyName {loan?.CompanyName}, CompanyId {loan?.CompanyId}, " +
                         $"PaymentDate {payment.Date}, PaymentStatus {payment.Status}, PaymentAmount {payment.Amount} " +
                         $"has processing status more than {_options.Value.ProcessingPaymentsDaysThreshold} days";

            return CreateEventMessage(message, EventLevel.Warning);
        }
    }
}
