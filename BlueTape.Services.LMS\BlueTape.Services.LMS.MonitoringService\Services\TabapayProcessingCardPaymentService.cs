﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.Services.LMS.MonitoringService.Abstractions;
using BlueTape.Services.LMS.MonitoringService.Configuration;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Constants;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.Services.LMS.MonitoringService.Services
{
    public class TabapayProcessingCardPaymentService : ITabapayProcessingCardPaymentService
    {
        private readonly ITransactionRepository _transactionRepository;
        private readonly IDateProvider _dateProvider;
        private readonly IOptions<MonitoringServiceOptions> _options;
        private readonly ILogger<TabapayProcessingCardPaymentService> _logger;
        private readonly ISlackNotificationService _slackNotificationService;
        private readonly ITraceIdAccessor _traceIdAccessor;

        public TabapayProcessingCardPaymentService(ITransactionRepository transactionRepository,
            IDateProvider dateProvider,
            IOptions<MonitoringServiceOptions> options,
            ILogger<TabapayProcessingCardPaymentService> logger,
            ISlackNotificationService slackNotificationService,
            ITraceIdAccessor traceIdAccessor)
        {
            _transactionRepository = transactionRepository;
            _dateProvider = dateProvider;
            _options = options;
            _logger = logger;
            _slackNotificationService = slackNotificationService;
            _traceIdAccessor = traceIdAccessor;
        }

        public async Task TabapayProcessingCardPaymentChecking(CancellationToken ct, string? transactionId = null)
        {
            try
            {
                _logger.LogInformation("Start processing payments check for Tabapay card payments");

                await PerformTabapayProcessingCardPaymentCheck(ct, transactionId);

                _logger.LogInformation("Tabapay processing card payments check completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing Tabapay card payments check");

                var message = CreateEventMessage(
                   $"Error in TabapayProcessingCardPaymentChecking: {ex.Message}\n{ex.StackTrace}",
                   EventLevel.Error);

                try
                {
                    await _slackNotificationService.NotifyFromMonitoring(
                        message,
                        _traceIdAccessor.TraceId,
                        ct);
                }
                catch (Exception notifyEx)
                {
                    _logger.LogError(notifyEx, "Failed to send notification about error in TabapayProcessingCardPaymentChecking");
                }

                throw;
            }
        }

        private async Task PerformTabapayProcessingCardPaymentCheck(CancellationToken ct, string? transactionId = null)
        {
            var transactionsWithOperations = await GetCardTransactionsWithProcessingOperations(transactionId, ct);

            if (transactionsWithOperations.Any())
            {
                _logger.LogInformation("Found {Count} Tabapay processing card payments older than {DaysThreshold} days",
                    transactionsWithOperations.Count, _options.Value.TabapayProcessingCardPaymentDaysThreshold);
                foreach (var transaction in transactionsWithOperations)
                {
                    try
                    {
                        var message = CreateOperationProcessingMessage(transaction);

                        await _slackNotificationService.NotifyFromMonitoring(
                          message,
                          _traceIdAccessor.TraceId,
                          CancellationToken.None);

                        _logger.LogInformation("Sent notification for transaction {transactionId}", transaction.Id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send notification for transaction {transactionId}, but continuing with others", transaction.Id);
                    }
                }
            }
            else
            {
                _logger.LogInformation("No Tabapay processing card payments found older than {DaysThreshold} days",
                    _options.Value.TabapayProcessingCardPaymentDaysThreshold);
            }
        }

        private EventMessageBody CreateEventMessage(string message, EventLevel eventLevel)
        {
            return new()
            {
                Message = message,
                EventLevel = eventLevel,
                EventName = "TabapayProcessingCardPaymentChecking",
                EventSource = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                ServiceName = "LMS",
                TimeStamp = _dateProvider.CurrentDateTime.ToString()
            };
        }

        private async Task<List<TransactionDocument>> GetCardTransactionsWithProcessingOperations(string? transactionId, CancellationToken ct)
        {
            var thresholdDate = _dateProvider.CurrentDateTime.AddDays(-_options.Value.TabapayProcessingCardPaymentDaysThreshold);

            List<TransactionDocument> transactions;
            if (transactionId == null)
            {
                transactions = await _transactionRepository.GetCardTransactionsWithProcessingOperations(thresholdDate, ct);
            }
            else
            {
                var transaction = await _transactionRepository.GetCardTransactionWithProcessingOperationByTransactionId(transactionId, thresholdDate, ct);
                transactions = transaction is not null ? [transaction] : [];
            }

            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
            if (env is EnvironmentConstants.Beta or EnvironmentConstants.Qa or EnvironmentConstants.Dev)
                return transactions.Take(20).ToList();

            return transactions;
        }

        private EventMessageBody CreateOperationProcessingMessage(TransactionDocument transaction)
        {
            var operation = transaction.Operation;
            var message = $"TabapayProcessingCardPaymentService: Operation with OperationId {operation?.Id}, OperationType {operation?.Type}, OperationDate {operation?.Date}, " +
                $"PaymentMethod {transaction.PaymentMethod}, PayerId {transaction.PayerId}, OwnerId {operation?.OwnerId} has processing status more than {_options.Value.TabapayProcessingCardPaymentDaysThreshold} days";

            return CreateEventMessage(message, EventLevel.Warning);
        }
    }
}
