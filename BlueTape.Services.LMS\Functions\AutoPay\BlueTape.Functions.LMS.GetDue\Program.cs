using BlueTape.Services.LMS.Infrastructure;
using BlueTape.Services.LMS.Infrastructure.Hosting.Extensions;
using Microsoft.Extensions.Hosting;

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureBlueTapeServicesForFunction()
    .ConfigureBlueTapeHostConfigurationForFunction()
    .ConfigureBlueTapeSerilog(LoggerConstants.GetDueFunctionName)
    .Build();

await host.RunAsync();