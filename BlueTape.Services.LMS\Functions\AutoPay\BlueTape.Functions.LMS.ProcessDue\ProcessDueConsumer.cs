using Azure.Messaging.ServiceBus;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy.Abstractions;
using BlueTape.Services.LMS.Infrastructure;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Functions.LMS.ProcessDue;

public class ProcessDueConsumer(IServiceScopeFactory serviceScopeFactory)
{
    [Function(nameof(ProcessDueConsumer))]
    public async Task Run([ServiceBusTrigger($"%{InfrastructureConstants.ProcessDueQueueName}%",
            Connection = $"{InfrastructureConstants.ProcessDueQueueConnectionString}")]
        ServiceBusReceivedMessage message,
        CancellationToken ct)
    {
        await using var scope = serviceScopeFactory.CreateAsyncScope();
        var body = message.Body.ToObjectFromJson<DueLoanMessage>()!;
        var processDueStrategy =
            scope.ServiceProvider.GetRequiredService<IEnumerable<IProcessDueStrategy>>()
                .First(x => x.IsApplicable(body.ProductType));

        await processDueStrategy.ProcessDue(body, ct);
    }
}
