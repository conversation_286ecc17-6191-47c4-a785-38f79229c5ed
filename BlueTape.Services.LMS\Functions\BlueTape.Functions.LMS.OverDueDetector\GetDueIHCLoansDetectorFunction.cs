﻿using BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy.Abstractions;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Functions.LMS.AutoPay;

public class GetDueIHCLoansDetectorFunction(IServiceScopeFactory serviceScopeFactory)
{
    [Function("GetDueIHCLoansDetectorFunction")]
    public async Task Run([TimerTrigger("0 30 13 * * ?")] TimerInfo myTimer)
    {
        await using var scope = serviceScopeFactory.CreateAsyncScope();

        var getDueStrategies =
            scope.ServiceProvider.GetRequiredService<IEnumerable<IGetDueStrategy>>();

        var stategy = getDueStrategies.FirstOrDefault(x => x.IsApplicable(ProductType.InHouseCredit));
           
        if(stategy is null) throw new VariableNullException("No strategy found");
        await stategy.GetDueLoans(CancellationToken.None);
    }
}